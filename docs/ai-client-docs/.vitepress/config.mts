import { defineConfig } from 'vitepress'

// https://vitepress.dev/reference/site-config
export default defineConfig({
  "title": "BetterYeah AI",
  "description": " ",
  "head": [
    [
      "link",
      {
        "rel": "icon",
        "href": "https://resource.bantouyan.com/battleyeah-ai/ai-client/logo-square.svg"
      }
    ]
  ],
  "srcDir": "docs",
  "outDir": "./dist",
  "themeConfig": {
    "logoLink": {
      "link": "https://www.betteryeah.com"
    },
    "logo": "https://resource.bantouyan.com/battleyeah-ai/ai-client/logo-square.svg",
    "nav": [],
    "search": {
      "provider": "local",
      "options": {
        "translations": {
          "button": {
            "buttonText": "搜索文档",
            "buttonAriaLabel": "搜索文档"
          },
          "modal": {
            "footer": {
              "selectText": "选择",
              "navigateText": "切换",
              "closeText": "关闭"
            }
          }
        }
      }
    },
    "docFooter": {
      "prev": "上一页",
      "next": "下一页"
    },
    "outline": {
      "level": "deep",
      "label": "页面导航"
    },
    "lastUpdated": {
      "text": "最后更新于",
      "formatOptions": {
        "dateStyle": "short",
        "timeStyle": "medium"
      }
    },
    "langMenuLabel": "多语言",
    "returnToTopLabel": "回到顶部",
    "sidebarMenuLabel": "菜单",
    "darkModeSwitchLabel": "主题",
    "lightModeSwitchTitle": "切换到浅色模式",
    "darkModeSwitchTitle": "切换到深色模式",
    "sidebar": [
      {
        "text": "介绍",
        "collapsed": false,
        "items": [
          {
            "text": "欢迎使用BetterYeah AI",
            "link": "/介绍/欢迎使用BetterYeah AI.md"
          },
          {
            "text": "BetterYeah AI的使用场景",
            "link": "/介绍/BetterYeah AI的使用场景.md"
          }
        ]
      },
      {
        "text": "搭建Agent",
        "collapsed": false,
        "items": [
          {
            "text": "什么是Agent",
            "link": "/搭建Agent/什么是Agent.md"
          },
          {
            "text": "搭建第一个Agent",
            "link": "/搭建Agent/搭建第一个Agent.md"
          },
          {
            "text": "角色设定",
            "link": "/搭建Agent/角色设定.md"
          },
          {
            "text": "变量",
            "link": "/搭建Agent/变量.md"
          },
          {
            "text": "技能",
            "link": "/搭建Agent/技能.md"
          },
          {
            "text": "任务",
            "link": "/搭建Agent/任务.md"
          },
          {
            "text": "知识",
            "link": "/搭建Agent/知识.md"
          },
          {
            "text": "搭建 文档QA助手",
            "link": "/搭建Agent/搭建 文档QA助手.md"
          },
          {
            "text": "搭建 数据分析助手",
            "link": "/搭建Agent/搭建 数据分析助手.md"
          },
          {
            "text": "搭建 文章分析助手",
            "link": "/搭建Agent/搭建 文章分析助手.md"
          }
        ]
      },
      {
        "text": "发布Agent",
        "collapsed": false,
        "items": [
          {
            "text": "API调用Agent",
            "collapsed": false,
            "items": [
              {
                "text": "API概览",
                "link": "/发布Agent/API调用Agent/API概览.md"
              },
              {
                "text": "创建新会话",
                "link": "/发布Agent/API调用Agent/创建新会话.md"
              },
              {
                "text": "发送会话消息（聊天）",
                "link": "/发布Agent/API调用Agent/发送会话消息（聊天）.md"
              },
              {
                "text": "获取会话列表",
                "link": "/发布Agent/API调用Agent/获取会话列表.md"
              },
              {
                "text": "获取会话历史消息",
                "link": "/发布Agent/API调用Agent/获取会话历史消息.md"
              },
              {
                "text": "推送Assistant消息",
                "link": "/发布Agent/API调用Agent/推送Assistant消息.md"
              },
              {
                "text": "获取请求相关信息",
                "link": "/发布Agent/API调用Agent/获取请求相关信息.md"
              }
            ]
          },
          {
            "text": "SDK嵌入Agent",
            "link": "/发布Agent/SDK嵌入Agent.md"
          },
          {
            "text": "在钉钉机器人中使用Agent",
            "link": "/发布Agent/在钉钉机器人中使用Agent.md"
          }
        ]
      },
      {
        "text": "插件",
        "collapsed": false,
        "items": [
          {
            "text": "什么是插件",
            "link": "/插件/什么是插件.md"
          },
          {
            "text": "创建自定义插件",
            "link": "/插件/创建自定义插件.md"
          },
          {
            "text": "在Agent中使用插件",
            "link": "/插件/在Agent中使用插件.md"
          },
          {
            "text": "在Flow中使用插件",
            "link": "/插件/在Flow中使用插件.md"
          }
        ]
      },
      {
        "text": "Flow",
        "collapsed": false,
        "items": [
          {
            "text": "什么是Flow",
            "link": "/Flow/什么是Flow.md"
          },
          {
            "text": "搭建第一个Flow",
            "link": "/Flow/搭建第一个Flow.md"
          },
          {
            "text": "节点介绍与配置",
            "collapsed": false,
            "items": [
              {
                "text": "开始与输出节点",
                "link": "/Flow/节点介绍与配置/开始与输出节点.md"
              },
              {
                "text": "LLM节点",
                "link": "/Flow/节点介绍与配置/LLM节点.md"
              },
              {
                "text": "知识库节点",
                "link": "/Flow/节点介绍与配置/知识库节点.md"
              },
              {
                "text": "JavaScript&Python节点",
                "link": "/Flow/节点介绍与配置/JavaScript&Python节点.md"
              },
              {
                "text": "API节点",
                "link": "/Flow/节点介绍与配置/API节点.md"
              },
              {
                "text": "Flow节点",
                "link": "/Flow/节点介绍与配置/Flow节点.md"
              },
              {
                "text": "逻辑分支节点",
                "link": "/Flow/节点介绍与配置/逻辑分支节点.md"
              },
              {
                "text": "Plugin节点",
                "collapsed": false,
                "items": [
                  {
                    "text": "AI图生视频",
                    "link": "/Flow/节点介绍与配置/Plugin节点/AI图生视频.md"
                  }
                ]
              }
            ]
          },
          {
            "text": "模板",
            "collapsed": false,
            "items": [
              {
                "text": "什么是模板",
                "link": "/Flow/模板/什么是模板.md"
              },
              {
                "text": "【自决策AI检索】模板",
                "link": "/Flow/模板/【自决策AI检索】模板.md"
              },
              {
                "text": "【音频整理】模板",
                "link": "/Flow/模板/【音频整理】模板.md"
              },
              {
                "text": "【自分段AI检索】模板",
                "link": "/Flow/模板/【自分段AI检索】模板.md"
              },
              {
                "text": "【意图识别增强】模板",
                "link": "/Flow/模板/【意图识别增强】模板.md"
              }
            ]
          },
          {
            "text": "Flow中的变量和数据流转",
            "link": "/Flow/Flow中的变量和数据流转.md"
          },
          {
            "text": "Flow的调试与日志",
            "link": "/Flow/Flow的调试与日志.md"
          },
          {
            "text": "Flow的运行与集成",
            "link": "/Flow/Flow的运行与集成.md"
          },
          {
            "text": "在Flow里传递变量",
            "link": "/Flow/在Flow里传递变量.md"
          }
        ]
      },
      {
        "text": "知识库",
        "collapsed": false,
        "items": [
          {
            "text": "什么是知识库",
            "link": "/知识库/什么是知识库.md"
          },
          {
            "text": "文档知识上传",
            "link": "/知识库/文档知识上传.md"
          },
          {
            "text": "问答知识上传",
            "link": "/知识库/问答知识上传.md"
          },
          {
            "text": "多模态知识上传",
            "link": "/知识库/多模态知识上传.md"
          },
          {
            "text": "文档知识处理",
            "link": "/知识库/文档知识处理.md"
          },
          {
            "text": "近义词",
            "link": "/知识库/近义词.md"
          },
          {
            "text": "命中测试",
            "link": "/知识库/命中测试.md"
          },
          {
            "text": "使用知识库",
            "link": "/知识库/使用知识库.md"
          },
          {
            "text": "知识查询方式",
            "link": "/知识库/知识查询方式.md"
          },
          {
            "text": "知识库节点-仅查询指定文件",
            "link": "/知识库/知识库节点-仅查询指定文件.md"
          }
        ]
      },
      {
        "text": "数据库",
        "collapsed": false,
        "items": [
          {
            "text": "什么是数据库",
            "link": "/数据库/什么是数据库.md"
          },
          {
            "text": "如何创建一个数据库",
            "link": "/数据库/如何创建一个数据库.md"
          },
          {
            "text": "调用数据库",
            "link": "/数据库/调用数据库.md"
          }
        ]
      },
      {
        "text": "MCP服务",
        "collapsed": false,
        "items": [
          {
            "text": "什么是MCP服务",
            "link": "/MCP服务/什么是MCP服务.md"
          },
          {
            "text": "创建MCP服务",
            "link": "/MCP服务/创建MCP服务.md"
          },
          {
            "text": "使用MCP服务",
            "link": "/MCP服务/使用MCP服务.md"
          }
        ]
      },
      {
        "text": "使用案例",
        "collapsed": false,
        "items": [
          {
            "text": "钉钉机器人集成Flow",
            "link": "/使用案例/钉钉机器人集成Flow.md"
          }
        ]
      },
      {
        "text": "协议",
        "collapsed": false,
        "items": [
          {
            "text": "服务协议 ",
            "link": "/协议/服务协议 .md"
          },
          {
            "text": "隐私协议",
            "link": "/协议/隐私协议.md"
          }
        ]
      },
      {
        "text": "更新日志",
        "collapsed": false,
        "items": [
          {
            "text": "2025-06-19 更新日志",
            "link": "/更新日志/2025-06-19 更新日志.md"
          },
          {
            "text": "2025-05-29 更新日志",
            "link": "/更新日志/2025-05-29 更新日志.md"
          },
          {
            "text": "2025-04-30 更新日志",
            "link": "/更新日志/2025-04-30 更新日志.md"
          },
          {
            "text": "2025-04-17 更新日志",
            "link": "/更新日志/2025-04-17 更新日志.md"
          },
          {
            "text": "2025-04-03 更新日志",
            "link": "/更新日志/2025-04-03 更新日志.md"
          },
          {
            "text": "2025-03-20 更新日志",
            "link": "/更新日志/2025-03-20 更新日志.md"
          },
          {
            "text": "2025-03-06 更新日志",
            "link": "/更新日志/2025-03-06 更新日志.md"
          },
          {
            "text": "2025-02-20 更新日志",
            "link": "/更新日志/2025-02-20 更新日志.md"
          },
          {
            "text": "2025-01-09 更新日志",
            "link": "/更新日志/2025-01-09 更新日志.md"
          },
          {
            "text": "2024-12-26 更新日志",
            "link": "/更新日志/2024-12-26 更新日志.md"
          },
          {
            "text": "2024-12-12 更新日志",
            "link": "/更新日志/2024-12-12 更新日志.md"
          },
          {
            "text": "2024-11-28 更新日志",
            "link": "/更新日志/2024-11-28 更新日志.md"
          },
          {
            "text": "2024-11-14 更新日志",
            "link": "/更新日志/2024-11-14 更新日志.md"
          },
          {
            "text": "2024-10-31 更新日志",
            "link": "/更新日志/2024-10-31 更新日志.md"
          },
          {
            "text": "2024-10-17 更新日志",
            "link": "/更新日志/2024-10-17 更新日志.md"
          },
          {
            "text": "2024-09-26 更新日志",
            "link": "/更新日志/2024-09-26 更新日志.md"
          },
          {
            "text": "2024-09-12 更新日志",
            "link": "/更新日志/2024-09-12 更新日志.md"
          },
          {
            "text": "2024-08-29 更新日志",
            "link": "/更新日志/2024-08-29 更新日志.md"
          },
          {
            "text": "2024-08-15 更新日志",
            "link": "/更新日志/2024-08-15 更新日志.md"
          },
          {
            "text": "2024-08-01 更新日志",
            "link": "/更新日志/2024-08-01 更新日志.md"
          },
          {
            "text": "2024-07-11 更新日志",
            "link": "/更新日志/2024-07-11 更新日志.md"
          },
          {
            "text": "2024-06-27 更新日志",
            "link": "/更新日志/2024-06-27 更新日志.md"
          },
          {
            "text": "2024-06-13 更新日志",
            "link": "/更新日志/2024-06-13 更新日志.md"
          },
          {
            "text": "2024-05-30 更新日志",
            "link": "/更新日志/2024-05-30 更新日志.md"
          },
          {
            "text": "2024-05-16 更新日志",
            "link": "/更新日志/2024-05-16 更新日志.md"
          },
          {
            "text": "2024-04-30 更新日志",
            "link": "/更新日志/2024-04-30 更新日志.md"
          },
          {
            "text": "2024-04-19 更新日志",
            "link": "/更新日志/2024-04-19 更新日志.md"
          }
        ]
      }
    ]
  }
})