---
title: 在Flow里传递变量
urlname: Sx0ZdGa1zo7NAPxTPCic4aaMnKg
date: 2025-06-04 10:42:49
updated: 2025-06-17 11:37:54
---
# 在Flow里传递变量

在flow中传递变量可以让整个工作流更加灵活高效。相比复杂的文字信息，AI更喜欢处理直接的变量，可以智能地调整执行流程。

以“文章阅读器”的工作流为例（搭建过程详见[搭建 文章分析助手](/%E6%90%AD%E5%BB%BAAgent%2F%E6%90%AD%E5%BB%BA%20%E6%96%87%E7%AB%A0%E5%88%86%E6%9E%90%E5%8A%A9%E6%89%8B.html)的第三步）

工作流各部分功能简单描述为：

开始  ->  llm_1（提取链接和用户问题）-> 网页解析 -> llm_2（内容总结）-> 输出

（1）调用 已知变量名
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/X5UOb47EdoCVclx7XbFcjJQJnFh.png)
在llm_1的提示词里，想要调用变量{{'message'}}，帮助大语言模型更好地理解我的提示词里面的“文本”两字。

左侧的紫色款是可调用的变量，蓝色框是可调用的节点

直接点击左侧【message】，可快速调用。调用变量的格式为：{{'变量名'}}

（2）调用 未知变量名

【网页解析】在这个工作流的作用是解析llm_1处理后的链接

因此，插件的URL地址栏里需要调用上一次的输出链接
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/T9Q2birAgomrdvxUrNLcv9DLnzq.png)
点击右上角的“运行”，查看运行日志，确定llm_1的输出——链接的变量名
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/KK1WbbaPkolEgkxgNfRcbcOonc3.png)
定位到llm_1的输出，可以看到经过大模型关键词提取处理后，网页链接储存到了名称为“url”的变量里
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/O495bLNbOo3T00x8zc4cv923n8e.png)
再将此变量填到【网页解析】的URL地址栏里
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/FCZFbeiyloaa7uxQU4HcqzjinZS.png)
同理，在llm_2里

llm_2的设定是“阅读文章内容，然后根据用户的问题，来总结文章内容”

因此，我们需要传入“用户的问题”、“文章内容”的变量

点击运行，查看日志，定位到llm_1的输出，经过大模型关键词提取，“用户的问题”存储到了question里面
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/ME0Cb3NcMomkwxxqpptcOHlYn3d.png)
定位到web_parsing_1，经过网页解析，“文章内容”存储到了content里
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/Ui4BbO3CioSDeIxRCn8cIdGrnzg.png)
此时，llm_2可以顺利调用变量
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/Qgqrbf5tYoCIjux7BxEcvDm8nye.png)
<br/>


<br/>


