---
title: 如何创建一个数据库
urlname: HCg9dDi9goG4NXx1Q8jc38j7nMg
date: 2025-06-03 16:56:32
updated: 2025-06-17 16:41:29
---
# 如何创建一个数据库

以创建“母婴销售助手”的数据库为例
# 新建数据库

![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/Ipo5bvYBpo6CW4xzl47cm5AJnAb.png)
输入名称“母婴销售助手”
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/WU8cbPKsBoAjJAxoUtucc2BBnCe.png)
# 添加数据表

添加数据表有两种方式，导入数据表和创建数据表
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/QUzGbOc5joxnXaxyScfcVY9Enff.png)
## 导入数据表

（1）选择文件导入数据库
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/JHGYb8ii4oShwzxX4ZKcmX1An9d.png)
（2）导入成功后，系统自动解析填入数据表名称和表头
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/Aac8bemfFociQ7x9BFPc5MiTnWc.png)
（3）填入数据表描述，点击保存
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/VVjKb731EoXwq6xKm4PcGu8fnJf.png)
共42条数据
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/Zjewbxud0oufYYxbGLUcAAADnXf.png)
## 创建数据表

（1）填入数据表名称和描述，方便在Agent/flow里调用

名称：母婴小店销售数据

描述：该表用于存储母婴小店的销售记录，包括各类商品的销售情况和相关信息。
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/JDJybbwCGow3UrxetY2c8LLen2a.png)
（2）在红色框内创建数据表的表头
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/K29Hbq0o2oWuwuxv2bacO88Onks.png)
点击保存，创建成功
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/KCCRb59vJoipoDxCQ1kcHfxCn3b.png)
在此界面可以手动输入或者直接导入已有的数据表
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/VZWabt0U1o2Kvqxf4wacmQOOnuh.png)
# 数据库功能

## 创建多个表格

点击“＋”号，重复【添加数据表】的操作，可在数据库内创建多个表格
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/TOHDbzN0JoqJbzx1RSOchGOEnBc.png)
## 筛选

通过添加过滤器，对数据表进行处理
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/VFdLbO87RoAIcTxIZYKca8ENnwb.png)
eg.过滤“宝宝乐婴儿车”的数据，得到下面14行数据
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/ADYqbYrQ7oGm8CxFJaIcKqI2nFe.png)
“全选”，右键“删除所选行”
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/UrUMbkGrdoX7yQxts2ycAaLXnab.png)
删除筛选器后，得到筛选后的表格
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/ThwwbM42CoAbf4xm4L1cieBCn1b.png)
剩余25行数据
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/Ol1abgneCo7JsfxgWL1cemJ6ntf.png)
<br/>


