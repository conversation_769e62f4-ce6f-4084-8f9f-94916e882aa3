---
title: 调用数据库
urlname: ZGxydmSMIoAgK9xz0rXcgKLMn4b
date: 2025-06-03 16:57:21
updated: 2025-06-04 16:02:50
---
# 调用数据库

# Agent里调用数据库

（1）点击“数据”，有【添加数据表】和【选择已有数据库】两种方式

【添加数据表】在[如何创建一个数据库](/%E6%95%B0%E6%8D%AE%E5%BA%93%2F%E5%A6%82%E4%BD%95%E5%88%9B%E5%BB%BA%E4%B8%80%E4%B8%AA%E6%95%B0%E6%8D%AE%E5%BA%93.html)里有详细介绍
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/PCYmbDXnFoMvRhxYwdrcTU83n3f.png)
【选择已有数据库】即可成功调用
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/NfD6bOz1GouSLNxaDzfcYrcJnXe.png)
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/QfVbbJUCyoQobBx27JicSSQxn9c.png)
（2）效果预览，用户自然语言提问 → Agent自动解析意图→ 精准检索数据表 → 运用图标绘制插件 → 返回结构化结果
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/ENBAbDjBio1rV3xyEH0c8ZuInzh.png)
# Flow里调用数据库

工作流中可以通过数据库节点，选择对应的数据库，根据SQL语句对数据库里的数据进行增、删、改、查

注意：执行SQL必加 LIMIT 限制，SQL最多处理500行数据，未加LIMIT将触发智能拦截
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/MuEQbCAh3ocBQVx1HaqcurNdnLb.png)
以“店铺销售助手”的工作流为例

（1）调用数据库里的“betteryeah母婴小店销售数据”表格

选择已创建的数据库【母婴销售助手】
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/CP5UbNHh0oEJI4xbVEPcmgY3ntg.png)
在SQL里填入以下信息
```sql
SELECT * --表示查询结果将返回表中的所有列（即全部字段）
FROM betteryeah母婴小店销售数据--选择数据库里的表格
WHERE 产品='宝宝乐婴儿车' AND 平台='抖音'--筛选符合条件的记录
limit 500--查询行数最多500行
```
（2）选择LLM处理销售数据，生成销售报告
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/GAjAbRlynoSbgwxyJ5scvRyjndh.png)
```markdown
根据数据库信息{{database_1}}，帮我写一份销售报告

## 回复格式
- 参考下面的格式：
-------
# 2024年XXX销售报告
## 一、概述
## 二、销售绩效
## 三、平台分析
## 四、市场分析与建议
## 五、结论
----------

直接返回结果即可
```
(3)点击运行，得到需要的销售报告
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/HVqUbufWkoBWGbxoSyRcQ1iqnIf.png)
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/NRZ5bxBoQojoPdxvECGcvqHTn3b.png)
### 注意事项

数据库里调用变量，我们正常选择变量之后，需要考虑对应的变量值是否是字符串类型，若是需要给变量加单引号标识

eg.构建一个简单工作流，实现查询数据库的功能

开始(输入查询关键词)→数据库→结束

输入节点的变量【查询关键词】是字符串类型，在数据库调用该变量时，要加单引号。格式："{{'message'}}",如下图红色框所示。
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/WDErbLLwbotJ0KxgoTrcsLrTn8d.png)
运行测试，输入“宝宝婴儿车”，得到查找后的结果
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/QArubXiBhoF1B3x6iSGcwsglnGd.png)
<br/>


