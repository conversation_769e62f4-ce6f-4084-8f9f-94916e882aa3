---
title: 什么是数据库
urlname: FTUhdQngZoVFWZx4CKCcvs78nFh
date: 2025-06-03 16:56:00
updated: 2025-06-17 17:14:07
---
# 什么是数据库

数据库是一种关系型数据库，它在Agent中扮演着重要的角色，它用于存储和检索各种关系型的结构化数据。与知识库侧重于存储非结构化的知识不同，数据库更关注于结构化数据的有效管理和使用。

Agent需要记录和更新的结构化信息（例如用户交互历史、交易订单、环境状态变化等等）都会存储在数据库中。而且Agent可以通过查询数据库获取特定信息，例如某个用户的购买记、某些产品不归规格之间的差异、财务的数据分析等。数据库为Agent提供了可靠的数据支持，使其能够根据历史数据和当前环境做出更明智的决策和行动。
## 使用场景

1. 在电商平台，智能客Agent可以使用数据库查询商品信息、用户订单历史和物流状态，从而解答用户疑问并提供个性化服务；推荐系Agent可以利用数据库中的用户行为数据、商品特征和用户画像，为用户推荐感兴趣的商品
1. 金融领域的交Agent可以利用数据库中的市场数据、交易规则和风险评估模型，进行自动化交易和风险管理
# 

<br/>


