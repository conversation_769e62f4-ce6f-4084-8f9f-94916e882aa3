---
title: 2025-06-19 更新日志
urlname: HHIedLki2oN7aLx2De0c8kqXnxF
date: 2025-06-19 19:04:40
updated: 2025-06-19 20:49:00
---
# 2025-06-19 更新日志

## 更新速览

🎉 BetterYeah AI 2025-06-19迭代更新来啦

【Agent】

🤯 自由模式上线！告别单一模型束缚，在对话中随心切换AI模型，对话体验由你掌控！

📂 日志新增首字响应、平均响应等耗时数据，性能瓶颈一目了然！辅助开发者优化Agent。

🆙 打开隐藏技能名称时，发布到钉钉也可隐藏技能名，仅输出答案，让用户直击回复内容！

【知识库】

🛫 对接飞书文档，一键同步飞书知识库！打破知识孤岛，扩展企业知识来源！

🧧 知识库复制改为异步复制，大数据量复制不再失败！降低用户的等待时间！

【数据库】

🛠️ 增加清空功能，一键清空数据表！批量删除不再繁琐，数据管理更高效。

立即体验：http://ai.betteryeah.com
## 更新详情

### Agent

#### 模型增加自由模式

打开自由模式后，使用者可以在调试界面、对话页面、客户端、sdk等交互前端自由选择模型，不再使用固定的指定模型
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/D6Oabu5FxoG7WUx1eXyczHCJnZe.png)
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/OcvLbf5MmoUFOLxXrtvcIPNhnqf.png)
#### 日志增加耗时数据

在Agnt日志列表中增加平均首字响应时长、平均耗时时长
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/XNENb6I4foo5X0x1ai3cPeglnAf.png)
在Agent日志详情中对每次对话增加首字响应时长、内容输出时长
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/NXY9bKyDzo1L8PxLZyMcloBxnPg.png)
#### 钉钉增加隐藏技能

打开隐藏技能名称的开关后，将Agent发布到钉钉，在回答时候，也可以隐藏技能的名称
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/SHYqbMfIUoUFADxgpeect7hHnNc.png)
钉钉机器人在回答时候会隐藏技能
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/JgVMb1hcXoSafKxPjZ4cvlgynbh.png)
<br/>

### 知识库

#### 一键同步飞书知识

先点击绑定飞书数据源，按照提示，绑定企业自建的飞书应用到数据源
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/D6VQbBGZcoCJHzxQ4Stc85xsnYd.png)
按照提示绑定企业自建的应用
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/UexXbEInAo61W2x2k0lcn2fan8f.png)
绑定数据源后，点击登录飞书账号
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/GNzJbQ6VPo9Qx0xXi3Gcg5HBn8d.png)
登陆后可以选择飞书的个人文档或者飞书知识库中的文档，同步到平台知识库中
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/XrO9bcyK9oB1BsxzVJycb7nDn5f.png)
<br/>

#### 异步复制知识库

复制知识库或者关联了知识库的Agent、Flow时，使用异步的方式复制知识库，解决了大数据量知识库导致的复制失败和在复制界面等待太久的问题
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/VoTMbEgOxoH3rxx4DAWcbSisnUg.png)
<br/>

### 数据库

#### 库表增加一键清空

增加一键清空数据表中所有数据的功能
![image](https://resource-bty-ai-public-prod.oss-cn-hangzhou.aliyuncs.com/docs/images/Lgd9bdSusowxxyxZrKSchwyVnmc.png)
<br/>


