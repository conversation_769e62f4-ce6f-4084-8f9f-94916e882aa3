import React from 'react'
import { useRequest } from 'ahooks'
import { getCountByClientTrackData } from '../../../services/officialWebsiteStats'
import { StatsCard } from '../officialWebsiteStats/components/StatsCard'

export function OtherStatistics() {
  const { data: helpPhoneData, loading: helpLoading } = useRequest(() => {
    return getCountByClientTrackData({
      where: '(event_id,eq,help_phone_appointment)',
    })
  })

  const { data: submitCount, loading: submitLoading } = useRequest(() => {
    return getCountByClientTrackData({
      where: '(event_id,eq,help_solution_submit)',
    })
  })

  const { data: clientExploreCountData, loading: clientExploreCountLoading } =
    useRequest(() => {
      return getCountByClientTrackData({
        where: '(event_id,eq,client_explore_count)',
      })
    })

  const { data: clientExploreBannerData, loading: clientExploreBanneLoading } =
    useRequest(() => {
      return getCountByClientTrackData({
        where: '(event_id,eq,client_explore_banner)',
      })
    })

  return (
    <div className='flex flex-col gap-y-20px'>
      <div>
        <h1 className='text-18px font-500'>平台轮播-预约演示</h1>
        <div className='flex gap-x-15px mt-20px'>
          <StatsCard
            title='总点击'
            value={clientExploreCountData?.count}
            loading={clientExploreCountLoading}
          />
          <StatsCard
            title='预约数'
            value={clientExploreBannerData?.count}
            loading={clientExploreBanneLoading}
          />
        </div>
      </div>
      <div>
        <h1 className='text-18px font-500'>帮助中心-预约演示</h1>
        <div className='flex gap-x-15px mt-20px'>
          <StatsCard
            title='总点击'
            value={helpPhoneData?.count}
            loading={helpLoading}
          />
          <StatsCard
            title='预约数'
            value={submitCount?.count}
            loading={submitLoading}
          />
        </div>
      </div>
    </div>
  )
}
