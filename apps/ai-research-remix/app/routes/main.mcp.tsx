import { memo } from 'react'
import { json } from '@remix-run/node'
import type { LoaderFunctionArgs } from '@remix-run/node'
import { MCPSetting } from '@/components/mcp/settings/mcp-setting'

export async function loader({ context }: LoaderFunctionArgs) {
  return json({
    user: context.user,
    token: context.userToken as string,
  })
}

function MCP() {
  return (
    <div className='flex-1 flex bg-white relative of-hidden'>
      <MCPSetting />
    </div>
  )
}

export default !import.meta.env.SSR ? memo(MCP) : () => null
