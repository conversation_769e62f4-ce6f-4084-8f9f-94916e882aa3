import { json } from '@remix-run/node'
import type { MetaFunction, LoaderFunctionArgs } from '@remix-run/node'
import { useTitle } from 'ahooks'
import { memo, useEffect } from 'react'
import { noop } from 'lodash-es'
import { AskChat } from '@/components/next-agent'
import { NextAgentProvider } from '@/components/next-agent/provider/NextAgentProvider'
import { NextAgentEventProvider } from '@/components/next-agent/provider/NextAgentEventProvider'
import { TaskPanel } from '@/components/task-panel'
import { useTaskStore } from '@/store/task'
import { useTaskEvent } from '@/store/task-event'

export const meta: MetaFunction = () => {
  return [{ title: 'Next Agent' }]
}

export async function loader({ context }: LoaderFunctionArgs) {
  return json({
    user: context.user,
    token: context.userToken as string,
  })
}

function MainAsk() {
  useTitle('Next Agent')

  const hidePanel = useTaskStore(state => state.hidePanel)

  const setTaskId = useTaskEvent(state => state.setTaskId)

  useEffect(() => {
    return () => {
      hidePanel()
      setTaskId(undefined)
    }
  }, [])

  return (
    <div className='flex-1 flex bg-white relative of-hidden'>
      <NextAgentEventProvider
        onReferenceClick={noop}
        onReferencesClick={noop}
        onConversationChangeHook={noop}
      >
        <NextAgentProvider type='MULTIPLE' activeReferenceMessageId={''}>
          <AskChat />
          <TaskPanel />
        </NextAgentProvider>
      </NextAgentEventProvider>
    </div>
  )
}

export default !import.meta.env.SSR ? memo(MainAsk) : () => null
