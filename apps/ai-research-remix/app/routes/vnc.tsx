import { useSearchParams, type MetaFunction } from '@remix-run/react'
import { memo, useEffect, useState } from 'react'
import { useInterval } from 'ahooks'
import { sendSandboxHeart, getSandboxInfo } from '@apis/mindnote/sandbox'
import { Spin } from 'antd'
import { Screen } from '@/components/vnc'

export const meta: MetaFunction = () => {
  return [{ name: 'referrer', content: 'no-referrer' }]
}

function VncPage() {
  const [params] = useSearchParams()
  const mode = params.get('mode') ?? 'view'
  const taskId = params.get('taskId')

  const [isLoading, setIsLoading] = useState(false)
  const [isRunning, setIsRunning] = useState(false)
  const [address, setAddress] = useState('')
  const [password, setPassword] = useState('')

  useInterval(() => {
    if (taskId && isRunning) {
      sendSandboxHeart(taskId)
    }
  }, 1000 * 30)

  useEffect(() => {
    if (!taskId) return
    setIsLoading(true)
    getSandboxInfo(taskId).then(res => {
      setAddress(res.domain ? `${res.domain}:8081` : '')
      setPassword(res.pwd)
      setIsRunning(res.status.toLowerCase() === 'running')
      setIsLoading(false)
    })
  }, [taskId])

  useEffect(() => {
    const app = document.getElementById('app')
    if (!app) return
    app.style.minWidth = '0'
  }, [])

  if (isLoading) {
    return (
      <div className='size-full flex items-center justify-center'>
        <Spin />
      </div>
    )
  }

  if (!isRunning) {
    return (
      <div className='size-full flex items-center justify-center'>
        <div>虚拟机未启动</div>
      </div>
    )
  }

  return (
    <div className='size-full'>
      <Screen
        ws={`wss://${address}/vnc`}
        control={`https://${address}/control`}
        password={password}
        viewOnly={mode !== 'control'}
      />
    </div>
  )
}

export default !import.meta.env.SSR ? memo(VncPage) : () => null
