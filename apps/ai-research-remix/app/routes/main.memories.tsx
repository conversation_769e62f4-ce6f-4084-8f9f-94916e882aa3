import { type MetaFunction, type LoaderFunctionArgs } from '@remix-run/node'
import { json } from '@remix-run/node'
import { useTitle } from 'ahooks'
import { memo } from 'react'
import { MemoriesRoot } from '@/components/memories-root'

export const meta: MetaFunction = () => {
  return [{ title: 'Memories' }]
}

export async function loader({ context }: LoaderFunctionArgs) {
  return json({
    user: context.user,
    token: context.userToken as string,
  })
}

function MainMemories() {
  useTitle('Memories')

  return <MemoriesRoot />
}

export default !import.meta.env.SSR ? memo(MainMemories) : () => null
