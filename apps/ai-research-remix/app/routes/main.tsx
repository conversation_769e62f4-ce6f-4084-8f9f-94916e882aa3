import { redirect } from '@remix-run/node'
import type { LoaderFunctionArgs } from '@remix-run/node'
import type { MetaFunction } from '@remix-run/react'
import { Outlet, useMatch, useNavigate } from '@remix-run/react'
import { memo, useEffect, useMemo, useRef } from 'react'
import classNames from 'classnames'
import { useDebounceFn, useMemoizedFn } from 'ahooks'
import { App, Divider, Tooltip } from 'antd'
import { checkUserCanUse } from '@apis/mindnote/user'
import { MainNav, TagItemNav, TagNav } from '@/components/main-nav'
import { User } from '@/components/user'
import { APPLY_STATUS_CODE, basePath, LOGIN_PATH } from '@/const'
import { Icon, IconButton } from '@/components/base/icon'
import { useTagStore } from '@/store/tag'
import { UserWechat } from '@/components/user-wechat'
import { ScrollView } from '@/components/base/scroll-view'
import { Image } from '@/components/base/image'
import { UserDownload } from '@/components/user-download'
import { useNavStore } from '@/store/nav'
import { useReadStore } from '@/store/read'

export const meta: MetaFunction = () => {
  return [{ title: 'MindNote' }]
}

export async function loader({ context }: LoaderFunctionArgs) {
  const res = await checkUserCanUse(context.userToken)

  if (
    [APPLY_STATUS_CODE.NO_PERMISSION, APPLY_STATUS_CODE.APPLYING].includes(
      res?.code,
    )
  ) {
    return redirect(LOGIN_PATH)
  }

  return {}
}

const CollectionIcon = (
  <Icon icon='i-icons-collection' size='size-18px' className='text-[#2F75FF]' />
)
const SubIcon = (
  <Icon icon='i-icons-bookmark' size='size-18px' className='text-[#1ECDB0]' />
)
const AskIcon = (
  <Icon icon='i-icons-aiask' size='size-18px' className='text-[#7B61FF]' />
)
const MemoriesIcon = (
  <Icon icon='i-icons-memories' size='size-18px' className='text-[#7B61FF]' />
)
const NextAgentIcon = (
  <Icon icon='i-icons-nextagent' size='size-18px' className='text-[#7B61FF]' />
)
const tagIcon = (
  <Icon icon='i-icons-tag-fill' size='size-18px' className='text-[#FF6385]' />
)
const ChatIcon = (
  <Icon icon='i-icons-chat' size='size-18px' className='text-[#A061FF]' />
)
// 暂时隐藏 等待添加
const MCPIcon = (
  <Icon icon='i-icons-mcp' size='size-18px' className='text-[#A061FF]' />
)

const UNREAD_LOAD_COUNT = 2

function Main() {
  const { message } = App.useApp()

  const navigate = useNavigate()
  const mainMatch = useMatch('/main/:type')
  const tagMatch = useMatch('/main/tag/:tagId')
  const mainType = useMemo(
    () => mainMatch?.params.type ?? '',
    [mainMatch?.params.type],
  )

  const show = useNavStore(s => s.show)
  const allRead = useReadStore(s => s.allRead)
  const readCount = useReadStore(s => s.readCount)
  const tagList = useTagStore(s => s.list)
  const unreadCount = useRef(0)

  const toggleShow = useMemoizedFn(() => {
    document.dispatchEvent(new Event(show ? 'closeNav' : 'showNav'))
  })

  const handleToTop = useMemoizedFn(() => {
    const container: HTMLElement = document.querySelector(
      '.collection-list-container .scroll-view',
    )!
    if (!container) return
    container.scrollTo({
      top: 0,
      behavior: 'smooth',
    })
  })

  const handleToCollectionUnread = useMemoizedFn(() => {
    const container: HTMLElement = document.querySelector(
      '.collection-list-container .scroll-view',
    )!
    if (!container) return
    if (allRead.collection) {
      container.scrollTo({
        top: 0,
        behavior: 'smooth',
      })
      return
    }

    const card: HTMLElement = document.querySelector('.collection-card-unread')!
    if (card) {
      container.scrollTo({
        top: card.offsetTop - 40,
        behavior: 'smooth',
      })
      return
    }
    container.scrollTo({ top: container.scrollHeight, behavior: 'smooth' })
  })

  const handleToSubUnread = useMemoizedFn(() => {
    const container: HTMLElement = document.querySelector(
      '.sub-list-container .scroll-view',
    )!
    if (!container) return
    if (allRead.subscribe) {
      container.scrollTo({
        top: 0,
        behavior: 'smooth',
      })
      return
    }

    const card: HTMLElement = document.querySelector('.sub-card-unread')!
    if (card) {
      unreadCount.current = 0
      container.scrollTo({
        top: card.offsetTop - 40,
        behavior: 'smooth',
      })
      return
    }
    if (unreadCount.current < UNREAD_LOAD_COUNT) {
      unreadCount.current += 1
      container.scrollTo({ top: container.scrollHeight, behavior: 'smooth' })
      return
    }

    unreadCount.current = 0
    document.dispatchEvent(new Event('switchToUnread'))
    message.info('检测到大量未读，正在加载所有未读……')
  })

  const { run: handleRefresh } = useDebounceFn(
    () => {
      document.dispatchEvent(new Event('updateReadState'))
    },
    { wait: 1000 },
  )

  const handleTagDelete = useMemoizedFn((id: string) => {
    if (id !== tagMatch?.params.tagId) return

    const list = useTagStore.getState().list
    if (list.length === 0) {
      navigate('/main/collection')
    } else {
      navigate(`/main/tag/${list[0].label_id}`)
    }
  })

  const handleTagAdd = useMemoizedFn((id: string) => {
    navigate(`/main/tag/${id}`)
  })

  useEffect(() => {
    document.dispatchEvent(new Event('updateTagList'))
    document.dispatchEvent(new Event('updateReadState'))
  }, [])

  return (
    <main className='size-full flex of-hidden bg-#F3F3F5'>
      <div
        className={classNames(
          'h-full flex-none flex flex-col items-start relative duration-300',
          {
            'w-212px': show,
            'w-72px !items-center': !show,
          },
        )}
      >
        {!show && (
          <Tooltip
            overlayClassName='text-12px/24px'
            placement='right'
            title='展开侧边栏'
          >
            <IconButton
              icon='i-icons-open-side'
              iconSize='size-18px'
              size='size-36px'
              className='mt-16px text-[#626999]/60'
              onClick={toggleShow}
            />
          </Tooltip>
        )}

        <div
          className={classNames(
            'w-full flex-none p-12px flex-center gap-12px overflow-hidden',
            {
              'pt-18px': show,
            },
          )}
        >
          {show && (
            <Image
              className='flex-none h-32px'
              src={`${basePath}/logo-text.png`}
            />
          )}
          {!show && (
            <Image className='flex-none h-32px' src={`${basePath}/logo.png`} />
          )}

          {show && (
            <Tooltip
              overlayClassName='text-12px/24px'
              placement='bottom'
              title='收起侧边栏'
            >
              <IconButton
                icon='i-icons-close-side'
                iconSize='size-18px'
                size='size-36px'
                className='text-[#626999]/60 flex-none'
                onClick={toggleShow}
              />
            </Tooltip>
          )}
        </div>

        <div className='w-full flex-none flex flex-col gap-4px p-12px pt-0px'>
          <MainNav
            show={!!show}
            now={mainType}
            router='next-agent'
            icon={NextAgentIcon}
            name={
              <div className='flex flex-wrap items-center justify-center'>
                {/* <span>Next</span>
                <span className='ml-4px'>Agent</span> */}
                任务
              </div>
            }
          />
          <MainNav
            show={!!show}
            now={mainType}
            router='memories'
            icon={MemoriesIcon}
            name='经验'
          />

          <div className='h-1px flex-none w-auto bg-[#E1E1E5]/80 mx-12px self-stretch'></div>
          <MainNav
            show={!!show}
            now={mainType}
            router='ask'
            icon={AskIcon}
            name='AI Ask'
          />

          <MainNav
            show={!!show}
            now={mainType}
            router='mcp-chat'
            icon={ChatIcon}
            name='MChat'
          />

          <MainNav
            show={!!show}
            now={mainType}
            router='mcp'
            icon={MCPIcon}
            name='MCP'
          />

          <div className='h-1px flex-none w-auto bg-[#E1E1E5]/80 mx-12px self-stretch'></div>

          <MainNav
            show={!!show}
            now={mainType}
            router='collection'
            icon={CollectionIcon}
            name='收集'
            extra={
              !allRead.collection ? (
                <div
                  className={classNames(
                    'size-8px bg-[#7B61FF] b-line b-1 b-#Fff rd-6px absolute',
                    {
                      'top-20px right-10px': show,
                      'top-10px right-14px': !show,
                    },
                  )}
                />
              ) : (
                show && (
                  <span className='text-12px/26px text-#8D8D99/60'>
                    {readCount.collection}
                  </span>
                )
              )
            }
            onClick={handleRefresh}
            onDoubleClick={handleToCollectionUnread}
          />

          <MainNav
            show={!!show}
            now={mainType}
            router='sub'
            icon={SubIcon}
            name='订阅'
            extra={
              !allRead.subscribe ? (
                <div
                  className={classNames(
                    'size-8px bg-[#7B61FF] b-line b-1 b-#Fff rd-6px absolute',
                    {
                      'top-20px right-10px': show,
                      'top-10px right-14px': !show,
                    },
                  )}
                />
              ) : (
                show && (
                  <span className='text-12px/26px text-#8D8D99/60'>
                    {readCount.subscribe}
                  </span>
                )
              )
            }
            onClick={handleRefresh}
            onDoubleClick={handleToSubUnread}
          />
        </div>

        <div className='h-1px flex-none w-auto bg-[#E1E1E5]/80 mx-12px self-stretch'></div>

        <div className='w-full flex-auto of-hidden flex flex-col gap-4px p-12px'>
          <TagNav show={!!show} icon={tagIcon} onAdd={handleTagAdd} />

          <ScrollView vbarClassName='right-[-12px]!'>
            {tagList.map(each => {
              return (
                <TagItemNav
                  key={each.label_id}
                  now={tagMatch?.params.tagId}
                  show={!!show}
                  data={each}
                  onDelete={handleTagDelete}
                  onDoubleClick={handleToTop}
                />
              )
            })}
          </ScrollView>
        </div>

        <div className='mt-auto w-full flex-none p-12px flex-center flex-col'>
          {show && (
            <div
              className={classNames('flex gap-8px w-full', {
                'flex-col items-center': !show,
              })}
            >
              <UserWechat collapse={!show} />
              {window.MindNote?.platform === 'macOS' ? null : (
                <UserDownload collapse={!show} />
              )}
            </div>
          )}
          {window.MindNote?.platform === 'macOS'
            ? null
            : show && <Divider className='my-12px' />}
          <User collapse={!show} />
        </div>
      </div>
      <Outlet />
    </main>
  )
}

export default !import.meta.env.SSR ? memo(Main) : () => null
