import type { McpConfig, McpTool } from '@bty/global-types/mcp'
import { localize } from '@bty/localize'

/**
 * 连接mcp server
 */
export async function connectStdioMcp(mcpConfig: McpConfig) {
  if (window.MindNote?.mcp) {
    return new Promise((resolve, reject) => {
      const config: Record<string, any> = {
        ...mcpConfig,
      }

      if (mcpConfig.type === 'command') {
        config.args = mcpConfig.args
          .split(' ')
          .map(item => item.trim())
          .filter(d => d)
        config.env = parseEnvString(mcpConfig.env)
      }

      window.MindNote?.mcp.connect(config)
      console.log('do connectMcpServer', config)

      const timer = setTimeout(
        () => {
          reject(new Error(localize('mcp.connect.timer.error', '连接超时')))
        },
        3 * 60 * 1000,
      )

      const onConnection = (event: any) => {
        console.log('onConnection response', event)
        if (
          event.data &&
          event.data.action &&
          event.data.action === 'mcp.connect.response' &&
          event.data.code === mcpConfig.code
        ) {
          window.removeEventListener('message', onConnection)
          clearTimeout(timer)

          const result = event.data.params ?? {}
          if (result.success) {
            console.log('Debug 连接成功！')
            resolve(result.data)
          } else {
            reject(
              new Error(
                result.message ||
                  localize('mcp.connect.error.unknown', '连接失败'),
              ),
            )
          }
        }
      }
      window.addEventListener('message', onConnection)
    })
  }
  return Promise.reject(
    new Error(localize('mcp.client.error', '请在客户端使用')),
  )
}

export async function disConnectStdioMcp(mcp: McpConfig) {
  if (window.MindNote?.mcp) {
    return new Promise((resolve, reject) => {
      window.MindNote?.mcp.disConnect({
        code: mcp.code,
        name: mcp.name,
      })

      const timer = setTimeout(() => {
        reject(new Error(localize('mcp.connect.timer.error', '请求超时')))
      }, 1 * 1000)

      const onResponse = (event: any) => {
        console.log('onResponse', event)
        if (
          event.data &&
          event.data.action &&
          event.data.action === 'mcp.close_connect.response' &&
          event.data.code === mcp.code
        ) {
          window.removeEventListener('message', onResponse)
          clearTimeout(timer)
          resolve(true)
        }
      }
      window.addEventListener('message', onResponse)
    })
  }
}

/**
 * 获取mcp工具
 */
export async function getStdioMcpTools(code: string): Promise<McpTool[]> {
  if (window.MindNote?.mcp) {
    return new Promise((resolve, reject) => {
      window.MindNote?.mcp.getToolList({ code })

      const timer = setTimeout(
        () => {
          reject(
            new Error(localize('mcp.get_tools.timer.error', '获取工具超时')),
          )
        },
        3 * 60 * 1000,
      )

      const onGetTools = (event: any) => {
        console.log('onGetTools', event)
        if (
          event.data &&
          event.data.action &&
          event.data.action === 'mcp.tools__list.response' &&
          event.data.code === code
        ) {
          window.removeEventListener('message', onGetTools)
          clearTimeout(timer)

          const result = event.data.params ?? {}
          if (result.success) {
            const tools = result.data?.tools ?? []
            resolve(tools)
          } else {
            reject(
              new Error(
                result.message ||
                  localize('mcp.get_tools.error.unknown', '获取工具失败'),
              ),
            )
          }
        }
      }
      window.addEventListener('message', onGetTools)
    })
  }
  return Promise.reject(
    new Error(localize('mcp.client.error', '请在客户端使用')),
  )
}

export async function callStdioMcpTool(
  code: string,
  toolName: string,
  timeout = 30,
  params?: Record<string, any>,
) {
  if (window.MindNote?.mcp) {
    return new Promise((resolve, reject) => {
      const eventId = `${code}__${toolName}__${Date.now()}`

      console.log('call tool ', eventId)
      window.MindNote?.mcp.executeTool({
        eventId,
        code,
        toolName,
        arguments: params,
      })

      const timer =
        timeout > 0
          ? setTimeout(() => {
              /**
               * 超时时补发一个断开连接的消息，进程中会根据此消息，kill MCP的运行进程
               */
              window.MindNote?.mcp.disConnect({
                code,
                name: toolName,
              })
              reject(
                new Error(
                  localize('mcp.execute_tool.timer.error', '执行工具超时'),
                ),
              )
            }, timeout * 1000)
          : null

      const onExecuteTool = (event: any) => {
        console.log('onExecuteTool', event)
        if (
          event.data &&
          event.data.action &&
          event.data.action === 'mcp.tools__call.response' &&
          event.data.eventId === eventId
        ) {
          console.log('onExecuteTool success id: ', event.data.eventId)
          window.removeEventListener('message', onExecuteTool)
          timer && clearTimeout(timer)

          const result = event.data.params ?? {}
          if (result.success) {
            resolve(result.data)
          } else {
            reject(
              new Error(
                result.message ||
                  localize('mcp.execute_tool.error.unknown', '执行工具失败'),
              ),
            )
          }
        }
      }
      window.addEventListener('message', onExecuteTool)
    })
  }
  return Promise.reject(
    new Error(localize('mcp.client.error', '请在客户端使用')),
  )
}

/**
 * 解析环境变量字符串为对象
 * @param envString 格式为 KEY=value\nKEY2=value2 的环境变量字符串
 * @returns 解析后的环境变量对象
 */
export function parseEnvString(envString: string): Record<string, string> {
  if (!envString) return {}

  return envString.split('\n').reduce(
    (acc, item) => {
      const trimmedItem = item.trim()
      if (!trimmedItem) return acc

      const firstEqualIndex = trimmedItem.indexOf('=')
      if (firstEqualIndex <= 0) return acc

      const key = trimmedItem.substring(0, firstEqualIndex)
      const value = trimmedItem.substring(firstEqualIndex + 1)

      if (key) {
        acc[key] = value
      }
      return acc
    },
    {} as Record<string, string>,
  )
}

export async function checkClientMcpEnv() {
  if (window.MindNote?.mcp) {
    return new Promise(resolve => {
      window.MindNote?.mcp.checkEnv()
      const timer = setTimeout(() => {
        resolve(false)
      }, 30 * 1000)

      const onCheckEnv = (event: any) => {
        console.log('onCheckEnv', event)
        if (
          event.data &&
          event.data.action &&
          event.data.action === 'mcp.checkDownloadEnv.response'
        ) {
          window.removeEventListener('message', onCheckEnv)
          clearTimeout(timer)
          resolve(!!event.data.status)
        }
      }
      window.addEventListener('message', onCheckEnv)
    })
  }
  return Promise.resolve(false)
}

export async function installClientMcpEnv() {
  if (window.MindNote?.mcp) {
    return new Promise((resolve, reject) => {
      window.MindNote?.mcp.installEnv()

      const timer = setTimeout(
        () => {
          reject(
            new Error(
              localize('mcp.client.install.env.error', '环境安装超时，请重试'),
            ),
          )
        },
        10 * 60 * 1000,
      )

      const onInstallEnv = (event: any) => {
        console.log('onInstallEnv', event)
        if (
          event.data &&
          event.data.action &&
          event.data.action === 'mcp.installEnv.response'
        ) {
          window.removeEventListener('message', onInstallEnv)
          clearTimeout(timer)
          resolve(true)
        }
      }
      window.addEventListener('message', onInstallEnv)
    })
  }

  return Promise.reject(
    new Error(
      localize('mcp.client.install.env.error', '环境安装失败，请在客户端使用'),
    ),
  )
}
