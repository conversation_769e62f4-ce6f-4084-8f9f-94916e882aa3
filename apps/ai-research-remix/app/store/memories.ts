import {
  createMemory,
  deleteMemory,
  getMemories,
  updateMemory,
  type CreateMemoryPayload,
  type Memory,
} from '@apis/mindnote/memories'
import { create } from 'zustand'
import { v4 as uuidv4 } from 'uuid'
import dayjs from 'dayjs'

export function isDraftMemory(memory?: Memory) {
  return memory?.id.startsWith('draft-') ?? false
}

interface MemoriesStore {
  selectedMemoryId?: string
  memories?: Memory[]
  getMemories: () => Promise<void>
  select: (id: string) => void
  draft: VoidFunction
  create: (data: CreateMemoryPayload) => Promise<void>
  update: (data: Partial<CreateMemoryPayload>) => Promise<void>
  remove: () => Promise<void>
  reload: VoidFunction
}

export const useMemories = create<MemoriesStore>((set, get) => ({
  selectedMemoryId: undefined,

  memories: undefined,

  getMemories: async () => {
    const response = await getMemories()
    const memories = response.data
    set({
      memories,
      selectedMemoryId: memories?.[0]?.id,
    })
  },

  select: (id: string) => set({ selectedMemoryId: id }),

  draft: () => {
    const id = `draft-${uuidv4()}`
    const draftMemory: Memory = {
      id,
      name: '新经验',
      describe: '待补充',
      created_at: dayjs().format('YYYY-MM-DD HH:mm'),
    }
    set(prev => ({
      memories: [...(prev.memories ?? []), draftMemory],
      selectedMemoryId: id,
    }))
  },

  create: async (data: CreateMemoryPayload) => {
    try {
      const response = await createMemory(data)
      set(prev => ({
        memories: prev.memories?.filter(el => !isDraftMemory(el)) ?? [],
        selectedMemoryId: response.id,
      }))
      get().reload()
    } catch (error) {
      // ignore
    }
  },

  update: async (data: Partial<CreateMemoryPayload>) => {
    const selectedMemoryId = get().selectedMemoryId
    if (!selectedMemoryId) {
      return
    }

    if (isDraftMemory({ id: selectedMemoryId } as Memory)) {
      return
    }

    try {
      await updateMemory(selectedMemoryId, data)
      get().reload()
    } catch (error) {
      // ignore
    }
  },

  remove: async () => {
    const selectedMemoryId = get().selectedMemoryId
    const isDraft = isDraftMemory({ id: selectedMemoryId } as Memory)

    if (isDraft) {
      set(prev => ({
        memories: prev.memories?.filter(el => el.id !== selectedMemoryId),
        selectedMemoryId: prev.memories?.[0]?.id ?? undefined,
      }))
      return
    }

    if (selectedMemoryId) {
      try {
        await deleteMemory(selectedMemoryId)
        get().getMemories()
      } catch (error) {
        // ignore
      }
    }
  },

  reload: async () => {
    const response = await getMemories()
    const memories = response.data
    set({ memories })
  },
}))

export function useFrontMemory() {
  const memoryId = useMemories(state => state.selectedMemoryId)

  const memory = useMemories(state =>
    state.memories?.find(el => el.id === memoryId),
  )

  return memory
}
