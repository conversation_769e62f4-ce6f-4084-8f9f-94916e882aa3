import { getArtifacts } from '@apis/mindnote/next-agent-chat'
import type { TaskArtifact } from '@apis/mindnote/next-agent-chat.type'
import type { StateCreator } from 'zustand'

export interface TaskArtifactSlice {
  artifacts: Map<string, TaskArtifact>
  frontArtifact: TaskArtifact | null
  addArtifacts: (...artifacts: TaskArtifact[]) => void
  getArtifactsList: () => TaskArtifact[]
  openArtifact: (artifact: TaskArtifact) => void
  closeArtifact: VoidFunction
  clearArtifacts: VoidFunction
}

const initialState = {
  artifacts: new Map(),
  frontArtifact: null,
}

export const createTaskArtifactSlice: StateCreator<TaskArtifactSlice> = (
  set,
  get,
) => ({
  ...initialState,

  fetchArtifacts: async (taskId: string) => {
    const artifacts = await getArtifacts(taskId)
    set({
      artifacts: new Map(artifacts.map(artifact => [artifact.path, artifact])),
    })
  },

  addArtifacts: (...artifacts) =>
    set(state => {
      const newArtifacts = new Map(state.artifacts)
      artifacts.forEach(artifact => {
        newArtifacts.set(artifact.path, artifact)
      })
      return { artifacts: newArtifacts }
    }),

  getArtifactsList: () => Array.from(get().artifacts.values()),

  openArtifact: artifact => {
    set({ frontArtifact: artifact })
  },

  closeArtifact: () => {
    set({ frontArtifact: null })
  },

  clearArtifacts: () => {
    set({ artifacts: new Map() })
  },
})
