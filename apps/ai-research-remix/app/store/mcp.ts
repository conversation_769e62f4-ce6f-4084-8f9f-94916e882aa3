import type { McpConfig, McpTool, TagsType } from '@bty/global-types/mcp'
import { localize } from '@bty/localize'
import { create } from 'zustand'
import {
  createMcpPlugin,
  batchCreateMcpPlugin,
  deleteMcpPlugin,
  generateMcpDescription,
  getMcpPluginList,
  updateMcpPlugin,
} from '@apis/mindnote/mcp'
import { cloneDeep } from 'lodash-es'
import {
  connectMcp,
  disconnectMcp,
  getMcpTools,
} from '@/components/mcp-chat/utils/mcp'
import { checkClientMcpEnv } from '@/native-bridge/mcp'

export type McpListItem = McpConfig & {
  tools: McpTool[]
  state: 'success' | 'failed' | 'connecting'
  errorMessage?: string
  functionId: string
  source: 'CLIENT' | 'CLOUD'
  descriptionState: 'success' | 'failed' | 'pending'
  external_source_url?: string
  tags?: TagsType[]
  description?: {
    introduction: string
    tools: Array<{ title: string; content: string }>
    examples: Array<{ title: string; content: string }>
  }
}

interface Store {
  clientEnvStatus: 'checking' | 'success' | 'failed'
  list: McpListItem[]
  init: () => Promise<void>
  refreshMcpList: () => Promise<string>
  addMcp: (mcpConfig: McpConfig) => Promise<string>
  batchAddMcp: (mcpConfigList: McpConfig[]) => Promise<string[]>
  addMcpFrontSave: (mcpConfig: McpConfig) => Promise<string>
  updateMcp: (
    functionId: string,
    mcpConfig: Partial<McpConfig>,
  ) => Promise<void>
  deleteMcp: (functionId: string) => Promise<McpListItem[]>
  updateMcpListItem: (id: string, payload: Partial<McpListItem>) => void
  connectMcp: (functionId: string) => Promise<void>
  generateMcpDescription: (
    functionId: string,
    tools: McpTool[],
  ) => Promise<void>
  setClientEnvStatus: (status: 'success' | 'failed') => void
}

// 工具函数：将 McpPlugin 转换为 McpListItem
function convertToMcpListItem(item: any): McpListItem {
  let descriptionState: McpListItem['descriptionState'] = 'pending'
  let description: McpListItem['description']
  if (item.description?.trim()) {
    try {
      description = JSON.parse(item.description) as McpListItem['description']
      descriptionState = 'success'
    } catch (error) {}
  }

  return {
    ...(item.config as McpConfig),
    tools: [],
    name: item.config?.name || item.name,
    code: item?.code || '',
    tags: item?.tags || [],
    is_self_developed: item?.is_self_developed,
    auto_execute: !!item.is_auto_executed,
    state: 'connecting',
    functionId: item.function_id,
    source: item.source,
    descriptionState,
    description,
  }
}

export const useMcpStore = /* @__PURE__ */ create<Store>((set, get) => ({
  clientEnvStatus: 'checking',
  list: [],
  init: async () => {
    checkClientMcpEnv()
      .then(res => {
        console.log('checkClientMcpEnv', res)
        set({ clientEnvStatus: res ? 'success' : 'failed' })
      })
      .catch(() => {
        set({ clientEnvStatus: 'failed' })
      })
    // 1. 从服务端获取已有的mcp列表
    const mcpPluginList = await getMcpPluginList(
      !window.MindNote ? 'CLOUD' : undefined,
    )

    const list: McpListItem[] = mcpPluginList?.map(convertToMcpListItem) ?? []
    set((state: Store) => ({ list: [...state.list, ...list] }))

    const connectMcp = get().connectMcp
    list.forEach(async item => {
      connectMcp(item.functionId)
    })
  },
  refreshMcpList: async () => {
    const allMcpPluginList = await getMcpPluginList(
      !window.MindNote ? 'CLOUD' : undefined,
    )

    const targetMcpPluginList = get().list
    // 创建一个 Set 来存储所有有效的 functionId
    const validFunctionIds = new Set(
      allMcpPluginList.map(item => item.function_id),
    )

    // 1. 删除不在 allMcpPluginList 中的项
    set(state => ({
      list: state.list.filter(item => validFunctionIds.has(item.functionId)),
    }))

    // 2. 找出需要新增的项目
    const existingFunctionIds = new Set(
      targetMcpPluginList.map(item => item.functionId),
    )
    const itemsToAdd = allMcpPluginList.filter(
      item => !existingFunctionIds.has(item.function_id),
    )

    // 3. 添加新项目
    const newItems: McpListItem[] = itemsToAdd.map(convertToMcpListItem)

    // 4. 更新 store 并连接新项目
    set(state => ({ list: [...state.list, ...newItems] }))
    // 5. 连接新项目
    const connectMcp = get().connectMcp
    for (const item of newItems) {
      connectMcp(item.functionId)
    }

    return 'success'
  },
  setClientEnvStatus: (status: 'success' | 'failed') => {
    set({ clientEnvStatus: status })
  },
  addMcp: async (mcpConfig: McpConfig) => {
    const connectMcp = get().connectMcp
    const source = window.MindNote ? 'CLIENT' : 'CLOUD'
    // 2. 保存到服务端
    const createResult = await createMcpPlugin({
      name: mcpConfig.name,
      code: mcpConfig.code,
      plugin_type: 'MCP',
      source,
      config: mcpConfig,
      is_auto_executed: mcpConfig.auto_execute,
    })
    set((state: Store) => ({
      list: [
        {
          ...mcpConfig,
          tools: [],
          state: 'connecting',
          source,
          functionId: createResult.function_id,
          descriptionState: 'pending',
          description: undefined,
        },
        ...state.list,
      ],
    }))
    connectMcp(createResult.function_id)
    return createResult.function_id
  },
  batchAddMcp: async (mcpConfigList: McpConfig[]) => {
    const connectMcp = get().connectMcp
    const source = window.MindNote ? 'CLIENT' : 'CLOUD'
    const batchCreateResult = await batchCreateMcpPlugin({
      functions: mcpConfigList,
    })
    const processedBatchCreateResult = batchCreateResult.map((item: any) => {
      const mcpType = item.config?.command ? 'command' : 'sse'
      return {
        ...item,
        command: item.config?.command || '',
        type: mcpType,
        auto_execute: false,
        url: item.config?.url || '',
        args: item.config?.args?.join(' ') || '',
        env: item?.config?.env
          ? Object.entries(item?.config?.env)
              .map(([key, value]) => `${key}=${value}`)
              .join('\n')
          : '',
      }
    })
    set((state: Store) => ({
      list: [
        ...processedBatchCreateResult?.map((item: any) => {
          return {
            ...item,
            tools: [],
            state: 'connecting',
            source,
            functionId: item.function_id,
            descriptionState: 'pending',
            description: undefined,
          }
        }),
        ...state.list,
      ],
    }))
    processedBatchCreateResult?.forEach(async (item: any) => {
      connectMcp(item.function_id)
    })
    return processedBatchCreateResult
  },
  addMcpFrontSave: async (mcpConfig: any) => {
    const connectMcp = get().connectMcp
    const source = window.MindNote ? 'CLIENT' : 'CLOUD'
    console.log('mcpConfig -------- ', mcpConfig)
    set((state: Store) => ({
      list: [
        {
          ...mcpConfig.config,
          tags: mcpConfig?.tags,
          code: mcpConfig?.code,
          tools: [],
          name: mcpConfig.name,
          state: 'connecting',
          source,
          functionId: mcpConfig?.function_id,
          descriptionState: 'pending',
          description: undefined,
        },
        ...state.list,
      ],
    }))
    connectMcp(mcpConfig.function_id)
    return mcpConfig.function_id
  },
  updateMcp: async (functionId: string, mcpConfig: Partial<McpConfig>) => {
    const updateMcpListItem = get().updateMcpListItem
    let needReconnect = false
    const prevMcpItem = get().list.find(item => item.functionId === functionId)
    if (!prevMcpItem) {
      return
    }
    if (mcpConfig.type && prevMcpItem.type !== mcpConfig.type) {
      needReconnect = true
    } else if (prevMcpItem.type === 'sse') {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      needReconnect = mcpConfig.url && prevMcpItem.url !== mcpConfig.url
    } else if (prevMcpItem.type === 'command') {
      needReconnect =
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-expect-error
        (mcpConfig.command && prevMcpItem.command !== mcpConfig.command) ||
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-expect-error
        (mcpConfig.args && prevMcpItem.args !== mcpConfig.args) ||
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-expect-error
        (mcpConfig.env && prevMcpItem.env !== mcpConfig.env)
    }

    const newConfig = {
      ...prevMcpItem,
      ...mcpConfig,
    } as McpConfig

    const safeConfig = cloneDeep(newConfig) as any
    // 避免存一些不必要的字段到服务端
    try {
      delete safeConfig.state
      delete safeConfig.tools
      delete safeConfig.errorMessage
    } catch (error) {
      console.error(error)
    }

    await updateMcpPlugin(functionId, {
      name: newConfig.name,
      code: newConfig.code,
      is_auto_executed: newConfig.auto_execute,
      config: safeConfig,
      timeout: newConfig.timeout,
    })
    updateMcpListItem(functionId, {
      ...newConfig,
    })
    if (needReconnect) {
      console.log('needReconnect', {
        mcpConfig,
        prevMcpItem,
      })
      updateMcpListItem(functionId, {
        state: 'connecting',
      })
      connectMcp(newConfig)
        .then(async () => {
          const tools = await getMcpTools(newConfig)
          try {
            updateMcpListItem(functionId, {
              tools,
              state: 'success',
              errorMessage: undefined,
            })
            get().generateMcpDescription(functionId, tools)
          } catch (error) {
            updateMcpListItem(functionId, {
              state: 'failed',
              errorMessage:
                error instanceof Error
                  ? error.message
                  : localize('mcp.unknown_error', '未知错误'),
            })
          }
        })
        .catch(error => {
          updateMcpListItem(functionId, {
            state: 'failed',
            errorMessage:
              error instanceof Error
                ? error.message
                : localize('mcp.unknown_error', '未知错误'),
          })
        })
    }
  },
  deleteMcp: async (functionId: string) => {
    const list = get().list
    const currentMcpItem = list.find(item => item.functionId === functionId)
    if (!currentMcpItem) {
      return list
    }
    await disconnectMcp(currentMcpItem).catch(() => {})
    await deleteMcpPlugin(functionId)
    const newList = list.filter(item => item.functionId !== functionId)
    set(() => ({ list: newList }))
    return newList
  },
  connectMcp: async (functionId: string): Promise<void> => {
    const list = get().list

    const mcp = list.find(item => item.functionId === functionId)
    if (!mcp) {
      return Promise.resolve()
    }
    try {
      get().updateMcpListItem(functionId, {
        state: 'connecting',
      })
      await connectMcp(mcp)
      const tools = await getMcpTools(mcp)
      get().updateMcpListItem(functionId, {
        tools,
        state: 'success',
        errorMessage: undefined,
      })
      if (!mcp.description) {
        get().generateMcpDescription(functionId, tools)
      }
    } catch (error) {
      get().updateMcpListItem(functionId, {
        state: 'failed',
        errorMessage:
          error instanceof Error
            ? error.message
            : localize('mcp.unknown_error', '未知错误'),
      })
    }
  },
  generateMcpDescription: async (functionId: string, tools: McpTool[]) => {
    const mcp = get().list.find(item => item.functionId === functionId)
    if (!mcp) {
      return
    }
    get().updateMcpListItem(functionId, {
      descriptionState: 'pending',
    })
    try {
      const descriptionRes = await generateMcpDescription(mcp.name, tools)
      get().updateMcpListItem(functionId, {
        descriptionState: 'success',
        description: {
          introduction: descriptionRes.introduction ?? '',
          tools: descriptionRes.tools ?? [],
          examples: descriptionRes.examples ?? [],
        },
      })
      updateMcpPlugin(functionId, {
        description: JSON.stringify({
          introduction: descriptionRes.introduction ?? '',
          tools: descriptionRes.tools ?? [],
          examples: descriptionRes.examples ?? [],
        }),
      })
    } catch (error) {
      get().updateMcpListItem(functionId, {
        descriptionState: 'failed',
      })
    }
  },
  updateMcpListItem: (id: string, payload: Partial<McpListItem>) => {
    set((state: Store) => {
      return {
        list: state.list.map((subItem: McpListItem) =>
          subItem.functionId === id
            ? ({ ...subItem, ...payload } as McpListItem)
            : subItem,
        ),
      }
    })
  },
}))

if (!import.meta.env.SSR) {
  window.addEventListener('USER_LOGIN_SUCCESS', () => {
    useMcpStore.getState().init()
  })
}
