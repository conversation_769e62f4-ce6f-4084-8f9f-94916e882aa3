import { create } from 'zustand'
import {
  EventActionType,
  EventStatus,
  EventType,
  ToolType,
} from '@apis/mindnote/next-agent-chat.type'
import type {
  Operation,
  TaskStatus,
  Attachment,
  Event,
  TaskArtifact,
} from '@apis/mindnote/next-agent-chat.type'
import { shallow } from 'zustand/shallow'
import { BLANK_CONVERSATION_ID } from '@/components/next-agent/const'
import {
  createTaskArtifactSlice,
  type TaskArtifactSlice,
} from './task-artifact'
import { Tabs, useTaskStore } from './task'

export interface MessageRenderProps {
  action: {
    icon: string
    event_type?: string
    name?: string
    arguments?: string[]
    action_type?: string
  }
  mcpContent?: {
    [key: string]: any
  }
  base?: {
    event_type: EventType
    event_status?: EventStatus
    event_id: string
    tool_output?: Event['content']['tool_output']
    tool_input?: Event['content']['tool_input']
    arguments: string[]
    action_type?: string
    metadata?: Record<string, any>
    plan_step_state?: Event['plan_step_state']
    plan_step_id?: Event['plan_step_id']
  }
  // deprecated start
  tool_input?: Event['content']['tool_input']
  tool_output?: Event['content']['tool_output']
  // deprecated end
  content?: { content: string }
  attachment?: Attachment | Attachment[]
  operation?: Operation
}

type EventRenderProps = Omit<MessageRenderProps, 'onClick'>

interface Step {
  id: string
  title: string
  state: TaskStatus
  events: Array<Event & { renderProps: EventRenderProps }>
  timestamp: string
}

const CONSTANTS = {
  DEFAULT_ICON: 'i-icons-next-agent-prepare',
  PLAN_PREPARE_ID: 'plan_prepare',
  USER_INPUT_PREFIX: 'user_input_',
  TASK_SUMMARY: 'task_summary',
} as const

const CONTENTLESS_EVENTS = new Set([
  EventType.PLAN_PREPARE_TITLE,
  EventType.PLAN_ANALYZE,
])

function isQuestionInputEvent(event: Event): boolean {
  return (
    event.event_type === EventType.USER_INPUT &&
    event.content.action_type === EventActionType.QuestionInput
  )
}

function isFileAttachment(toolOutput: unknown): toolOutput is TaskArtifact {
  return (
    toolOutput !== null &&
    typeof toolOutput === 'object' &&
    !Array.isArray(toolOutput) &&
    'path' in toolOutput &&
    'file_type' in toolOutput &&
    'file_name' in toolOutput
  )
}

function isTaskSummary(event: Event): boolean {
  return (
    event.event_type === EventType.SUMMARY &&
    event.content?.action_type === EventActionType.Summary
  )
}

function isStepSummary(event: Event): boolean {
  return (
    event.event_type === EventType.STEP_SUMMARY &&
    event.content?.action_type === EventActionType.StepSummary
  )
}

function createAttachment(toolOutput: TaskArtifact) {
  return {
    file_type: toolOutput.file_type,
    file_name: toolOutput.file_name,
    file_url: toolOutput.path,
  }
}

const getIconName = (type: string): string => type

function createActionProps(
  event: Event,
  icon: string = CONSTANTS.DEFAULT_ICON,
): EventRenderProps['action'] {
  return {
    icon,
    name: event.content?.action_name || '',
    arguments: event.content?.arguments,
    action_type: event.content?.action_type,
  }
}

function createBaseEventProps(event: Event): Partial<EventRenderProps> {
  const baseProps = {
    base: {
      event_id: event.event_id,
      event_status: event.event_status,
      event_type: event.event_type,
      arguments: event.content?.arguments!,
      action_type: event.content?.action_type,
      tool_output: event?.content?.tool_output,
      tool_input: event?.content?.tool_input,
      plan_step_state: event.plan_step_state,
      plan_step_id: event.plan_step_id,
    },
  }
  return baseProps
}

function createCommonEventProps(event: Event): Partial<EventRenderProps> {
  const baseProps = createBaseEventProps(event)
  const toolOutput = event?.content?.tool_output

  // 处理文件附件 - 统一处理逻辑
  const attachments = []

  // 1. 处理 tool_output 中的附件
  if (isFileAttachment(toolOutput) && toolOutput) {
    attachments.push(createAttachment(toolOutput))
  }

  // 2. 处理 summary 中的附件文件
  if (isTaskSummary(event)) {
    const summaryAttachmentFiles = event?.content?.attachment_files
    if (summaryAttachmentFiles && Array.isArray(summaryAttachmentFiles)) {
      const summaryAttachments = summaryAttachmentFiles.map(file => {
        return createAttachment({
          file_name: file.file_name,
          file_type: file.file_type,
          path: file.path,
        })
      })
      // TODO: 等待后端处理逻辑添加一个总结性的文件 先隐藏当前逻辑
      // attachments.push(...summaryAttachments)

      // TODO:等待后端处理逻辑
      attachments.unshift({
        file_name: '查看此任务所有文件',
        file_type: '',
        path: 123123,
        isSummary: true,
      })
    }
  }

  // 3. 处理 step_summary 中的附件文件
  if (isStepSummary(event)) {
    const summaryAttachmentFiles = event?.content?.attachment_files
    if (summaryAttachmentFiles && Array.isArray(summaryAttachmentFiles)) {
      const summaryAttachments = summaryAttachmentFiles.map(file => {
        return createAttachment({
          file_name: file.file_name,
          file_type: file.file_type,
          path: file.path,
        })
      })
      attachments.push(...summaryAttachments)
    }
  }

  // 3. 设置最终的 attachment 属性
  if (attachments.length > 0) {
    baseProps.attachment =
      attachments.length === 1 ? attachments[0] : attachments
  }

  // 处理内容
  if (!CONTENTLESS_EVENTS.has(event.event_type) && event.content?.content) {
    baseProps.content = { content: event.content.content }
  }

  return baseProps
}

function createEventHandler(
  iconResolver: (event: Event) => string = event =>
    getIconName(event.content?.action_type || event.event_type),
) {
  return (event: Event): EventRenderProps => ({
    action: createActionProps(event, iconResolver(event)),
    ...createCommonEventProps(event),
  })
}

function createToolEventHandler(event: Event): EventRenderProps {
  const { content } = event
  const isBrowseUseTakeover = content.tool_name === ToolType.BrowserUseTakeover

  // TODO: action_type后续需要修改
  const isMcp =
    event.event_type === EventType.TOOL_CALL &&
    (event.content as any)?.action_type === ToolType.MCP
  const baseProps = {
    action: createActionProps(
      event,
      getIconName(content.tool_name || event.event_type),
    ),
    eventType: event.event_type,
  }

  if (isMcp) {
    return {
      ...createBaseEventProps(event),
      ...baseProps,
      mcpContent: {
        metadata: event.content?.metadata,
        status:
          !event.content?.metadata?.is_auto_executed &&
          event.event_status !== EventStatus.SUCCESS &&
          event.event_status !== EventStatus.FAILED
            ? 'ready'
            : event.event_status,
        tool_call_id: event.content?.tool_call_id,
        tool_name: event.content?.tool_name,
        tool_input: event.content?.tool_input,
        tool_output: event.content?.tool_output,
      },
    }
  }

  if (isBrowseUseTakeover) {
    return {
      ...createBaseEventProps(event),
      ...baseProps,
      content: { content: '' },
      operation: {
        operation_type: content.action_type,
        content: content.content || '',
      },
    }
  }

  return {
    ...baseProps,
    ...createCommonEventProps(event),
  }
}

// 事件处理器映射
const EVENT_HANDLERS: Record<EventType, (event: Event) => EventRenderProps> = {
  [EventType.USER_INPUT]: createEventHandler(),
  [EventType.TOOL_CALL]: createToolEventHandler,
  [EventType.TOOL_CALL_RESULT]: createToolEventHandler,
  [EventType.REPLY]: createEventHandler(),
  [EventType.PLAN_PREPARE_TITLE]: createEventHandler(),
  [EventType.PLAN_ANALYZE]: createEventHandler(),
  [EventType.READ_RECORDING]: createEventHandler(),
  [EventType.STEP_TITLE]: createEventHandler(),
  [EventType.THINK]: createEventHandler(),
  [EventType.TEXT]: createEventHandler(),
  [EventType.ASK_USER]: createEventHandler(),
  [EventType.STEP_SUMMARY]: createEventHandler(),
  [EventType.SUMMARY]: createEventHandler(),
}

function getEventRenderProps(event: Event): EventRenderProps {
  const handler = EVENT_HANDLERS[event.event_type]

  if (handler) {
    return handler(event)
  }

  // 默认处理器
  return {
    action: {
      icon: getIconName(event.content?.action_type || event.event_type),
      action_type: event.content?.action_type || '',
      name: event.content?.action_name || '',
    },
    ...createCommonEventProps(event),
  }
}

function createStep(
  id: string,
  event: Event,
  eventWithProps: Event & { renderProps: EventRenderProps },
): Step {
  return {
    id,
    title: event.plan_step_title || '',
    state: event.plan_step_state || '',
    events: [eventWithProps],
    timestamp: event.timestamp,
  }
}

function createUserInputStep(
  event: Event,
  eventWithProps: Event & { renderProps: EventRenderProps },
) {
  const stepId = `${CONSTANTS.USER_INPUT_PREFIX}${event.event_id}`
  return createStep(stepId, event, eventWithProps)
}

function createTaskSummaryStep(
  event: Event,
  eventWithProps: Event & { renderProps: EventRenderProps },
) {
  const stepId = `${CONSTANTS.TASK_SUMMARY}${event.event_id}`
  return createStep(stepId, event, eventWithProps)
}

function fileCreated(event: Event): TaskArtifact | null {
  // 处理 CreateFile 事件
  if (
    event.event_status === EventStatus.SUCCESS &&
    event.content?.tool_name === ToolType.CreateFile
  ) {
    const output =
      typeof event.content.tool_output === 'object'
        ? (event.content.tool_output as TaskArtifact)
        : null
    if (output) {
      return output
    }
  }

  // 处理 StepSummary 事件中的附件文件
  if (
    event.event_status === EventStatus.SUCCESS &&
    event.content?.action_type === EventActionType.StepSummary &&
    event.content?.attachment_files &&
    Array.isArray(event.content.attachment_files) &&
    event.content.attachment_files.length > 0
  ) {
    // 返回第一个附件文件作为任务产物
    // 如果需要支持多个文件，可能需要修改返回类型
    const firstFile = event.content.attachment_files[0]
    return {
      file_name: firstFile.file_name,
      file_type: firstFile.file_type,
      path: firstFile.path,
    }
  }

  return null
}

interface TaskEventStore {
  taskId?: string
  setTaskId: (taskId?: string) => void
  events: Event[]
  normalizedEvents: Step[]
  append: (event: Event) => void
  clear: VoidFunction
  setEvents: (events: Event[] | ((prevEvents: Event[]) => Event[])) => void
}

const initialState = {
  events: [],
  normalizedEvents: [],
}

export const useTaskEvent = create<TaskEventStore & TaskArtifactSlice>(
  (set, get, ...rest) => ({
    ...initialState,

    ...createTaskArtifactSlice(set, get, ...rest),

    taskId: BLANK_CONVERSATION_ID,

    setTaskId: taskId => {
      set({ taskId })
    },

    append: event => {
      if (isQuestionInputEvent(event)) {
        const step = createUserInputStep(event, {
          ...event,
          renderProps: getEventRenderProps(event),
        })
        set(state => ({
          events: [...state.events, event],
          normalizedEvents: [...state.normalizedEvents, step],
        }))

        return
      }

      if (isTaskSummary(event)) {
        const step = createTaskSummaryStep(event, {
          ...event,
          renderProps: getEventRenderProps(event),
        })
        set(state => ({
          events: [...state.events, event],
          normalizedEvents: [...state.normalizedEvents, step],
        }))

        return
      }

      const normalizedEvents = get().normalizedEvents
      const id = event.plan_step_id || CONSTANTS.PLAN_PREPARE_ID
      const existedIndex = normalizedEvents.findIndex(el => el.id === id)

      // 如果有文件产出，自动打开 panel，打开对应文件
      const toBeFrontArtifact = fileCreated(event)

      if (existedIndex === -1) {
        const step = createStep(id, event, {
          ...event,
          renderProps: getEventRenderProps(event),
        })

        set(state => ({
          events: [...state.events, event],
          normalizedEvents: [...state.normalizedEvents, step],
        }))

        if (toBeFrontArtifact) {
          useTaskStore.setState({
            panelVisible: true,
            activeTab: Tabs.Computer,
          })
        }

        return
      }

      const eventWithProps = {
        ...event,
        renderProps: getEventRenderProps(event),
      }
      const step = normalizedEvents[existedIndex]
      step.state = event.plan_step_state || step.state
      const index = step.events.findIndex(
        el => el.event_id === eventWithProps.event_id,
      )
      if (index === -1) {
        step.events.push(eventWithProps)
      } else {
        step.events[index] = eventWithProps
      }
      step.events = step.events.slice()

      set(state => ({
        events: [...state.events, event],
        normalizedEvents: [...state.normalizedEvents],
      }))

      if (toBeFrontArtifact) {
        useTaskStore.setState({
          panelVisible: true,
          activeTab: Tabs.Computer,
        })
      }
    },

    clear: () => {
      set(initialState)
    },

    setEvents: events => {
      const _events =
        typeof events === 'function' ? events(get().events) : events

      const normalizedEvents: Step[] = []

      for (const event of _events) {
        if (isQuestionInputEvent(event)) {
          const step = createUserInputStep(event, {
            ...event,
            renderProps: getEventRenderProps(event),
          })
          normalizedEvents.push(step)
          continue
        }

        if (isTaskSummary(event)) {
          const step = createTaskSummaryStep(event, {
            ...event,
            renderProps: getEventRenderProps(event),
          })
          normalizedEvents.push(step)
          continue
        }

        const id = event.plan_step_id || CONSTANTS.PLAN_PREPARE_ID
        const existedIndex = normalizedEvents.findIndex(el => el.id === id)

        if (existedIndex === -1) {
          const step = createStep(id, event, {
            ...event,
            renderProps: getEventRenderProps(event),
          })
          normalizedEvents.push(step)
          continue
        }

        const eventWithProps = {
          ...event,
          renderProps: getEventRenderProps(event),
        }
        const step = normalizedEvents[existedIndex]
        step.state = event.plan_step_state || step.state
        const index = step.events.findIndex(
          el => el.event_id === event.event_id,
        )
        if (index === -1) {
          step.events.push(eventWithProps)
        } else {
          step.events[index] = eventWithProps
        }
      }
      set({ events: _events, normalizedEvents })
    },
  }),
)

export function useTaskEventList() {
  const normalizedEvents = useTaskEvent(state => state.normalizedEvents)
  return normalizedEvents
}

export function useLatestTaskEvent() {
  const events = useTaskEvent(state => state.events)
  return events.length > 0 ? events[events.length - 1] : null
}

export function useTaskArtifact() {
  const open = useTaskEvent(state => state.openArtifact)

  const close = useTaskEvent(state => state.closeArtifact)

  const clear = useTaskEvent(state => state.clearArtifacts)

  const add = useTaskEvent(state => state.addArtifacts)

  return { open, close, clear, add }
}

// export function useTaskArtifactCollection() {
//   const events = useTaskEvent(state => state.events)

//   const add = useTaskEvent(state => state.addArtifacts)

//   const clear = useTaskEvent(state => state.clearArtifacts)

//   useEffect(() => {
//     clear()

//     const newArtifacts: TaskArtifact[] = []

//     for (const event of events) {
//       const artifact = fileCreated(event)
//       if (artifact) {
//         newArtifacts.push(artifact)
//       }
//     }

//     if (newArtifacts.length > 0) {
//       add(...newArtifacts)
//     }
//   }, [events])
// }

export function useTaskArtifactFront() {
  return useTaskEvent(state => state.frontArtifact)
}

export function useTaskArtifactList() {
  return useTaskEvent(state => state.getArtifactsList(), shallow)
}

export function useTask() {
  const taskId = useTaskEvent(state => state.taskId)
  return taskId
}
