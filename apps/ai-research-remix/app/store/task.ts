import { create } from 'zustand'

export const Tabs = {
  Computer: 'computer',
  Artifact: 'artifact',
} as const

interface TaskStore {
  panelVisible: boolean
  showPanel: VoidFunction
  hidePanel: VoidFunction
  togglePanel: VoidFunction
  activeTab: (typeof Tabs)[keyof typeof Tabs]
  setActiveTab: (activeTab: (typeof Tabs)[keyof typeof Tabs]) => void
}

const initialState = {
  panelVisible: false,
  activeTab: Tabs.Computer,
}

export const useTaskStore = create<TaskStore>(set => ({
  ...initialState,

  showPanel: () => set({ panelVisible: true }),
  hidePanel: () => set({ panelVisible: false }),
  togglePanel: () => set(state => ({ panelVisible: !state.panelVisible })),

  setActiveTab: activeTab => set({ activeTab }),
}))

export function useTaskPanel() {
  const panelVisible = useTaskStore(state => state.panelVisible)
  const showPanel = useTaskStore(state => state.showPanel)
  const hidePanel = useTaskStore(state => state.hidePanel)
  const togglePanel = useTaskStore(state => state.togglePanel)

  const activeTab = useTaskStore(state => state.activeTab)
  const setActiveTab = useTaskStore(state => state.setActiveTab)

  return {
    panelVisible,
    showPanel,
    hidePanel,
    togglePanel,
    activeTab,
    setActiveTab,
  }
}
