import type { TaskOfMemory } from '@apis/mindnote/memories'
import { memo } from 'react'

interface MemoryTaskProps extends TaskOfMemory {
  onClick: VoidFunction
}

function InnerMemoryTask({ step, name, experience, onClick }: MemoryTaskProps) {
  return (
    <div
      className='p-16px rounded-8px bg-white border border-solid border-line border-op-80 mb-24px transition-colors cursor-pointer hover:border-#7b66ee/30'
      onClick={onClick}
    >
      <p className='text-#8d8d99 text-14px/16px'>
        #子任务
        <span className='ml-2px'>{step}</span>
      </p>
      <p className='mt-10px text-18px/45px font-semibold'>{name}</p>
      <p className='py-10px text-14px/26px whitespace-pre-wrap break-words [word-break:break-word]'>
        {experience}
      </p>
    </div>
  )
}

export const MemoryTask = memo(InnerMemoryTask)
