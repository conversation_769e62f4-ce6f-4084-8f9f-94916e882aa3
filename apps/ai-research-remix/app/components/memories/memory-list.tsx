import { memo, useRef } from 'react'
import { IconButton } from '../base/icon'
import { ScrollView } from '../base/scroll-view'
import { useMemories } from '@/store/memories'
import { MemorySummary } from './memory-summary'

function InnerMemoryList() {
  const memories = useMemories(state => state.memories)

  const draft = useMemories(state => state.draft)

  const memoryId = useMemories(state => state.selectedMemoryId)

  // const [showSearch, setShowSearch] = useState(false)

  // const [search, setSearch] = useState('')

  // const [compositing, handleComposition] = useCompositing(text => {
  //   setSearch(text)
  //   // onSearch(text)
  // })

  // const handleKeyDown = useMemoizedFn((event: KeyboardEvent) => {
  //   if (compositing.current) return false
  //   if (event.code === 'Enter') {
  //     event.preventDefault()
  //     // onSearch(search)
  //   }
  // })

  // const handleSearch = useMemoizedFn((event: ChangeEvent) => {
  //   setSearch((event.target as any).value)
  //   if (compositing.current) return false
  //   // onSearch((event.target as any).value)
  // })

  const scrollRoot = useRef<HTMLDivElement>(null)

  return (
    <div className='w-300px h-full overflow-hidden flex flex-col bg-#f7f7f9'>
      <div className='font-semibold pt-16px pl-16px pr-16px pb-4px flex gap-8px'>
        <div className='mr-auto text-truncate text-20px/36px'>经验</div>
        {/* <Input
          className={cn('font-400 flex-none', {
            'w-210px!': showSearch || search,
            'w-36px! cursor-pointer bg-transparent! hover:bg-bg-hover/8! b-none! [&_.ant-input-prefix]:pl-1px!':
              !showSearch && !search,
          })}
          placeholder='标题/内容'
          variant='filled'
          prefix={
            <Icon
              icon='i-icons-search'
              size='size-18px'
              className='ml-[-2px]'
            />
          }
          value={search}
          onChange={handleSearch}
          onKeyDown={handleKeyDown}
          onFocus={() => setShowSearch(true)}
          onBlur={() => setShowSearch(false)}
          onCompositionStart={handleComposition}
          onCompositionEnd={handleComposition}
          allowClear
        /> */}
        <IconButton
          icon='i-icons-add'
          iconSize='size-18px'
          size='size-36px'
          onClick={draft}
        />
      </div>
      <div className='flex-1 overflow-hidden' ref={scrollRoot}>
        <ScrollView h={false}>
          <div className='size-full p-16px pt-0'>
            {memories?.map((el, index) => (
              <MemorySummary
                key={el.id}
                {...el}
                index={index}
                selected={memoryId === el.id}
              />
            ))}
          </div>

          {/* {hasMore && (
            <div className='flex-center h-36px' ref={nextSentinelRef}>
              <Spin />
            </div>
          )} */}
        </ScrollView>
      </div>
    </div>
  )
}

export const MemoryList = memo(InnerMemoryList)
