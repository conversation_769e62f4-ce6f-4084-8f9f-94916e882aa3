import type { Memory } from '@apis/mindnote/memories'
import { cn } from '@bty/util'
import { memo, useRef } from 'react'
import dayjs from 'dayjs'
import { useFadeIn } from '@/hooks/use-fade-in'
import { useMemories } from '@/store/memories'

interface MemorySummaryProps
  extends Pick<Memory, 'id' | 'name' | 'describe' | 'created_at'> {
  index: number
  selected?: boolean
}

function InnerMemorySummary({
  id,
  name,
  describe,
  created_at,
  index,
  selected,
}: MemorySummaryProps) {
  const select = useMemories(state => state.select)

  const ref = useRef<HTMLDivElement>(null)

  useFadeIn(ref, {
    delay: Math.min((index % 10) * 100, 1500),
    fill: 'forwards',
  })

  return (
    <div
      ref={ref}
      className={cn(
        'mt-8px bg-white rounded-8px border border-solid border-line border-op-60 transition-colors overflow-hidden cursor-pointer',
        {
          'border-#7b61ff bg-primary/8': selected,
        },
      )}
      onClick={() => select(id)}
    >
      <div className='h-88px flex flex-col justify-between p-16px'>
        <p className='text-16px/24px'>{name}</p>
        <p className='truncate text-#8d8d99'>{describe}</p>
      </div>
      <div className='flex items-center bg-[rgba(98,105,153,0.03)] h-28px px-16px text-12px/16px text-#8d8d99/80'>
        <span className='ml-auto'>
          {dayjs(created_at).format('YYYY-MM-DD HH:mm')}
        </span>
      </div>
    </div>
  )
}

export const MemorySummary = memo(InnerMemorySummary)
