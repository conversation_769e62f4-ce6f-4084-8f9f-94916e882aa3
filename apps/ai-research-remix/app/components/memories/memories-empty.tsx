import { Button } from 'antd'
import { basePath } from '@/const'
import { useMemories } from '@/store/memories'
import { Image } from '../base/image'
import { Icon } from '../base/icon'

export function MemoriesEmpty() {
  const draft = useMemories(state => state.draft)

  return (
    <div className='w-full text-center'>
      <h1 className='text-20px/24px font-medium pl-16px py-22px text-start'>
        经验
      </h1>
      <Image
        className='w-150px mt-192px object-contain mx-auto'
        src={`${basePath}/sub-empty.png`}
      />
      <p className='text-#8d8d99 mt-24px'>暂无任何经验</p>
      <Button
        className='mt-36px w-124px'
        type='primary'
        icon={<Icon icon='i-icons-add' />}
        onClick={draft}
      >
        添加经验
      </Button>
    </div>
  )
}
