import { Button, Dropdown, Form, message } from 'antd'
import { memo, useCallback, useRef, useState } from 'react'
import dayjs from 'dayjs'
import { Controller } from '@bty/components'
import type { Memory } from '@apis/mindnote/memories'
import { Icon, IconButton } from '../base/icon'
import { isDraftMemory, useFrontMemory, useMemories } from '@/store/memories'

interface MemoryEditorHeaderProps {
  isEditing: boolean
  onEdit: (isEditing: boolean) => void
  onSubmit: () => Promise<void>
  onRestore: VoidFunction
}

function InnerMemoryEditorHeader({
  isEditing,
  onEdit,
  onSubmit,
  onRestore,
}: MemoryEditorHeaderProps) {
  const memory = useFrontMemory()

  const updateMemory = useMemories(state => state.update)

  const removeMemory = useMemories(state => state.remove)

  const isDraft = isDraftMemory(memory as Memory)

  const form = Form.useFormInstance()

  const oldName = useRef<string>()

  const handleFocus = useCallback(() => {
    oldName.current = form.getFieldValue('name')
  }, [])

  const [loading, setLoading] = useState(false)

  const handleBlur = useCallback(
    async (event: React.FocusEvent<HTMLInputElement, Element>) => {
      // 如果是草稿，最终的更新由保存按钮执行
      if (!memory || isDraft) {
        return
      }

      const name = event.target.value.trim()

      if (name === oldName.current) {
        return
      }

      if (name.length === 0) {
        // sb 必须手动移除 error
        form.setFields([
          { name: 'name', value: oldName.current, errors: undefined },
        ])
        return
      }

      try {
        await updateMemory({ name })
      } catch (error) {
        message.error('保存失败')
      }
    },
    [memory, isDraft],
  )

  const handleSubmit = () => {
    const p = onSubmit()
    if (p instanceof Promise) {
      setLoading(true)
      p.finally(() => {
        setLoading(false)
        onEdit(false)
      })
    }
  }

  return (
    <div className='shrink-0 px-20px pt-24px flex items-center'>
      <div className='flex-1'>
        <Controller.Overrides mb='10px'>
          <Controller.Input
            overrides={{
              root: 'text-22px px-0 font-medium [-webkit-box-shadow:0_0_0_30px_white_inset]',
            }}
            name='name'
            rules={[{ required: true, message: '请输入标题' }]}
            showValidation={false}
            variant='borderless'
            placeholder='请输入标题'
            allowClear={false}
            autoFocus={isEditing}
            onFocus={handleFocus}
            onBlur={handleBlur}
          />
        </Controller.Overrides>
        <div className='text-14px/16px flex items-center gap-20px text-nowrap'>
          <span className='text-#8D8D99/80'>
            {dayjs().format('MM-DD HH:mm')}
          </span>
        </div>
      </div>

      {isEditing && !loading ? (
        <Button
          className='w-80px'
          onClick={() => {
            isDraft ? removeMemory() : onRestore()
            onEdit(false)
          }}
        >
          取消
        </Button>
      ) : null}
      <Button
        className='w-80px ml-12px'
        type='primary'
        loading={loading}
        onClick={isEditing ? handleSubmit : () => onEdit(true)}
      >
        {isEditing ? '保存' : '编辑'}
      </Button>

      {isDraft ? null : (
        <Dropdown
          placement='bottomRight'
          menu={{
            items: [
              {
                key: 'delete',
                label: (
                  <div className='w-80px flex items-center gap-4px text-error'>
                    <Icon icon='i-icons-delete' />
                    删除
                  </div>
                ),
              },
            ],
            onClick: () => {
              removeMemory()
            },
          }}
        >
          <IconButton
            className='ml-8px'
            icon='i-icons-more'
            iconSize='size-18px'
            size='size-36px'
          />
        </Dropdown>
      )}
    </div>
  )
}

export const MemoryEditorHeader = memo(InnerMemoryEditorHeader)
