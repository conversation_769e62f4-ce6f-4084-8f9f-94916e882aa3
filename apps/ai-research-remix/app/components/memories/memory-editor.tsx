import { memo, useCallback, useEffect, useState, useRef } from 'react'
import { getMemory } from '@apis/mindnote/memories'
import type { Memory, TaskOfMemory } from '@apis/mindnote/memories'
import { useUpdateEffect } from 'ahooks'
import { ProForm } from '@bty/components'
import { Icon } from '../base/icon'
import { isDraftMemory, useMemories } from '@/store/memories'
import { MemoryEditorHeader } from './memory-editor-header'
import { MemoryTaskEditor } from './memory-task-editor'
import { MemoryTask } from './memory-task'

interface MemoryEditorValues {
  name: string
  task_structure: TaskOfMemory[]
}

const initialValues: MemoryEditorValues = {
  name: '',
  task_structure: [
    {
      step: 1,
      name: '',
      experience: '',
    },
  ],
}

function InnerMemoryEditor() {
  const memoryId = useMemories(state => state.selectedMemoryId)

  const createMemory = useMemories(state => state.create)

  const updateMemory = useMemories(state => state.update)

  const isDraft = isDraftMemory({ id: memoryId } as Memory)

  const [isEditing, setIsEditing] = useState(isDraft)

  const [hasError, setHasError] = useState(false)

  const originalDataRef = useRef<MemoryEditorValues | null>(null)

  const [form] = ProForm.useForm<MemoryEditorValues>()

  useEffect(() => {
    if (!memoryId) {
      return
    }

    if (isDraft) {
      const data = initialValues
      form.setFieldsValue(data)
      originalDataRef.current = data
      return
    }

    getMemory(memoryId).then(res => {
      const data = {
        name: res.name,
        task_structure: res.task_structure || [],
      }
      form.setFieldsValue(data)
      originalDataRef.current = data // 缓存原始数据
    })
  }, [memoryId, isDraft])

  useUpdateEffect(() => {
    setIsEditing(isDraft)
  }, [isDraft])

  const noTask = (error: any) => {
    return !!error.errorFields.find((el: any) =>
      el.name.includes('task_structure'),
    )
  }

  const handleSubmit = useCallback(async () => {
    try {
      const values = (await form.validateFields()) as MemoryEditorValues

      const data = {
        name: values.name,
        task_structure: values.task_structure
          .filter(el => el.name.trim() || el.experience.trim())
          .map((el, index) => ({
            ...el,
            step: index + 1,
          })),
      }
      form.setFieldsValue(data)
      await (isDraft ? createMemory : updateMemory)(data)

      setHasError(false)
    } catch (error) {
      const noTaskError = noTask(error)
      setHasError(noTaskError)
    }
  }, [isDraft])

  const handleEditing = useCallback(() => {
    setIsEditing(true)
  }, [])

  const handleRestore = useCallback(() => {
    if (!originalDataRef.current) {
      return
    }

    form.setFieldsValue(originalDataRef.current)
  }, [])

  return (
    <div className='flex-1 h-full overflow-hidden bg-white'>
      <ProForm
        className='flex flex-col h-full'
        form={form}
        layout='vertical'
        onValuesChange={async value => {
          if ('task_structure' in value) {
            try {
              const _ = await form.validateFields(['task_structure'], {
                validateOnly: true,
              })
              setHasError(false)
            } catch (error) {
              const noTaskError = noTask(error)
              setHasError(noTaskError)
            }
          }
        }}
      >
        <MemoryEditorHeader
          isEditing={isEditing}
          onEdit={setIsEditing}
          onSubmit={handleSubmit}
          onRestore={handleRestore}
        />
        <div className='p-20px pt-24px overflow-y-auto flex-1'>
          {isEditing ? (
            <ProForm.List
              name='task_structure'
              rules={[
                {
                  validator: (_, value: TaskOfMemory[]) => {
                    const valid = value.filter(
                      el => el.name.trim() || el.experience.trim(),
                    )
                    return valid.length === 0
                      ? Promise.reject(new Error('请添加子任务'))
                      : Promise.resolve()
                  },
                },
              ]}
            >
              {(fields, { add, remove }) => (
                <>
                  {fields.map((field, index, self) => (
                    <MemoryTaskEditor
                      key={field.key}
                      className={hasError ? '!border-[rgb(255,82,25)]' : ''}
                      name={field.name}
                      step={index + 1}
                      remove={self.length > 1 ? remove : undefined}
                    />
                  ))}
                  <button
                    className='w-full flex items-center h-56px px-16px rounded-8px bg-white border border-solid border-line border-op-80 transition-colors hover:border-#7b66ee/30'
                    onClick={() => {
                      add({ name: '', experience: '' })
                    }}
                  >
                    <div className='w-24px h-24px flex items-center justify-center'>
                      <Icon icon='i-icons-add' size='size-16px' />
                    </div>
                    <span className='text-#3f3f44 ml-12px'>添加子任务</span>
                  </button>
                </>
              )}
            </ProForm.List>
          ) : (
            <ProForm.Item noStyle dependencies={[]}>
              {({ getFieldValue }) =>
                (getFieldValue('task_structure') as TaskOfMemory[])?.map(
                  (el, index) => (
                    <MemoryTask key={index} {...el} onClick={handleEditing} />
                  ),
                )
              }
            </ProForm.Item>
          )}
        </div>
      </ProForm>
    </div>
  )
}

export const MemoryEditor = memo(InnerMemoryEditor)
