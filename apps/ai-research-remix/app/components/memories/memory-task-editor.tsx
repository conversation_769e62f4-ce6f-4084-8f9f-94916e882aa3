import { Controller } from '@bty/components'
import { cn } from '@bty/util'
import { useFlip } from '@bty/hooks'
import { useEffect, useState } from 'react'
import { Icon } from '../base/icon'

interface MemoryTaskEditorProps {
  className?: string
  name: number
  step: number
  remove?: (name: number) => void
}

export function MemoryTaskEditor({
  className,
  name,
  step,
  remove,
}: MemoryTaskEditorProps) {
  const [mounted, setMounted] = useState(false)

  // 使用 useFlip 钩子来实现高度动画
  const ref = useFlip([mounted], {
    duration: 200,
    easing: 'ease-in-out',
    dimensions: 'height',
  })

  // 组件挂载时设置 mounted 为 true 触发进入动画
  useEffect(() => {
    // 延迟一帧设置 mounted，确保初始高度为 0
    requestAnimationFrame(() => {
      setMounted(true)
    })
  }, [])

  return (
    <div
      ref={ref}
      className={cn(
        'relative p-16px rounded-8px bg-white border border-solid border-[rgba(225,225,229,0.8)] mb-24px has-[.delete-btn:hover]:border-#7b61ff transition-colors hover:border-#7b66ee/30',
        mounted ? '' : 'h-0 overflow-hidden',
        className,
      )}
    >
      <Controller.Overrides mb='12px'>
        <Controller.Input
          name={[name, 'name']}
          label={
            <span className='text-#8d8d99'>
              子任务<span className='ml-2px'>{step}</span>
            </span>
          }
          placeholder='请输入子任务'
        />
      </Controller.Overrides>
      <Controller.Overrides mb={0}>
        <Controller.Textarea
          overrides={{ textarea: 'px-[9px] py-[9px]' }}
          name={[name, 'experience']}
          label={<span className='text-#8d8d99'>经验</span>}
          placeholder='请输入经验'
          autoSize={{ minRows: 4, maxRows: 10 }}
        />
      </Controller.Overrides>
      {remove ? (
        <button
          className='delete-btn absolute -top-10px -right-10px w-20px h-20px rounded-full inline-flex items-center justify-center text-#8d8d99 bg-#e0e1eb hover:bg-#ff5219 hover:text-white transition-colors after:content-[""] after:absolute after:-inset-6px after:rounded-full'
          onClick={() => remove(name)}
        >
          <Icon icon='i-icons-close' size='size-12px' />
        </button>
      ) : null}
    </div>
  )
}
