import './index.scss'

import { memo, type FC } from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './header'
import { ConversationList } from './conversation'
import { NextAgentMessageInput } from './message-input'
import { NextAgentMessageList } from './message-list'

export interface AskChatProps {
  autofocus?: boolean
  renders?: {
    header?: FC | boolean
    messageList?: FC | boolean
  }
}

export const AskChat = memo<AskChatProps>(props => {
  const { autofocus } = props

  return (
    <div className='chat flex-1 min-w-0 size-full flex flex-col of-hidden'>
      <ChatHeader />
      <div className='flex-1 size-full flex of-hidden'>
        <ConversationList />
        <div className='chat-container pb-24px px-24px flex-1 h-full flex flex-col justify-center items-center overflow-hidden'>
          <NextAgentMessageList />
          <div className='w-full max-w-960px min-w-200px shrink-0'>
            <NextAgentMessageInput autofocus={autofocus} />
          </div>
        </div>
      </div>
    </div>
  )
})
