import React from 'react'
import { Console } from '@bty/components'
import { TaskOutputLayout } from './task-output-layout'
import { TaskLoading } from './task-loading'

export const TaskTerminal = React.memo(({ command }: { command: string }) => {
  if (!command) {
    return <TaskLoading />
  }

  return (
    <TaskOutputLayout className='mt-20px flex-1'>
      <Console
        overrides={{
          root: 'px-12px py-8px h-[calc(100%-44px)] overflow-scroll',
        }}
        stdout={command}
      />
    </TaskOutputLayout>
  )
})
