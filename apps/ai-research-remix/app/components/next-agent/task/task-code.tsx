import { Palette } from '@bty/components'
import React, { useEffect, useState } from 'react'
import { getFileUrl } from '@apis/mindnote/next-agent-chat'
import { useTask } from '@/store/task-event'
import { TaskOutputLayout } from './task-output-layout'
import { TaskLoading } from './task-loading'

interface TaskCodeProps {
  lang: string
  name: string
  path?: string
  content?: string
}

export const supportedLangs = ['py', 'txt', 'sh', 'js', 'css', 'json']

export const TaskCode = React.memo(
  ({ lang, name, path, content }: TaskCodeProps) => {
    const taskId = useTask()

    const [url, setUrl] = useState<string>()

    useEffect(() => {
      if (path) {
        getFileUrl(taskId!, path).then(setUrl)
      }
    }, [taskId, path])

    if (!url && !content) {
      return <TaskLoading />
    }

    return (
      <TaskOutputLayout className='mt-20px flex-1' title={name}>
        <div className='p-24px h-[calc(100%-44px)] overflow-auto'>
          <Palette code={content} url={url} lang={lang} remote={!!url} />
        </div>
      </TaskOutputLayout>
    )
  },
)
