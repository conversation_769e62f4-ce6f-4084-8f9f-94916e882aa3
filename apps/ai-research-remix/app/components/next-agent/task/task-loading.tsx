import { cn } from '@bty/util'
import { basePath } from '@/const'
import { Image } from '../../base/image'

export function TaskLoading({ className }: { className?: string }) {
  return (
    <div className={cn('my-auto text-center', className)}>
      <Image
        className='size-80px mx-auto'
        src={`${basePath}/collection-loading.gif`}
      />
      <p className='text-12px/16px text-#8d8d99/80 mt-12px'>正在运行...</p>
    </div>
  )
}
