import { useEffect, useMemo, useState } from 'react'
import { But<PERSON> } from '@bty/components'
import NiceModal from '@ebay/nice-modal-react'
import { ToolType } from '@apis/mindnote/next-agent-chat.type'
import { Modal } from '../../base/modal'
import { Icon, IconButton } from '../../base/icon'

interface TaskComputerUseModalProps {
  owner?: 'human' | 'ai'
  taskId?: string
  callback?: (params: { message_type: string }) => void
}

export const TaskComputerUseModal = NiceModal.create(
  ({
    owner: initialOwner = 'ai',
    taskId,
    callback,
  }: TaskComputerUseModalProps) => {
    const { visible, hide, remove } = NiceModal.useModal()

    const [owner, setOwner] = useState<'human' | 'ai'>(initialOwner)

    useEffect(() => {
      setOwner(initialOwner)
    }, [initialOwner])

    const stateUI = useMemo(
      () =>
        owner === 'human' ? (
          <div className='h-26px px-10px flex items-center gap-4px leading-26px rounded-8px bg-#7b67ee/15 text-14px text-#7b61ff mr-auto'>
            <Icon className='i-icons-ai-pause !size-14px' />
            用户接管，AI等待中
          </div>
        ) : owner === 'ai' ? (
          <div className='h-26px leading-26px rounded-8px text-14px text-#3f3f44 mr-auto gradient-border before:gradient-border-bg after:gradient-border-mask overflow-hidden select-none'>
            <div className='flex items-center gap-4px h-full px-10px relative z-1'>
              <Icon className='i-icons-ai-running !size-14px' />
              AI自动运行中
            </div>
          </div>
        ) : null,
      [owner],
    )

    const waitingHelpUI = useMemo(
      () =>
        owner === 'human' && (
          <>
            <span className='text-14px text-#3f3f44'></span>
            <Button
              className='mx-12px'
              onClick={() => {
                callback?.({
                  message_type: ToolType.JumpOver,
                })
                hide()
              }}
            >
              尝试跳过
            </Button>
          </>
        ),
      [owner],
    )

    return (
      <Modal
        open={visible}
        styles={{ body: { padding: 0 } }}
        width='74vw'
        className='min-w-1308px max-w-1550px'
        title={null}
        footer={null}
        closeIcon={null}
        afterClose={remove}
        destroyOnClose
      >
        <div className='h-60px px-16px flex items-center border-b-1 border-solid border-#e1e1e5/60'>
          <span className='text-16px font-500 text-font mr-10px'>远程电脑</span>
          {stateUI}
          {waitingHelpUI}
          {owner === 'human' && (
            <Button
              type='primary'
              onClick={() => {
                callback?.({
                  message_type: ToolType.TakeOverExit,
                })
                hide()
                setOwner('ai')
              }}
            >
              退出接管
            </Button>
          )}
          <span className='mx-12px h-20px border-l-1 border-solid border-#e1e1e5/60' />
          <IconButton
            className='w-32px h-32px'
            icon='i-icons-zoom-out'
            onClick={() => {
              callback?.({
                message_type: ToolType.TakeOverExit,
              })
              setOwner('ai')
              hide()
            }}
          />
        </div>
        <div className='p-16px'>
          <div className='shadow-[inset_0px_0px_20px_0px_rgba(83,106,255,0.8)] rounded-10px overflow-hidden'>
            <div className='max-w-900px mx-auto'>
              <div className='h-0 pt-80% relative'>
                <iframe
                  className='absolute inset-16px w-[calc(100%-32px)] h-[calc(100%-32px)] rounded-10px border-none'
                  src={`/vnc?mode=control&taskId=${taskId}`}
                  loading='lazy'
                />
              </div>
            </div>
          </div>
        </div>
      </Modal>
    )
  },
)
