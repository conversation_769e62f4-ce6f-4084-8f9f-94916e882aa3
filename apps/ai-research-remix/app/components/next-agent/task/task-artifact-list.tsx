import React from 'react'
import type { TaskArtifact } from '@apis/mindnote/next-agent-chat.type'
import { getFileIconByType } from '@/util/file'

interface TaskArtifactListProps {
  artifacts?: TaskArtifact[]
  onClick?: (artifact: TaskArtifact) => void
}

function InnerTaskArtifactList({ artifacts, onClick }: TaskArtifactListProps) {
  return (
    <div>
      {artifacts?.map(artifact => (
        <div
          key={artifact.path}
          className='not-first:mt-4px h-48px px-8px flex items-center rounded-10px cursor-pointer hover:bg-white/80'
          onClick={() => onClick?.(artifact)}
        >
          {React.cloneElement(
            getFileIconByType(artifact.file_type) as React.ReactElement,
            {
              className: 'size-32px shrink-0',
            },
          )}
          <span className='text-14px text-font ml-4px truncate'>
            {artifact.file_name}
          </span>
        </div>
      ))}
    </div>
  )
}

export const TaskArtifactList = React.memo(InnerTaskArtifactList)
