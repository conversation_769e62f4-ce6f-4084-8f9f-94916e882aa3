import { cn } from '@bty/util'
import React from 'react'
import { IconButton } from '../../base/icon'

export const Action = {
  Copy: 'copy',
  CopyLink: 'copy-link',
  Download: 'download',
  OpenLink: 'open-link',
  Fullscreen: 'zoom-in',
} as const

function getActionIcon(action: ActionType) {
  switch (action) {
    case Action.Copy:
      return 'i-icons-copy'
    case Action.CopyLink:
      return 'i-icons-copy-link'
    case Action.OpenLink:
      return 'i-icons-open-link'
    case Action.Download:
      return 'i-icons-download'
    case Action.Fullscreen:
      return 'i-icons-zoom-in'
    default:
      return ''
  }
}

type ActionType = (typeof Action)[keyof typeof Action]

interface TaskOutputLayoutProps extends React.PropsWithChildren {
  className?: string
  startContent?: React.ReactNode
  title?: string
  actions?: (ActionType | React.ReactElement)[]
  onAction?: (action: ActionType) => void
}

export function TaskOutputLayout({
  className,
  startContent,
  title,
  actions,
  onAction,
  children,
}: TaskOutputLayoutProps) {
  return (
    <div
      className={cn(
        className,
        'mb-auto rounded-12px bg-#f3f3f5 overflow-hidden',
      )}
    >
      <div className='h-44px px-16px flex items-center gap-8px border-b-1 border-solid border-#e1e1e5/80'>
        {startContent}
        <span className='text-14px text-#3f3f44 mr-auto truncate'>{title}</span>
        {actions?.map(action =>
          typeof action === 'string' ? (
            <IconButton
              key={action}
              className='text-#8d8d99'
              icon={getActionIcon(action)}
              iconSize='size-14px'
              onClick={() => {
                onAction?.(action)
              }}
            />
          ) : (
            action
          ),
        )}
      </div>
      {children}
    </div>
  )
}
