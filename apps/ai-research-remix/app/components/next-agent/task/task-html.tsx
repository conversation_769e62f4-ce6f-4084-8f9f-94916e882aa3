import React from 'react'
import { cn } from '@bty/util'
import { useTask } from '@/store/task-event'
import { TaskOutputLayout } from './task-output-layout'

interface HtmlRef {
  getContent: () => string | undefined
}

interface HtmlProps {
  className?: string
  taskId: string
  path?: string
  content?: string
}

export const Html = React.forwardRef(
  (
    { className, taskId, path, content }: HtmlProps,
    _ref: React.Ref<HtmlRef>,
  ) => {
    if (content) {
      // 在 content 中添加 base 标签，确保所有链接在 iframe 内部打开
      const contentWithBase = content.includes('<head>')
        ? content.replace('<head>', '<head><base href="about:srcdoc">')
        : content

      return (
        <iframe
          className={cn('w-full border-none', className)}
          srcDoc={contentWithBase}
          sandbox='allow-scripts'
        />
      )
    }

    const src = `${import.meta.env.BASE_URL}web/html?file=${path}&taskId=${taskId}`

    return (
      <iframe
        className={cn('w-full border-none', className)}
        src={src}
        sandbox='allow-same-origin allow-scripts'
      />
    )
  },
)

interface TaskHtmlProps {
  name?: string
  path?: string
  content?: string
}

export const TaskHtml = React.memo(({ name, path, content }: TaskHtmlProps) => {
  const taskId = useTask()

  return (
    <TaskOutputLayout className='mt-20px flex-1' title={name}>
      <div className='h-[calc(100%-44px)] p-8px overflow-hidden'>
        <Html
          className='h-full'
          taskId={taskId!}
          path={path}
          content={content}
        />
      </div>
    </TaskOutputLayout>
  )
})
