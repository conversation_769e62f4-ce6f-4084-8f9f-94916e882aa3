import React, { useEffect, useState } from 'react'
import NiceModal from '@ebay/nice-modal-react'
import type { BrowserUseOutput } from '@apis/mindnote/next-agent-chat.type'
import { getFileUrl } from '@apis/mindnote/next-agent-chat'
import { useTask } from '@/store/task-event'
import { TaskComputerUseModal } from './task-computer-use-modal'
import { Action, TaskOutputLayout } from './task-output-layout'
import { TaskLoading } from './task-loading'

interface TaskComputerUseProps {
  title?: string
  useVnc?: boolean
  snapshot?: BrowserUseOutput
}

export const TaskComputerUse = React.memo(
  ({ title, snapshot, useVnc }: TaskComputerUseProps) => {
    const taskId = useTask()

    const [image, setImage] = useState<string>()

    useEffect(() => {
      const path = snapshot?.clean_screenshot_path
      if (!path) {
        return
      }
      getFileUrl(taskId!, path).then(setImage)
    }, [snapshot])

    const handleAction = () => {
      NiceModal.show(TaskComputerUseModal, {
        owner: 'ai',
        taskId: taskId!,
      })
    }

    let children: React.ReactNode

    if (useVnc) {
      children = (
        <iframe
          className='absolute inset-0 size-full rounded-10px overflow-hidden border-none'
          src={`/vnc?taskId=${taskId}`}
          loading='lazy'
        />
      )
    } else if (snapshot) {
      children = (
        <img
          className='absolute inset-0 size-full rounded-10px'
          src={image}
          alt=''
        />
      )
    }

    if (!children) {
      return <TaskLoading />
    }

    return (
      <TaskOutputLayout
        className='mt-20px'
        title={title}
        actions={[Action.Fullscreen]}
        onAction={handleAction}
      >
        <div className='p-8px'>
          <div className='h-0 pt-80% relative'>{children}</div>
        </div>
      </TaskOutputLayout>
    )
  },
)
