import { Preview } from '@bty/components'
import { useEffect, useState } from 'react'
import { getFileUrl } from '@apis/mindnote/next-agent-chat'
import { useTask } from '@/store/task-event'
import { TaskOutputLayout } from './task-output-layout'
import { TaskLoading } from './task-loading'

interface TaskFileProps {
  type: string
  name?: string
  path?: string
  content?: string
}

export function TaskFile({ name, type, path, content }: TaskFileProps) {
  const taskId = useTask()

  const [url, setUrl] = useState<string>()

  useEffect(() => {
    if (path) {
      getFileUrl(taskId!, path).then(setUrl)
    }
  }, [taskId, path])

  if (!url && !content) {
    return <TaskLoading />
  }

  return (
    <TaskOutputLayout className='mt-20px flex-1' title={name}>
      <div className='h-[calc(100%-44px)] overflow-auto'>
        {type === 'csv' ? (
          <Preview.CSV
            overrides={{ root: 'bg-white' }}
            url={url}
            content={content}
            loadingIndicator={
              <TaskLoading className='h-full flex flex-col justify-center' />
            }
          />
        ) : type === 'txt' ? (
          <div className='p-16px'>{content}</div>
        ) : (
          <p className='text-center text-#8d8d99 mt-100px'>
            此文件类型暂不支持预览
          </p>
        )}
      </div>
    </TaskOutputLayout>
  )
}
