import React, { useMemo } from 'react'
import { type CommonSearchOutput } from '@apis/mindnote/next-agent-chat.type'
import { tryParseToJsonObject } from '@bty/util'
import classNames from 'classnames'
import { Icon } from '../../base/icon'
import { TaskOutputLayout } from './task-output-layout'

interface TaskMcpProps {
  value?: CommonSearchOutput
  title?: string
  mcpInfo: Record<string, any>
}

export const TaskMcp = React.memo(({ value, title, mcpInfo }: TaskMcpProps) => {
  const { description, tool_input, tool_output } = mcpInfo

  const output = tryParseToJsonObject(tool_output)
  const res = useMemo(() => {
    try {
      return JSON.stringify(output.result, null, 2)
    } catch (e) {
      return '解析失败'
    }
  }, [output?.result])

  return (
    <TaskOutputLayout className='mt-20px flex-1' title={title}>
      <div className='px-12px py-8px h-[calc(100%-44px)] overflow-auto'>
        <div className='text-14px/22px c-#8D8D99'>
          {description || '暂无描述'}
        </div>
        <div className='flex flex-col mt-24px'>
          <span className='text-14px/14px c-font font-500'>参数</span>
          <div className='mt-12px text-14px/22px c-font b-1 b-solid b-[rgba(225,225,229,0.6)] bg-#fff rounded-8px p-8px text-14px/22px'>
            {tool_input}
          </div>
        </div>
        <div className='flex flex-col mt-24px'>
          <span className='text-14px/14px c-font font-500'>结果</span>
          <div className={classNames('', {})}>
            {output && output?.success && (
              <div className='p-12px mt-12px rounded-8px text-14px/16px flex items-center b-1 b-solid b-[rgba(44,185,105,0.12)] bg-[rgba(243,249,242)]'>
                <Icon
                  icon='i-icons-run-success'
                  className='!size-16px c-#00B078 '
                />
                <span className='c-#2CB969 ml-8px font-500'>运行成功</span>
              </div>
            )}
            {output && !output?.success && (
              <div className='p-12px mt-12px rounded-8px text-14px/16px flex flex-col b-1 b-solid b-[rgba(255,82,25,0.12)] bg-[rgba(250,242,238)]'>
                <div className='flex items-center'>
                  <Icon
                    icon='i-icons-run-failed'
                    className='!size-16px c-#FF5219 '
                  />
                  <span className='c-#FF5219 ml-8px font-500'>运行失败</span>
                </div>
                <div className='my-12px h-1px bg-[rgba(225,225,229,0.4)]'></div>
                <div className='text-12px/12px c-#FF5219'>
                  {output?.error || '暂无错误信息'}
                </div>
              </div>
            )}
            {!output && (
              <div className='p-12px mt-12px rounded-8px text-14px/16px flex items-center b-1 b-solid b-[rgba(44,185,105,0.12)] bg-[rgba(243,249,242)]'>
                <span className='c-#2CB969 ml-8px font-500'>正在运行</span>
              </div>
            )}
          </div>
          <div className='mt-12px text-14px/22px c-#8D8D99 b-1 b-solid b-[rgba(225,225,229,0.6)] bg-#fff rounded-8px  p-8px text-14px/22px c-#8D8D99 break-words [word-break:break-word]'>
            {(output && res) || '暂无结果'}
          </div>
        </div>
      </div>
    </TaskOutputLayout>
  )
})
