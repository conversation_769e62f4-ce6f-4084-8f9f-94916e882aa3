import React from 'react'
import { ToolType } from '@apis/mindnote/next-agent-chat.type'
import type {
  ToolTypeValue,
  CommonSearchOutput,
} from '@apis/mindnote/next-agent-chat.type'
import { Icon } from '../../base/icon'
import { TaskOutputLayout } from './task-output-layout'
import { TaskLoading } from './task-loading'

interface TaskSearchProps {
  mode: Extract<ToolTypeValue, 'web_search' | 'local_search' | 'mcp'>
  value?: CommonSearchOutput
}

export const TaskSearch = React.memo(({ value, mode }: TaskSearchProps) => {
  if (!value?.length) {
    return <TaskLoading />
  }

  const title =
    mode === ToolType.WebSearch ? '网络搜索结果' : '本地知识库搜索结果'

  return (
    <TaskOutputLayout className='mt-20px flex-1' title={title}>
      <div className='px-12px py-8px h-[calc(100%-44px)] overflow-auto'>
        {value.map((item, index) => (
          <a
            key={index}
            className='h-32px flex items-center px-6px rounded-10px hover:bg-white/80 text-font hover:text-font cursor-pointer not-first:mt-8px'
            href={item.url}
            target='_blank'
            rel='noopener noreferrer'
          >
            <span className='w-24px h-24px inline-flex justify-center items-center shrink-0'>
              <Icon
                className={
                  mode === ToolType.WebSearch
                    ? 'i-icons-copy-link'
                    : 'i-icons-next-agent-datastore'
                }
              />
            </span>
            <span className='ml-4px truncate'>{item.title}</span>
          </a>
        ))}
      </div>
    </TaskOutputLayout>
  )
})
