import React, { useEffect, useRef, useState } from 'react'
import type { <PERSON> } from '@tanstack/react-ranger'
import { useRanger } from '@tanstack/react-ranger'
import { cn } from '@bty/util'
import { ToolType } from '@apis/mindnote/next-agent-chat.type'
import type {
  BrowserUseOutput,
  Event,
} from '@apis/mindnote/next-agent-chat.type'
import { useDebounceFn } from 'ahooks'
import { getFileUrl } from '@apis/mindnote/next-agent-chat'
import { useTask } from '@/store/task-event'
import { IconButton } from '../../base/icon'

interface TaskTimelineProps {
  eventId?: Event['plan_id']
  events: ReadonlyArray<Event>
  onChange: (event?: Event) => void
}

export const TaskTimeline = React.memo(
  ({ eventId, events, onChange }: TaskTimelineProps) => {
    // todo: re-render

    const max = events.length ? events.length - 1 : 0

    const [values, setValues] = useState<ReadonlyArray<number>>([0])

    const [isRealTime, setIsRealTime] = useState(true)

    const [hoverValue, setHoverValue] = useState<number>()

    const [mouseX, setMouseX] = useState<number>()

    useEffect(() => {
      if (eventId) {
        const eventIndex = events.findIndex(e => e.event_id === eventId)
        if (eventIndex !== -1) {
          setValues([eventIndex])
          setIsRealTime(eventIndex === max)
        }
      } else {
        setValues([max])
        setIsRealTime(true)
      }
    }, [eventId, events, max])

    const { run } = useDebounceFn(
      (event?: Event) => {
        onChange(event)
      },
      {
        wait: 500,
      },
    )

    const rangerRef = useRef<HTMLDivElement>(null)

    const rangerInstance = useRanger<HTMLDivElement>({
      getRangerElement: () => rangerRef.current,
      values,
      min: 0,
      max,
      stepSize: 1,
      tickSize: Math.ceil(max / 100),
      onDrag: (instance: Ranger<HTMLDivElement>) => {
        const newValues = instance.sortedValues
        setValues(newValues)
        const isRealTime = newValues[0] === max
        setIsRealTime(isRealTime)
        run(isRealTime ? undefined : events[newValues[0]])
      },
    })

    const handleValuesChange = (action: 'back' | 'forward' | 'reset') => {
      let newValue = values[0]
      let newIsRealTime = isRealTime

      switch (action) {
        case 'back':
          newValue = Math.max(0, values[0] - 1)
          newIsRealTime = false
          break
        case 'forward':
          newValue = Math.min(max, values[0] + 1)
          newIsRealTime = newValue === max
          break
        case 'reset':
          newValue = max
          newIsRealTime = true
          break
      }

      setValues([newValue])
      setIsRealTime(newIsRealTime)

      if (action === 'reset' || newIsRealTime) {
        onChange()
      } else {
        onChange(events[newValue])
      }
    }

    const progressWidth = max === 0 ? 0 : (values[0] / max) * 100

    return (
      <div className='flex items-center'>
        <IconButton
          className='text-#626999/60 mr-8px'
          icon='i-icons-chevron'
          size='size-26px'
          onClick={() => handleValuesChange('back')}
          disabled={values[0] === 0}
        />
        <IconButton
          className='rotate-180 text-#626999/60'
          icon='i-icons-chevron'
          size='size-26px'
          onClick={() => handleValuesChange('forward')}
          disabled={values[0] === max}
        />
        <div
          className='flex-1 h-6px rounded-full ml-12px mr-24px select-none relative bg-#626999/12 cursor-pointer group'
          ref={rangerRef}
          onMouseMove={event => {
            const rect = event.currentTarget.getBoundingClientRect()
            const x = event.clientX - rect.left
            const percentage = x / rect.width
            const value = Math.max(
              0,
              Math.min(max, Math.round(percentage * max)),
            )
            setHoverValue(value)
            setMouseX(event.clientX)
          }}
          onMouseLeave={() => {
            setHoverValue(undefined)
            setMouseX(undefined)
          }}
          onClick={() => {
            if (hoverValue === undefined) {
              return
            }
            const isRealTime = hoverValue === max
            setValues([hoverValue])
            setIsRealTime(isRealTime)
            run(isRealTime ? undefined : events[hoverValue])
            setHoverValue(undefined)
            setMouseX(undefined)
          }}
        >
          {hoverValue && mouseX ? (
            <EventPopover event={events[hoverValue]} mouseX={mouseX} />
          ) : null}
          <div
            className='absolute left-0 top-0 h-full rounded-full bg-#7b67ee z-10'
            style={{ width: `${progressWidth}%` }}
          >
            {rangerInstance
              .handles()
              .map(
                ({ onKeyDownHandler, onMouseDownHandler, onTouchStart }, i) => (
                  <button
                    key={i}
                    className='absolute right-0 top-50% translate-x-50% -translate-y-50% w-10px h-10px rounded-full bg-#7b61ff opacity-0 group-hover:opacity-100 transition-opacity duration-300 cursor-pointer border border-solid border-white outline-none shadow-[0px_4px_10px_0px_rgba(0,0,0,0.3)] after:content-[""] after:absolute after:left-50% after:top-50% after:-translate-x-50% after:-translate-y-50% after:w-20px after:h-20px after:rounded-full after:z-[-1]'
                    role='slider'
                    onClick={event => event.stopPropagation()}
                    onKeyDown={onKeyDownHandler}
                    onMouseDown={onMouseDownHandler}
                    onTouchStart={onTouchStart}
                  />
                ),
              )}
          </div>
        </div>
        <button
          className={cn(
            'inline-flex justify-center items-center gap-4px h-26px rounded-8px transition-300 px-10px',
            isRealTime
              ? 'bg-#626999/12 text-#7b67ee'
              : 'bg-#626999/8 text-#8d8d99',
          )}
          onClick={() => {
            if (!isRealTime) {
              handleValuesChange('reset')
            }
          }}
        >
          <span className='inline-block w-6px h-6px rounded-full bg-current' />
          <span>{isRealTime ? '实时画面' : '回到实时'}</span>
        </button>
      </div>
    )
  },
)

interface EventPopoverProps {
  event: Event
  mouseX: number
}

function EventPopover({ event, mouseX }: EventPopoverProps) {
  const taskId = useTask()

  const actionName = event.content.action_name?.slice(2)

  const args = event.content?.arguments?.join(' ').slice(0, 20)

  const screenshotPath =
    event.content.tool_name === ToolType.BrowserUse && event.content.tool_output
      ? (event.content.tool_output as BrowserUseOutput).clean_screenshot_path
      : undefined

  const [screenshotUrl, setScreenshotUrl] = useState<string>()

  useEffect(() => {
    if (screenshotPath) {
      getFileUrl(taskId!, screenshotPath).then(setScreenshotUrl)
    } else {
      setScreenshotUrl(undefined)
    }
  }, [screenshotPath])

  return (
    <div
      className='fixed z-50 w-180px rounded-6px bg-#3f3f44 shadow-[0px_8px_24px_0px_rgba(0,0,0,0.2)] backdrop-blur-lg p-8px pointer-events-none -translate-x-1/2'
      style={{
        left: mouseX,
        bottom: 60,
      }}
    >
      {screenshotUrl ? (
        <img
          src={screenshotUrl}
          alt=''
          className='w-full h-auto rounded-2px mb-8px align-top'
        />
      ) : null}
      <div className='text-12px/18px text-white text-center truncate'>
        {actionName} {args}
      </div>
      <div className='absolute left-1/2 -translate-x-1/2 -bottom-6px w-0 h-0 border-l-6 border-l-transparent border-r-6 border-r-transparent border-t-6 border-t-[#3f3f44]' />
    </div>
  )
}
