import { Markdown } from '@bty/components'
import React, { useEffect, useState } from 'react'
import { getFileUrl } from '@apis/mindnote/next-agent-chat'
import { useTask } from '@/store/task-event'
import { TaskOutputLayout } from './task-output-layout'
import { TaskLoading } from './task-loading'

interface TaskMarkdownProps {
  name?: string
  path?: string
  content?: string
}

export const TaskMarkdown = React.memo(
  ({ name, path, content }: TaskMarkdownProps) => {
    const taskId = useTask()

    const [url, setUrl] = useState<string>()

    useEffect(() => {
      if (path) {
        getFileUrl(taskId!, path).then(setUrl)
      }
    }, [path])

    if (!url && !content) {
      return <TaskLoading />
    }

    return (
      <TaskOutputLayout className='mt-20px flex-1' title={name}>
        <div className='p-24px h-[calc(100%-44px)] overflow-auto'>
          <Markdown content={content} url={url} remote={!!url} />
        </div>
      </TaskOutputLayout>
    )
  },
)
