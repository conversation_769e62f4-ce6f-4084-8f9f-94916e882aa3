import {
  memo,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react'
import { useMemoizedFn } from 'ahooks'
import TextAreaAutoSize from 'react-textarea-autosize'
import classNames from 'classnames'
import { Tooltip, message } from 'antd'
import {
  EventActionType,
  EventType,
  TaskStatus,
} from '@apis/mindnote/next-agent-chat.type'
import { Icon } from '../base/icon'
import { useLatestTaskEvent, useTask } from '@/store/task-event'
import { useCompositing } from '@/hooks/use-compositing'
import { useNextAgent } from './provider/NextAgentProvider'

// import { ResearchConfig } from './research-config'
import { useNextAgentEvent } from './provider/NextAgentEventProvider'
import { BLANK_CONVERSATION_ID, statusTextMap } from './const'
import type { SendMessagePayload } from './types/message'
// import type { MCPConfigRef } from './mcp-popover'
// import { MCPConfig } from './mcp-popover'

export const NextAgentMessageInput = memo<{ autofocus?: boolean }>(props => {
  const { autofocus } = props
  const {
    sendMessage,
    messageInputRef,
    taskStatus,
    abortMessage,
    onCreateConversationAndRefresh,
    messageInputActionRef,
    onUpdateConversation,
    wsReadyState,
  } = useNextAgent()

  const taskId = useTask()

  const latestEvent = useLatestTaskEvent()
  // const mcpConfigRef = useRef<MCPConfigRef>(null)
  const [compositing, handleComposition] = useCompositing()

  const { onMessageSendHook } = useNextAgentEvent()
  const [content, setContent] = useState('')

  const taskIsRunning = taskStatus === TaskStatus.IN_PROGRESS
  const taskIsPause = taskStatus === TaskStatus.PAUSE
  const taskIsCompleted = [
    TaskStatus.COMPLETED,
    TaskStatus.FAILED,
    TaskStatus.CANCELED,
  ].includes(taskStatus)

  const isAskUser = useMemo(() => {
    return taskIsCompleted
      ? false
      : latestEvent?.event_type === EventType.ASK_USER &&
          latestEvent?.content?.action_type === EventActionType.AskUser
  }, [
    latestEvent?.event_type,
    latestEvent?.content?.action_type,
    taskIsCompleted,
  ])

  const handleSubmit = useMemoizedFn((payload: SendMessagePayload) => {
    onMessageSendHook?.()
    if (taskId && taskId === BLANK_CONVERSATION_ID) {
      onCreateConversationAndRefresh().then(async returnConversationId => {
        if (returnConversationId) {
          const url = new URL(window.location.href)
          url.searchParams.set('id', returnConversationId)
          window.history.replaceState({}, '', url.toString())
          // await mcpConfigRef.current?.triggerMcp(returnConversationId)
          setTimeout(() => {
            sendMessage(payload, returnConversationId)
          }, 50)
        }
      })
    } else {
      sendMessage(payload)
    }
  })

  useImperativeHandle(messageInputActionRef, () => ({
    setContent: (text: string) => {
      setContent(text)
      if (messageInputRef.current) {
        messageInputRef.current.value = text
      }
    },
  }))

  useEffect(() => {
    if (taskId) {
      setContent('')
    }
  }, [taskId])

  useEffect(() => {
    if (
      latestEvent?.event_type === EventType.ASK_USER &&
      latestEvent?.content?.action_type === EventActionType.AskUser &&
      messageInputRef.current
    ) {
      messageInputRef.current.focus()
    }
  }, [
    latestEvent?.event_type,
    latestEvent?.content?.action_type,
    messageInputRef.current,
  ])

  const tooltipPopContainerRef = useRef<HTMLDivElement | null>(null)

  const showStatusText = [
    TaskStatus.IN_PROGRESS,
    TaskStatus.COMPLETED,
    TaskStatus.FAILED,
    TaskStatus.CANCELED,
  ].includes(taskStatus)

  return (
    <>
      <div
        className='group relative p-[2px] box-border bg-white rounded-[12px] items-center border-[#E1E1E5] border border-solid border-opacity-60 shadow-[0px_4px_20px_0px_rgba(0,0,0,0.08)] box-border'
        onClick={() => {
          if (messageInputRef.current) {
            messageInputRef.current.focus()
          }
        }}
      >
        <div className='flex items-center'>
          {isAskUser ? (
            <div className='w-100% border-1px px-12px h-66px border-solid border-[rgba(225,225,229,0.6)] rounded-[12px_12px_0_0] absolute left-0 top-[-45px]  z-0 text-14px c-font items-start'>
              <div className='mt-12px flex items-center justify-between'>
                <div className='flex items-center'>
                  <Icon
                    icon='i-icons-next-agent-task-running'
                    className='!size-18px mr-8px'
                  />
                  <p>正在等待你的回复...</p>
                </div>
                <div
                  className='w-24px h-24px rounded-full flex-center bg-gradient-to-br from-[#36CDFF] via-[#684AFF] to-[#963AFF] cursor-pointer hover:op-80'
                  onClick={e => {
                    e.stopPropagation()
                    if (taskIsPause) {
                      abortMessage()
                      if (taskId) {
                        onUpdateConversation(taskId, {
                          task_state: TaskStatus.CANCELED,
                        })
                      }
                    }
                  }}
                >
                  <Icon icon='i-icons-pause-white' className='!size-8px' />
                </div>
              </div>
            </div>
          ) : (
            <div className='absolute invisible group-focus-within:visible right-0 top-[-28px] z-[10] pointer-events-none p-4px c-#8D8D99/80 text-12px flex items-center rd-4px bg-#fff/90 backdrop-blur-10px'>
              <Icon icon='i-icons-enter-key' className='!size-12px mr-4px' />
              发送，
              <Icon icon='i-icons-shift-key' className='!size-12px' />
              <Icon
                icon='i-icons-enter-key'
                className='!size-12px ml-2px mr-4px'
              />
              换行 ，
              <Icon icon='i-icons-cmd-key' className='!size-9px mr-2px' />K
            </div>
          )}
        </div>

        <div className='hidden absolute inset-0 pointer-events-none rounded-[12px] border-0 bg-[conic-gradient(from_var(--angle),#36CDFF,#684AFF,#963AFF,#36CDFF)] group-focus-within:block animate-[rotate_16s_linear_infinite]'></div>
        <div className='chat-input-wrapper flex items-center relative z-[2] min-h-[50px] w-full flex justify-between bg-white rounded-[10px] box-border p-[9px]'>
          {showStatusText ? (
            <div className='text-14px/14px c-font flex items-center'>
              <Icon
                icon='i-icons-next-agent-task-running'
                className='!size-18px mr-8px'
              />
              <span>{statusTextMap[taskStatus]}</span>
            </div>
          ) : (
            <div className='w-100%'>
              <TextAreaAutoSize
                ref={messageInputRef}
                value={content}
                onChange={event => {
                  const content = event.target.value
                  setContent(content)
                }}
                onKeyDown={e => {
                  if (compositing.current) return
                  if (e.key === 'Enter') {
                    if (e.shiftKey) {
                      return // 允许换行
                    }
                    e.preventDefault() // 阻止默认的换行行为
                    if (wsReadyState !== 1) {
                      message.error('连接已断开，请刷新页面重试')
                      return
                    }
                    if (content.trim() && !taskIsRunning && content) {
                      handleSubmit({ content })
                      setContent('')
                    }
                  }
                }}
                onCompositionStart={handleComposition}
                onCompositionEnd={handleComposition}
                autoFocus={autofocus}
                wrap='soft'
                className='block min-h-[24px] w-full h-full p-[0] placeholder:align-middle resize-none border-0 bg-white text-[14px]/[24px] outline-0 placeholder:text-[#8d8d99]/60 placeholder:leading-[24px] text-[#17171d] focus-visible:outline-none disabled:placeholder:text-[#8d8d99]/40 mb-0'
                minRows={1}
                maxRows={8}
                style={{ fontSize: '16px' }}
                placeholder='问我任何问题...'
              />
              {/* <MCPConfig ref={mcpConfigRef} /> */}
            </div>
          )}

          <div
            className={classNames('ml-auto mt-auto', {
              'mb-auto': showStatusText,
            })}
          >
            {!taskIsCompleted && (
              <Tooltip
                title={taskIsRunning && '停止生成'}
                getPopupContainer={() =>
                  tooltipPopContainerRef.current || document.body
                }
                destroyTooltipOnHide
              >
                <div
                  ref={tooltipPopContainerRef}
                  className={classNames(
                    'rounded-full flex-center bg-gradient-to-br from-[#36CDFF] via-[#684AFF] to-[#963AFF] cursor-pointer hover:op-80',
                    {
                      '!opacity-50 cursor-not-allowed':
                        !content && !taskIsRunning,
                      'w-24px h-24px': taskIsRunning,
                      'w-32px h-32px': !taskIsRunning,
                    },
                  )}
                  onClick={e => {
                    e.stopPropagation()
                    if (taskIsRunning) {
                      abortMessage()
                    } else {
                      if (!content) return
                      handleSubmit({ content })
                      setContent('')
                    }
                  }}
                >
                  {taskIsRunning ? (
                    <Icon icon='i-icons-pause-white' className='!size-8px' />
                  ) : (
                    <Icon icon='i-icons-send-white' className='!size-12px' />
                  )}
                </div>
              </Tooltip>
            )}
          </div>
        </div>
      </div>
      <style>{`
        @property --angle {
          syntax: '<angle>';
          inherits: false;
          initial-value: 0deg;
        }

        @keyframes rotate {
          to {
            --angle: 360deg;
          }
        }
      `}</style>
    </>
  )
})
