import { message } from 'antd'
import {
  EventStatus,
  EventType,
  TaskStatus,
  ToolType,
} from '@apis/mindnote/next-agent-chat.type'
import type { McpConfig } from '@bty/global-types/mcp'
import { runMcpTool } from '@/components/mcp-chat/utils/mcp'

export function createNextAgentSocketUrl(
  path: string,
  params: Record<string, any>,
) {
  const baseUrl = process.env.API_BASE_URL_SUB_WS

  const buildUrl = baseUrl + path
  const url = new URL(buildUrl)

  for (const [key, value] of Object.entries(params)) {
    if (value) {
      url.searchParams.set(
        key,
        Array.isArray(value) ? value.join(',') : value.toString(),
      )
    }
  }

  return url.toString()
}

export function processMcpToolName(toolName: Record<string, any>) {
  if (!toolName) return ''
  const toolNameArr = toolName.split('__')
  toolNameArr.shift() // 移除第一个元素
  return toolNameArr.join('__') // 使用 '__' 连接，保持原有分隔符
}

// 状态映射常量
export const MCP_STATUS_MAP = {
  ready: '等待确认运行',
  running: '正在运行',
  success: '已运行',
  failed: '已运行',
} as const

// 状态类型定义
export type McpStatus = keyof typeof MCP_STATUS_MAP

// 状态图标映射
export const STATUS_ICON_MAP: Record<
  string,
  { icon: string; className: string }
> = {
  ready: { icon: 'i-icons-tool', className: 'size-12px! c-#8D8D99' },
  running: { icon: 'i-icons-loading', className: 'size-14px! animate-spin' },
  success: { icon: 'i-icons-run-success', className: 'size-14px!' },
  failed: { icon: 'i-icons-run-failed', className: 'size-14px!' },
}

// 执行MCP工具
export async function executeMcpTool(
  mcp: McpConfig,
  realToolName: string,
  toolInput: any,
  onSuccess?: (result: any) => void,
  onError?: (error: any) => void,
) {
  try {
    const result = await runMcpTool(mcp, realToolName, toolInput)
    console.log('MCP工具执行成功:', result)
    onSuccess?.(result)
    return result
  } catch (error) {
    console.error('MCP工具执行失败:', error)
    const errorMessage =
      error instanceof Error ? error.message : '工具调用出现未知异常'
    message.error(errorMessage)
    onError?.(error)
    throw error
  }
}

// 检查是否可以执行MCP工具
export function canExecuteMcpTool(
  event: any,
  latestEvent: any,
  metadata: any,
  toolCallId?: string,
): boolean {
  return !!(
    event &&
    event?.event_type === EventType.TOOL_CALL &&
    // TODO: action_type后续需要修改
    event?.content?.action_type === ToolType.MCP &&
    latestEvent.plan_step_state === TaskStatus.PAUSE &&
    metadata?.is_auto_executed &&
    toolCallId &&
    event.event_status !== EventStatus.SUCCESS &&
    event.event_status !== EventStatus.FAILED
  )
}

// 延迟执行工具函数
export function delayedExecution(fn: () => Promise<void>, delay = 100) {
  setTimeout(fn, delay)
}

export function isClientPlatform() {
  return !!window.MindNote
}
