import classNames from 'classnames'
import type { ReactNode } from 'react'
import { useEffect, useMemo, useState } from 'react'
import { TaskStatus } from '@apis/mindnote/next-agent-chat.type'
import { ScrollView } from '../base/scroll-view'
import { Icon } from '../base/icon'
import { useLatestTaskEvent, useTask } from '@/store/task-event'
import { useNextAgent } from './provider/NextAgentProvider'
import Step, { StepType } from './components/step'

interface NextAgentMessageListProps {
  placeholder?: ReactNode
}

export function NextAgentMessageList(props: NextAgentMessageListProps) {
  const { placeholder } = props
  const [showScrollToBottom, setShowScrollToBottom] = useState(false)

  const {
    conversationList,
    messageList,
    scrollRef,
    taskStatus,
    autoScroll,
    setAutoScroll,
  } = useNextAgent()

  const taskId = useTask()

  const latestEvent = useLatestTaskEvent()

  const taskIsRunning = taskStatus === TaskStatus.IN_PROGRESS

  const nowConversation = useMemo(() => {
    return conversationList.find(e => e.conversation_id === taskId)
  }, [conversationList, taskId])

  const hasMessage = useMemo(() => {
    return messageList.length !== 0 || nowConversation?.has_messages === true
  }, [messageList, nowConversation])

  useEffect(() => {
    scrollRef.current?.scrollTo({
      top: scrollRef.current?.scrollHeight,
    })
  }, [!!messageList.length])

  // 当新消息添加或生成状态改变时，如果启用了自动滚动，则滚动到底部
  useEffect(() => {
    if (autoScroll && taskIsRunning) {
      scrollRef.current?.scrollTo({
        top: scrollRef.current?.scrollHeight,
        behavior: 'smooth',
      })
    }
  }, [messageList, taskIsRunning, autoScroll])

  if (messageList.length === 0 && placeholder) {
    return placeholder
  }

  return (
    <ScrollView
      className={classNames(
        'duration-300 [&_.markdown-paragraph]:text-16px/24px [&_li]:text-16px/21px relative [&_.scroll-view]:max-w-960px [&_.scroll-view]:mx-auto',
        {
          'flex-1 size-full [&_.scroll-view-content]:py-24px': hasMessage,
          'h-0px!': !hasMessage,
        },
      )}
      vbarClassName='right-[-24px]!'
      scrollRef={scrollRef}
      onWheel={e => {
        // scrollview 判定10px以内为底部
        if (e.deltaY < -10 && autoScroll) {
          setAutoScroll(false)
        }
      }}
      onScrolledToBottomChange={isAtBottom => {
        setShowScrollToBottom(!isAtBottom)
        if (isAtBottom && !autoScroll) {
          setAutoScroll(true)
        }
      }}
    >
      {messageList.map((step, index) => {
        // 根据步骤类型动态设置适当的 Step 类型
        const stepType = step.id.startsWith('user_input_')
          ? StepType.USER_INPUT
          : StepType.STANDARD

        return (
          <div className={classNames({ 'mt-24px': index !== 0 })} key={index}>
            <Step
              title={step.title}
              stepState={step.state}
              taskState={taskStatus}
              events={step.events}
              isCollapsed={false}
              type={stepType}
              latestEvent={latestEvent!}
            />
          </div>
        )
      })}

      <div
        className={classNames(
          'absolute bottom-24px z-[19] left-50% -translate-x-1/2 bg-white box-border border-1 border-solid border-[rgba(225,225,229,0.8)] shadow-[0px_0px_20px_0px_rgba(0,0,0,0.16)] flex items-center justify-center rounded-full w-44px h-44px cursor-pointer transition-opacity duration-150 ease-in-out',
          {
            '!opacity-0 !pointer-events-none !scale-0':
              !showScrollToBottom || (taskIsRunning && autoScroll),
            'opacity-100 scale-100': showScrollToBottom,
          },
        )}
        onClick={() => {
          scrollRef.current?.scrollTo({
            top: scrollRef.current?.scrollHeight,
            behavior: 'smooth',
          })
          if (taskIsRunning) {
            setAutoScroll(true)
          }
        }}
      >
        <Icon icon='i-icons-scroll-to-bottom' className='!text-16px' />
      </div>
    </ScrollView>
  )
}
