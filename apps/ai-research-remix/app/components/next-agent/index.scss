.chat {
  .related-question-item {
    font-size: 14px;
    height: 36px;
  }

  @keyframes breath {
    0% {
      opacity: 0;
      transform: scale(0.5);
    }

    80% {
      opacity: 0.6;
      transform: scale(1);
    }

    100% {
      opacity: 0;
      transform: scale(0.5);
    }
  }

  .animate-breath {
    animation: breath 2s linear infinite;
  }

  .chat-conversation-item {
    position: relative;
    overflow: hidden;

    .chat-conversation-item-edit-wrapper {
      position: absolute;
      inset: 0;
      z-index: 1;
    }

    .chat-conversation-item-edit-input {
      height: 100%;
      border-radius: 0.5rem;
      border: 1px solid #7b61ff;
    }

    .chat-conversation-item-edit-confirm {
      display: none;
    }
  }
}