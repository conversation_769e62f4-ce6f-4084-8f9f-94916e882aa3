import { TaskStatus } from '@apis/mindnote/next-agent-chat.type'

export const BLANK_CONVERSATION_ID = 'BLANK_CONVERSATION_ID'

export const NEW_TASK = '新任务'

export const TIME_GROUPS: { key: string; label?: string; threshold: number }[] =
  [
    {
      key: 'inOneWeek',
      threshold: 0,
    },
    {
      key: 'aDayAgo',
      label: '1天前',
      threshold: 1,
    },
    {
      key: 'threeDaysAgo',
      label: '3天前',
      threshold: 3,
    },
    {
      key: 'aWeekAgo',
      label: '1周前',
      threshold: 7,
    },
    {
      key: 'aMonthAgo',
      label: '1个月前',
      threshold: 30,
    },
    {
      key: 'threeMonthsAgo',
      label: '3个月前',
      threshold: 90,
    },
    {
      key: 'sixMonthsAgo',
      label: '6个月前',
      threshold: 180,
    },
    {
      key: 'oneYearAgo',
      label: '1年前',
      threshold: 365,
    },
  ]

// 状态与文案映射
export const statusTextMap: Record<TaskStatus, string> = {
  [TaskStatus.NOT_STARTED]: '任务未开始',
  [TaskStatus.IN_PROGRESS]: '任务正在运行',
  [TaskStatus.PAUSE]: '任务已暂停', // 目前暂停状态都是需要用户认为介入的一种状态，因此文案提示为等待用户操作
  [TaskStatus.COMPLETED]: '任务已完成',
  [TaskStatus.FAILED]: '任务失败',
  [TaskStatus.CANCELED]: '任务已取消',
  [TaskStatus.ConnectFail]: '连接失败,请刷新页面重试',
}
