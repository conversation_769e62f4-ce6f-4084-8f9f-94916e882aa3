import {
  Attachment,
  Operation,
  Event,
  EventStatus,
  TaskStatus,
} from '@apis/mindnote/next-agent-chat.type'
import { EventType } from '@apis/mindnote/next-agent-chat.type'

export interface SendMessagePayload {
  message_type?: string
  content: string
  originUserMessageId?: string // 原来的UserMessage的ID，用于重新生成的时候，绑定原始的用户消息（仅前端逻辑使用）
}

export { EventType }
export type { Event }

export interface WSEventData {
  code: number
  success: boolean
  message: string
  data: {
    type?: string
    conversation_id: string
    task_id: string
    chat_event: Event
    request_id: string
    now_time: number
    task_status: TaskStatus
  }
}
