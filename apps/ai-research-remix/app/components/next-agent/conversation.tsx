import { useEffect, useMemo, useRef, useState } from 'react'
import dayjs from 'dayjs'
import { useMemoizedFn } from 'ahooks'
import type { Conversation } from '@apis/mindnote/next-agent-chat.type'
import { cn } from '@bty/util'
import { InfiniteScroll } from '@bty/components'
import { ScrollView } from '../base/scroll-view'
import { useTask, useTaskArtifact, useTaskEvent } from '@/store/task-event'
import { Tabs, useTaskPanel } from '@/store/task'
import { BLANK_CONVERSATION_ID, TIME_GROUPS } from './const'
import { useNextAgent } from './provider/NextAgentProvider'
import { ConversationItem } from './conversation-item'

export function ConversationList() {
  const {
    conversationListCollapsed,
    conversationList,
    conversationLoading,
    onConversationDelete,
    onUpdateConversation,
    taskStatus,
    hasMoreConversation,
    loadMoreConversations,
  } = useNextAgent()

  const taskId = useTask()

  const setTaskId = useTaskEvent(state => state.setTaskId)

  const { setActiveTab } = useTaskPanel()

  const { close: closeArtifactIfNeed } = useTaskArtifact()

  const targetRef = useRef<HTMLDivElement>(null)
  const [loading, setLoading] = useState(false)

  const conversationGroups = useMemo(() => {
    if (!conversationList.length) {
      return []
    }

    const groups = TIME_GROUPS.reduce(
      (map, value) => {
        map[value.key] = {
          label: value.label,
          data: [],
        }
        return map
      },
      {} as Record<string, { label?: string; data: Conversation[] }>,
    )

    conversationList.forEach(item => {
      const delta = dayjs().diff(dayjs(item.created_at), 'day')

      const foundGroup = TIME_GROUPS.find((value, index, values) => {
        if (index === values.length - 1) {
          return delta >= value.threshold
        }
        return delta >= value.threshold && delta < values[index + 1].threshold
      })

      if (foundGroup) {
        groups[foundGroup.key].data.push(item as any)
      }
    })
    return Object.keys(groups).map(key => ({
      key,
      ...groups[key],
    }))
  }, [conversationList, taskStatus])

  const handleConversationRename = useMemoizedFn(
    (id?: string, title?: string) => {
      if (!id) return
      onUpdateConversation(id, {
        title,
      })
    },
  )

  const handleConversationDelete = useMemoizedFn(async (id?: string) => {
    if (!id) return

    try {
      await onConversationDelete(id)

      // 如果删除的是当前选中的对话，清除状态和URL
      if (taskId === id) {
        const url = new URL(window.location.href)
        url.searchParams.delete('id')
        window.history.replaceState({}, '', url.toString())
      }
    } catch (error) {
      console.error('删除对话失败:', error)
    }
  })

  const handleConversationClick = useMemoizedFn((id?: string) => {
    if (!id) return
    setTaskId(id)
    setActiveTab(Tabs.Computer)
    closeArtifactIfNeed()
    const url = new URL(window.location.href)
    url.searchParams.set('id', id)
    window.history.replaceState({}, '', url.toString())
  })

  useEffect(() => {
    const url = new URL(window.location.href)
    setTaskId(url.searchParams.get('id') ?? BLANK_CONVERSATION_ID)
  }, [])

  const loadMoreData = useMemoizedFn(async () => {
    if (loading) {
      return
    }
    setLoading(true)
    try {
      await loadMoreConversations()
    } finally {
      setLoading(false)
    }
  })

  const list = conversationGroups
    .map(group =>
      group.data.length ? (
        <div key={group.key} className='group'>
          <span className='block pl-[12px] text-font_1  text-[12px]/[36px] font-semibold'>
            {group.label}
          </span>
          {group.data.map(conversation => {
            if (conversation.conversation_id === BLANK_CONVERSATION_ID) {
              return null
            }
            const selected = taskId === conversation.conversation_id
            return (
              <ConversationItem
                key={conversation.conversation_id}
                id={conversation.conversation_id}
                selected={selected}
                state={conversation.task_state}
                content={conversation.title}
                onClick={handleConversationClick}
                onEditSave={handleConversationRename}
                onDelete={handleConversationDelete}
              />
            )
          })}
        </div>
      ) : undefined,
    )
    .filter(Boolean)

  return (
    <div
      className={cn(
        'conversation-list-popup w-[264px] shrink-0 transition-[width] flex flex-col h-full overflow-hidden relative b-r-1px b-[rgba(225,225,229,0.6)]',
        conversationListCollapsed ? '!w-0 of-hidden' : '',
      )}
    >
      <div
        ref={targetRef}
        className='flex w-full flex-1 flex-col overflow-hidden p-0px'
      >
        <ScrollView
          className='h-full px-16px scroll-y-auto'
          vbarClassName='right-0px!'
        >
          <InfiniteScroll
            key={`infinite-scroll-${conversationList.length}-${hasMoreConversation}`}
            load={loadMoreData}
            targetRoot={targetRef}
            hasMore={hasMoreConversation && !loading}
            loader={
              <div className='flex flex-center h-full c-#8D8D99'>
                数据加载中...
              </div>
            }
          >
            <div>
              {!loading && list.length === 0 && !conversationLoading && (
                <div className='text-center text-yeah-light text-opacity-60 text-[14px]/[20px] mt-[60px]'>
                  暂无任务
                </div>
              )}

              {list.length !== 0 && list}
            </div>
          </InfiniteScroll>
        </ScrollView>
      </div>
    </div>
  )
}
