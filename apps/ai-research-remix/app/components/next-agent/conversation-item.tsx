import type { ChangeEvent } from 'react'
import { memo, useLayoutEffect, useState } from 'react'
import { Input, Popconfirm } from 'antd'
import classNames from 'classnames'
import { useMemoizedFn } from 'ahooks'
import {
  TaskStatus,
  type Conversation,
} from '@apis/mindnote/next-agent-chat.type'
import { IconButton } from '../base/icon'
import { statusTextMap } from './const'

export interface ConversationItemProps {
  id?: string
  selected?: boolean
  content?: string
  onClick?: (id?: string) => void
  onEditSave?: (id: string, content: string) => void
  onDelete?: (id?: string) => void
  state?: Conversation['status']
}

export const ConversationItem = memo<ConversationItemProps>(
  ({ selected, content = '', onClick, id, onDelete, onEditSave, state }) => {
    const [mode, setMode] = useState<'view' | 'edit'>('view')

    const [title, setTitle] = useState(content)
    const [editingValue, setEditingValue] = useState('')
    const [popConfirmOpen, setPopConfirmOpen] = useState(false)

    useLayoutEffect(() => {
      setTitle(content)
    }, [content])

    const handleToggleMode = useMemoizedFn((mode: 'edit' | 'view') => {
      setMode(mode)
      if (mode === 'edit') {
        setEditingValue(content)
      }
    })

    const handleChange = useMemoizedFn((e: ChangeEvent<HTMLInputElement>) => {
      setEditingValue(e.target.value)
    })

    const handleChangeTitle = () => {
      if (editingValue?.length) {
        setMode('view')
        setTitle(editingValue)
        onEditSave?.(id!, editingValue)
      }
    }

    const handleSelect = useMemoizedFn(() => {
      if (mode === 'edit') return
      onClick?.(id)
    })

    const handleToEdit = useMemoizedFn((event: any) => {
      if (mode === 'edit') return
      event.stopPropagation()
      handleToggleMode('edit')
    })

    const handleToView = useMemoizedFn((event: any) => {
      if (mode === 'view') return
      event.stopPropagation()
      handleToggleMode('view')
    })

    const handleKeydown = useMemoizedFn((event: any) => {
      if (event.code === 'Enter') {
        if (!editingValue?.length) {
          setMode('view')
        }
        handleChangeTitle()
      }
    })

    const handleDelete = useMemoizedFn(() => {
      onDelete?.(id)
    })

    return (
      <div
        className={classNames(
          'p-12px flex items-center  hover:bg-#626999/8 cursor-pointer rd-8px mb-4px [&_.operation-group]:hover:flex! [&_.title-group]:hover:w-80%!',
          {
            'bg-#7B61FF/8!': selected,
          },
        )}
        onClick={handleSelect}
      >
        <div
          className={classNames('felx flex-col w-full title-group', {
            'w-80%!': popConfirmOpen,
          })}
        >
          <div className='flex items-center'>
            <div className={classNames('w-full mb-4px')}>
              {mode === 'view' ? (
                <div className='h-16px size-full flex items-center of-hidden gap-8px'>
                  <span
                    className={classNames(
                      'flex-1 text-truncate ui-text-yeah text-14px/16px',
                      {
                        'font-500': selected,
                      },
                    )}
                  >
                    {title}
                  </span>
                </div>
              ) : (
                <Input
                  className='size-full h-full rd-4px shadow-none! border-solid border-primary text-14px/16px'
                  value={editingValue}
                  autoFocus
                  onBlur={handleToView}
                  onChange={handleChange}
                  onKeyDown={handleKeydown}
                />
              )}
            </div>
          </div>
          <div className='mt-8px flex items-center'>
            <div
              className={classNames(
                'z-1 size-12px rd-12px relative flex-center mr-3px',
                {
                  hidden: state === TaskStatus.COMPLETED || state === TaskStatus.FAILED || state === TaskStatus.CANCELED || state === TaskStatus.NOT_STARTED,
                },
              )}
            >
              <div className='animate-breath absolute inset-0 rd-12px bg-#625DFF/50'></div>
              <div className='size-6px rd-12px bg-#625DFF' />
            </div>
            <p className='text-12px/12px text-#8D8D99'>{statusTextMap[state!]}</p>
          </div>
        </div>
        <div
          className={classNames(
            'operation-group hidden flex items-center text-#626999/60 mr-[-4px]',
            {
              'flex!': popConfirmOpen,
            },
          )}
          onClick={e => e.stopPropagation()}
        >
          <IconButton
            icon='i-icons-edit'
            className='rd-4px!'
            onClick={handleToEdit}
          />

          <Popconfirm
            icon={null}
            title={
              <div className='mb-12px'>任务删除后无法恢复，是否确认删除？</div>
            }
            onOpenChange={setPopConfirmOpen}
            onConfirm={handleDelete}
            okText='删除'
            cancelText='取消'
            okButtonProps={{
              danger: true,
              autoInsertSpace: false,
              className: 'h-32px! w-60px!',
            }}
            cancelButtonProps={{
              variant: 'filled',
              autoInsertSpace: false,
              className: 'h-32px! w-60px!',
            }}
          >
            <IconButton
              icon='i-icons-delete'
              className={classNames(
                'rd-4px! hover:bg-#FF5219/8! hover:text-#FF5219',
                {
                  'bg-#FF5219/8! text-#FF5219!': popConfirmOpen,
                },
              )}
            />
          </Popconfirm>
        </div>
      </div>
    )
  },
)
