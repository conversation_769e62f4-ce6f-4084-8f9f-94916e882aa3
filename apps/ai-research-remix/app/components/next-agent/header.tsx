import { cn } from '@bty/util'
import { useKeyPress } from 'ahooks'
import { useCallback, useMemo } from 'react'
import classNames from 'classnames'
import { Tooltip } from 'antd'
import { Icon, IconButton } from '../base/icon'
import { useTaskStore } from '@/store/task'
import { useTask } from '@/store/task-event'
import { NEW_TASK } from './const'
import { useNextAgent } from './provider/NextAgentProvider'

export function ChatHeader() {
  const {
    conversationList,
    conversationListCollapsed,
    setConversationListCollapsed,
    onCreateBlankConversation,
  } = useNextAgent()

  const taskId = useTask()

  const hidePanel = useTaskStore(state => state.hidePanel)

  useKeyPress('meta.k', () => {
    onCreateBlankConversation()
    hidePanel()
  })

  const handleHistoryListCollapse = useCallback(() => {
    // 打开任务列表的时候隐藏详情面板
    if (conversationListCollapsed) {
      hidePanel()
    }
    setConversationListCollapsed(!conversationListCollapsed)
  }, [conversationListCollapsed])

  const title = useMemo(() => {
    const now = conversationList.find(e => e.conversation_id === taskId)
    if (!now || now.title.startsWith(NEW_TASK)) {
      return 'Next Agent'
    }
    return now.title
  }, [taskId, conversationList])

  return (
    <header className='h-60px flex relative shrink-0'>
      <div className='h-36px flex-center absolute left-16px top-16px'>
        <IconButton
          icon='i-icons-list'
          iconSize='size-18px'
          size='size-36px'
          className='text-font mr-12px'
          onClick={handleHistoryListCollapse}
        />
        <div className='flex flex-1 items-center'>
          <Tooltip
            title={
              conversationListCollapsed && (
                <div className='flex'>
                  {NEW_TASK}
                  <span className='ml-auto text-13px flex flex-center text-white pl-8px'>
                    <Icon
                      icon='i-icons-cmd-key'
                      className='!size-10px mr-3px'
                    />
                    K
                  </span>
                </div>
              )
            }
          >
            <IconButton
              className={classNames(
                'text-font duration-300 justify-start! px-8px',
                {
                  'size-36px!': conversationListCollapsed,
                  'h-36px! w-183px! hover:bg-bg-hover/8 b-1px b-solid b-[#E1E1E5]/80 rd-8px!':
                    !conversationListCollapsed,
                },
              )}
              icon='i-icons-add'
              iconSize='size-18px'
              size='size-36px'
              onClick={() => {
                onCreateBlankConversation()
                hidePanel()
              }}
            >
              {!conversationListCollapsed && (
                <>
                  <span className='flex-1 ml-8px text-nowrap'>{NEW_TASK}</span>
                  <span className='ml-auto text-13px flex flex-center c-#8D8D99 c-op-60'>
                    <Icon
                      icon='i-icons-cmd-key'
                      className='!size-10px mr-3px'
                    />
                    K
                  </span>
                </>
              )}
            </IconButton>
          </Tooltip>
        </div>
      </div>

      <div
        className={cn(
          'flex-none w-[264px] duration-150 b-r-solid b-r-line/60',
          {
            'b-r-1px': !conversationListCollapsed,
            '!w-0': conversationListCollapsed,
          },
        )}
      />

      <div className='flex-1 flex flex-center ui-gap-8px pt-16px'>
        <div className='font-500 text-16px'>{title}</div>
      </div>
    </header>
  )
}
