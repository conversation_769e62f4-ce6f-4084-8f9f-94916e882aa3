import { useRef, useEffect } from 'react'
import { message } from 'antd'
import useMemoizedFn from 'ahooks/es/useMemoizedFn'
import { TaskStatus } from '@apis/mindnote/next-agent-chat.type'
import { getNextAgentMessageList } from '@apis/mindnote/next-agent-chat'
import type { Event } from '@apis/mindnote/next-agent-chat.type'
import type { SendMessagePayload } from '../../types/message'
import { BLANK_CONVERSATION_ID } from '../../const'
import { useMcpEvent } from '../../../mcp/hooks/useMcpEvent'

const CONSTANTS = {
  PAGE_SIZE: 3000,
} as const

export function useMessageActions(
  setTaskStatus: (status: TaskStatus) => void,
  setEvents: (events: Event[] | ((prevEvents: Event[]) => Event[])) => void,
  wsSendMessage: (message: string) => void,
  conversationId?: string,
) {
  const { syncMcpTools } = useMcpEvent()
  const timer = useRef<ReturnType<typeof setTimeout>>()
  const nextPageNo = useRef(1)
  // 中止当前消息
  const abortMessage = useMemoizedFn(() => {
    clearTimeout(timer.current)
    setTaskStatus(TaskStatus.CANCELED)

    if (conversationId && conversationId !== BLANK_CONVERSATION_ID) {
      try {
        wsSendMessage(
          JSON.stringify({
            message_type: 'cancel',
            conversation_id: conversationId,
          }),
        )
      } catch (error) {
        console.error('取消任务失败:', error)
      }
    }
  })

  // 发送消息
  const sendMessage = useMemoizedFn(
    async (payload: SendMessagePayload, conversationIdAfterCreate = '') => {
      const innerConversationId = conversationIdAfterCreate || conversationId

      try {
        // 发送WebSocket消息
        try {
          await syncMcpTools()
          // 设置生成状态
          setTaskStatus(TaskStatus.IN_PROGRESS)

          // 发送格式化的消息到WebSocket
          const wsMessage = {
            message_type: payload.message_type || 'chat',
            conversation_id: innerConversationId,
            content: payload.content,
          }
          wsSendMessage?.(JSON.stringify(wsMessage))
        } catch (sendError) {
          console.error('发送WebSocket消息失败:', sendError)
          message.error('发送消息失败，请重试')
          setTaskStatus(TaskStatus.FAILED)
        }
      } catch (error) {
        console.error('发送消息失败:', error)
        message.error((error as Error).message || '发送失败，请重试')
        setTaskStatus(TaskStatus.FAILED)
      }
    },
  )

  // 加载历史消息列表
  const loadMessageList = useMemoizedFn(async (reload = false) => {
    if (!conversationId) {
      throw new Error('Conversation ID is required')
    }

    if (reload) {
      nextPageNo.current = 1
    }

    try {
      // 获取历史事件列表
      const res = await getNextAgentMessageList(
        conversationId,
        nextPageNo.current,
        CONSTANTS.PAGE_SIZE,
      )
      if (res && res.data) {
        const eventList = res.data.chat_event_list
        setEvents(prev => (reload ? eventList : [...prev, ...eventList]))
        setTaskStatus(res.data.task_status)
      }
    } catch (error) {
      console.error('加载消息列表失败:', error)
      message.error('加载历史消息失败')
    }
  })

  // 在组件卸载时清理
  useEffect(() => {
    return () => {
      clearTimeout(timer.current)
    }
  }, [])

  return {
    sendMessage,
    abortMessage,
    loadMessageList,
  }
}
