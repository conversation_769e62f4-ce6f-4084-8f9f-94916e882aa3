import useMemoizedFn from 'ahooks/es/useMemoizedFn'
import { useEffect } from 'react'
import { TaskStatus } from '@apis/mindnote/next-agent-chat.type'
import type { Event } from '@apis/mindnote/next-agent-chat.type'

import { tryParseToJsonObject } from '@bty/util'
import { useMcpStore } from '@/store/mcp'
import {
  canExecuteMcpTool,
  executeMcpTool,
  processMcpToolName,
} from '../../utils'

export function useMcpProcessor(
  latestEvent: Event,
  callback: (params: {
    unique_id: string
    result: string
    status: string
  }) => void,
) {
  const mcpList = useMcpStore(state => state.list)
  const processLatestMcpTool = useMemoizedFn((event: Event) => {
    const metadata = event?.content?.metadata
    if (
      canExecuteMcpTool(
        event,
        latestEvent,
        metadata,
        event?.content?.tool_call_id,
      )
    ) {
      console.log('判断最新逻辑是mcp相关工具 开始执行')
      const mcp = mcpList.find(
        item => item.functionId === metadata?.function_id,
      )
      const toolInput = tryParseToJsonObject(event?.content?.tool_input || '')
      const realToolName = processMcpToolName(metadata?.tool_name)

      if (mcp) {
        executeMcpTool(
          mcp,
          realToolName,
          toolInput,
          res => {
            console.log('mcp工具执行成功', res)
            console.log(
              'mcp工具执行成功- latestEvent and plan_step_state',
              latestEvent,
              latestEvent.plan_step_state,
            )
            if (latestEvent.plan_step_state === TaskStatus.PAUSE) {
              callback?.({
                unique_id: event?.content?.tool_call_id!,
                result: res.data,
                status: res.status === 'SUCCEEDED' ? 'success' : 'failed',
              })
            }
          },
          () => {
            if (latestEvent.plan_step_state === TaskStatus.PAUSE) {
              callback({
                unique_id: event.content.tool_call_id!,
                result: JSON.stringify({}),
                status: 'failed',
              })
            }
          },
        )
      } else {
        if (latestEvent.plan_step_state === TaskStatus.PAUSE) {
          callback({
            unique_id: event.content.tool_call_id!,
            result: JSON.stringify({}),
            status: 'failed',
          })
        }
      }
    }
  })

  useEffect(() => {
    processLatestMcpTool(latestEvent)
  }, [latestEvent])
}
