import { useWebSocket } from 'ahooks'
import useMemoizedFn from 'ahooks/es/useMemoizedFn'
import { useCallback, useEffect, useRef } from 'react'
import { message } from 'antd'
import {
  TaskStatus,
  EventType,
  EventActionType,
} from '@apis/mindnote/next-agent-chat.type'
import type { Event } from '@apis/mindnote/next-agent-chat.type'
import { createNextAgentSocketUrl } from '../../utils'
import type { WSEventData } from '../../types/message'
import { BLANK_CONVERSATION_ID } from '../../const'

const CONSTANTS = {
  HEARTBEAT_INTERVAL: 30000,
  HEARTBEAT_TIMEOUT: 33000, // ping超时时间
} as const

const CONNECTION_SUCCESS_TYPE = 'connection_success'

export function useWebSocketConnection(
  token: string,
  onEventReceived: (event: Event) => void,
  setTaskStatus: (status: TaskStatus) => void,
  conversationId?: string,
  generalConversationTitle?: (id: string, content: string) => void,
  scrollToBottom?: (animated: boolean) => void,
  onUpdateConversation?: (
    id: string,
    payload: { task_state?: TaskStatus },
  ) => void,
) {
  const isSwitchedRef = useRef<boolean>(true)
  const readyStateRef = useRef<number | undefined>()
  const wsSendMessageRef = useRef<((message: string) => void) | null>(null)
  const heartbeatTimer = useRef<NodeJS.Timeout>()
  const heartbeatTimeoutTimer = useRef<NodeJS.Timeout>()
  const waitingForPong = useRef<boolean>(false)

  const socketUrl = createNextAgentSocketUrl(
    '/v1/super_agent/chat/completions',
    {
      Authorization: token,
    },
  )

  // 清除心跳相关定时器
  const clearHeartbeat = useMemoizedFn(() => {
    if (heartbeatTimer.current) {
      clearTimeout(heartbeatTimer.current)
      heartbeatTimer.current = undefined
    }
    if (heartbeatTimeoutTimer.current) {
      clearTimeout(heartbeatTimeoutTimer.current)
      heartbeatTimeoutTimer.current = undefined
    }
    waitingForPong.current = false
  })

  // 处理ping超时
  const handlePingTimeout = useMemoizedFn(() => {
    console.warn('心跳超时，连接可能已断开')
    waitingForPong.current = false
    // 可以选择重连或者其他处理方式
    // 这里先清除心跳，让连接自然断开
    clearHeartbeat()
  })

  // 发送心跳消息
  const sendHeartbeat = useMemoizedFn(() => {
    if (waitingForPong.current) {
      console.log('正在等待pong响应，跳过本次ping')
      return
    }

    if (wsSendMessageRef.current && readyStateRef.current === 1) {
      try {
        wsSendMessageRef.current(JSON.stringify({ message_type: 'ping' }))
        waitingForPong.current = true


        // 设置ping超时定时器
        heartbeatTimeoutTimer.current = setTimeout(
          handlePingTimeout,
          CONSTANTS.HEARTBEAT_TIMEOUT,
        )


        console.log('发送ping，等待pong响应')
      } catch (error) {
        console.error('发送心跳消息失败:', error)
        clearHeartbeat()
      }
    } else {
      clearHeartbeat()
    }
  })

  // 处理pong响应
  const handlePongReceived = useMemoizedFn(() => {
    if (!waitingForPong.current) {
      return
    }

    console.log('收到pong响应')
    waitingForPong.current = false


    // 清除超时定时器
    if (heartbeatTimeoutTimer.current) {
      clearTimeout(heartbeatTimeoutTimer.current)
      heartbeatTimeoutTimer.current = undefined
    }

    // 安排下一次心跳
    heartbeatTimer.current = setTimeout(
      sendHeartbeat,
      CONSTANTS.HEARTBEAT_INTERVAL,
    )
  })

  // 启动心跳
  const startHeartbeat = useMemoizedFn(() => {
    clearHeartbeat() // 确保不会重复启动
    // 连接建立后立即发送第一个ping
    heartbeatTimer.current = setTimeout(sendHeartbeat, 1000)
  })

  // 使用useWebSocket hook
  const {
    sendMessage: wsSendMessage,
    latestMessage,
    connect,
  } = useWebSocket(socketUrl, {
    manual: true,
    onOpen: () => {
      console.log('WebSocket连接已建立')
      readyStateRef.current = 1
      startHeartbeat()
    },
    onClose: () => {
      console.log('WebSocket连接已关闭')
      readyStateRef.current = 0
      clearHeartbeat()
    },
    onError: () => {
      readyStateRef.current = 2
      message.error('连接出错，请刷新页面重试')
      setTaskStatus(TaskStatus.ConnectFail)
    },
  })

  // 更新 wsSendMessage 引用
  useEffect(() => {
    wsSendMessageRef.current = wsSendMessage
  }, [wsSendMessage, readyStateRef.current])

  // 验证消息基本格式
  const validateMessage = useMemoizedFn(
    (messageData: string): WSEventData | null => {
      try {
        const res: WSEventData = JSON.parse(messageData)

        if (!res.success) {
          message.error(res.message || '接收数据失败')
          return null
        }

        return res
      } catch (error) {
        console.error('解析WebSocket消息失败:', error)
        return null
      }
    },
  )

  // 验证会话ID匹配
  const validateConversationId = useMemoizedFn((data: WSEventData): boolean => {
    if (conversationId === BLANK_CONVERSATION_ID) {
      return false
    }

    if (data.data.conversation_id !== conversationId) {
      console.log(
        '会话ID不匹配:',
        data.data.conversation_id,
        'vs',
        conversationId,
      )
      return false
    }

    return true
  })

  // 验证聊天事件是否需要处理
  const shouldProcessChatEvent = useMemoizedFn((chatEvent: any): boolean => {
    if (!chatEvent) {
      return false
    }

    // TODO: 先hack处理 等待后续后端给出解决方案
    // 过滤掉文本块事件
    if (
      chatEvent.event_type === EventType.TEXT &&
      chatEvent.content.action_type === EventActionType.TextChunk
    ) {
      return false
    }

    return true
  })

  // 处理原始WebSocket消息（包括pong）
  const handleRawMessage = useMemoizedFn((messageData: string) => {
    try {
      const rawMessage = JSON.parse(messageData)
      // 处理pong响应
      if (rawMessage.data.message_type === 'pong') {
        handlePongReceived()
        return true // 表示已处理，不需要进一步处理
      }


      return false // 表示未处理，需要继续正常流程
    } catch (error) {
      // 如果不是JSON格式，继续正常流程
      return false
    }
  })

  // 处理接收到的WebSocket消息
  useEffect(() => {
    if (!latestMessage?.data) return

    const messageData = latestMessage.data as string


    // 首先检查是否是pong消息
    if (handleRawMessage(messageData)) {
      return // 如果是pong消息，直接返回
    }

    // 1. 验证消息格式
    const parsedData = validateMessage(messageData)
    if (!parsedData) return

    // 2. 验证会话ID
    if (!validateConversationId(parsedData)) return

    if (!isSwitchedRef.current) {
      if (
        parsedData.data.conversation_id === conversationId &&
        parsedData.data.type === CONNECTION_SUCCESS_TYPE
      ) {
        isSwitchedRef.current = true
      } else {
        return
      }
    }
    // 3. 获取聊天事件
    const { data } = parsedData
    const chatEvent = data.chat_event

    // 4. 验证是否需要处理该事件
    if (!shouldProcessChatEvent(chatEvent)) return

    // 5. 处理事件
    onEventReceived(chatEvent)

    // 6. 更新任务状态
    if (conversationId && conversationId !== BLANK_CONVERSATION_ID) {
      setTaskStatus(data.task_status)
      onUpdateConversation?.(conversationId, { task_state: data.task_status })
    }

    // 7. 滚动到底部
    setTimeout(() => {
      scrollToBottom?.(true)
    }, 100)
  }, [
    latestMessage,
    handleRawMessage,
    validateMessage,
    validateConversationId,
    shouldProcessChatEvent,
    onEventReceived,
    setTaskStatus,
    scrollToBottom,
    conversationId,
    onUpdateConversation,
  ])

  // 在组件挂载时尝试连接，卸载时清理心跳
  useEffect(() => {
    connect()
    return () => {
      clearHeartbeat()
    }
  }, [connect, clearHeartbeat])

  const switchConversation = useCallback(
    (conversation_id: string) => {
      if (!wsSendMessage) {
        console.warn('WebSocket未连接，无法切换会话')
        return
      }

      try {
        setTaskStatus(TaskStatus.NOT_STARTED)
        wsSendMessage(
          JSON.stringify({
            message_type: 'switch_conversation',
            conversation_id,
          }),
        )
        isSwitchedRef.current = false
      } catch (error) {
        console.error('切换会话失败:', error)
      }
    },
    [wsSendMessage],
  )

  return {
    wsReadyState: readyStateRef.current,
    wsSendMessage,
    clearHeartbeat,
    switchConversation,
    conversationIsReady: isSwitchedRef.current,
  }
}
