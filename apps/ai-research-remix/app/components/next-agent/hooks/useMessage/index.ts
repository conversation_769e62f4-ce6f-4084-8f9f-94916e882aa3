import { shallow } from 'zustand/shallow'
import { useCallback, useEffect, useState } from 'react'
import { useLoaderData } from '@remix-run/react'
import { TaskStatus, ToolType } from '@apis/mindnote/next-agent-chat.type'
import { BLANK_CONVERSATION_ID } from '../../const'
import {
  useLatestTaskEvent,
  useTask,
  useTaskEvent,
  useTaskEventList,
} from '@/store/task-event'
import { useScrollManagement } from './useScrollManagement'
import { useWebSocketConnection } from './useSocketConnection'
import { useMessageActions } from './useMessageActions'
import { useMcpProcessor } from './useMcpProcessor'

const CONSTANTS = {
  SCROLL_DELAY: 200,
} as const

export function useMessage(
  messageInputRef: React.RefObject<HTMLTextAreaElement>,
  scrollRef: React.RefObject<HTMLDivElement>,
  generalConversationTitle?: (id: string, content: string) => void,
  onUpdateConversation?: (
    id: string,
    payload: { task_state?: TaskStatus },
  ) => void,
) {
  const { token } = useLoaderData<{ token: string }>()

  const [taskStatus, setTaskStatus] = useState(TaskStatus.NOT_STARTED)

  const taskId = useTask()

  const { appendEvent, clearEvents, setEvents } = useTaskEvent(
    state => ({
      appendEvent: state.append,
      clearEvents: state.clear,
      setEvents: state.setEvents,
    }),
    shallow,
  )

  const latestEvent = useLatestTaskEvent()

  const messageList = useTaskEventList()

  const { scrollToBottom, resetScrollHeight, autoScroll, setAutoScroll } =
    useScrollManagement(scrollRef)

  const {
    wsSendMessage,
    clearHeartbeat,
    switchConversation,
    conversationIsReady,
    wsReadyState,
  } = useWebSocketConnection(
    token,
    appendEvent,
    setTaskStatus,
    taskId,
    generalConversationTitle,
    scrollToBottom,
    onUpdateConversation,
  )

  const { sendMessage, abortMessage, loadMessageList } = useMessageActions(
    setTaskStatus,
    setEvents,
    wsSendMessage,
    taskId,
  )
  const mcpProcessorCallback = useCallback(
    async (params: { unique_id: string; result: string }) => {
      await sendMessage({
        message_type: ToolType.MCP,
        content: JSON.stringify(params),
      })
      setTimeout(async () => {
        await sendMessage({
          message_type: ToolType.TakeOverExit,
          content: '',
        })
      }, 100)
    },
    [sendMessage],
  )
  // 处理mcp流程的工具
  useMcpProcessor(latestEvent!, mcpProcessorCallback)

  // 会话ID变化时加载历史消息和重置状态
  useEffect(() => {
    // 清理旧状态
    clearEvents()
    setTaskStatus(TaskStatus.NOT_STARTED)

    if (taskId && taskId !== BLANK_CONVERSATION_ID) {
      loadMessageList(true)
      switchConversation(taskId)
      setTimeout(() => {
        resetScrollHeight()
        scrollToBottom(false)
        setAutoScroll(true)
      }, CONSTANTS.SCROLL_DELAY)
    }

    // 自动聚焦输入框
    const nowActive = document.activeElement
    if (!['INPUT', 'TEXTAREA'].includes(nowActive?.tagName || '')) {
      messageInputRef.current?.focus()
    }
  }, [
    taskId,
    loadMessageList,
    resetScrollHeight,
    scrollToBottom,
    messageInputRef,
    switchConversation,
    wsReadyState,
  ])

  useEffect(() => {
    return () => {
      clearHeartbeat()
    }
  }, [clearHeartbeat])

  return {
    taskStatus,
    sendMessage,
    messageList,
    abortMessage,
    conversationIsReady,
    autoScroll,
    setAutoScroll,
    wsReadyState,
  }
}
