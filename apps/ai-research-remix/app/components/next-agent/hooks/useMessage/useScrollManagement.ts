// hooks/useScrollManagement.ts
import useMemoizedFn from 'ahooks/es/useMemoizedFn'
import { useState } from 'react'

export function useScrollManagement(
  scrollRef: React.RefObject<HTMLDivElement>,
) {
  const [autoScroll, setAutoScroll] = useState(false)
  const scrollToBottom = useMemoizedFn((animated = true) => {
    if (autoScroll) {
      scrollRef.current?.scrollTo({
        top: scrollRef.current?.scrollHeight,
        behavior: animated ? 'smooth' : 'auto',
      })
    }
  })

  const resetScrollHeight = useMemoizedFn(() => {
    const scrollContentElement = scrollRef.current?.querySelector(
      '.scroll-view-content',
    ) as HTMLDivElement | null
    if (scrollContentElement) {
      scrollContentElement.style.minHeight = 'auto'
    }
  })

  return {
    scrollToBottom,
    resetScrollHeight,
    autoScroll,
    setAutoScroll,
  }
}
