import {
  createNextAgentConversation,
  getNextAgentConversationList,
  deleteNextAgentConversation,
  generateNextAgentConversationTitle,
  updateNextAgentConversation,
  pollConversationIds,
} from '@apis/mindnote/next-agent-chat'
import type { Conversation } from '@apis/mindnote/next-agent-chat.type'
import { TaskStatus } from '@apis/mindnote/next-agent-chat.type'
import useMemoizedFn from 'ahooks/es/useMemoizedFn'
import { useCallback, useEffect, useRef, useState } from 'react'
import { message } from 'antd'
import { uniqBy } from 'lodash-es'
import { BLANK_CONVERSATION_ID, NEW_TASK } from '../const'
import { useTask, useTaskEvent } from '@/store/task-event'

export type ConversationProps = Record<string, any>

// 定义完成状态的任务状态
const COMPLETED_STATUSES: TaskStatus[] = [
  TaskStatus.COMPLETED,
  TaskStatus.FAILED,
  TaskStatus.CANCELED,
]

export function useConversation(
  setConversationListCollapsed?: (collapsed: boolean) => void,
) {
  const taskId = useTask()

  const setTaskId = useTaskEvent(state => state.setTaskId)

  const [conversationList, setConversationList] = useState<Conversation[]>([])

  const [loading, setLoading] = useState(false)
  const [updateLoading, setUpdateLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const nextPageNo = useRef(1)
  const pollingTimerRef = useRef<NodeJS.Timeout | null>(null)
  const [isPolling, setIsPolling] = useState(false)

  const onCreateConversation = useMemoizedFn(async () => {
    const title = `${NEW_TASK}${conversationList.length > 0 ? conversationList.length : ''}`
    const res = await createNextAgentConversation(title)
    return res.conversation_id
  })

  const loadConversationList = useCallback(
    async (reload = false, callback?: (hasList: boolean) => void) => {
      // if (loading) {
      //   return
      // }
      if (reload) {
        nextPageNo.current = 1
      }
      setLoading(true)
      const res = await getNextAgentConversationList(
        nextPageNo.current,
        20,
      ).finally(() => {
        setLoading(false)
      })
      if (res) {
        const list = res.data ?? []

        let newList: Conversation[]
        if (nextPageNo.current === 1) {
          // 首次加载或重新加载，直接使用新数据
          newList = list
        } else {
          // 分页加载，合并数据并用lodash去重
          const combinedList = [...conversationList, ...list]
          newList = uniqBy(combinedList, 'conversation_id')
        }

        setConversationList(newList)
        setHasMore(res.total > newList.length)
        if (hasMore) {
          nextPageNo.current++
        }
        await callback?.(!res.total)
        return newList
      }
      return []
    },
    [conversationList, hasMore, loading],
  )

  const onCreateBlankConversation = useMemoizedFn(async () => {
    if (taskId === BLANK_CONVERSATION_ID) {
      // message.info('当前已是最新')
      return
    }
    setTaskId(BLANK_CONVERSATION_ID)
    setConversationList(prev => {
      const prevList = prev.filter(
        item => item.conversation_id !== BLANK_CONVERSATION_ID,
      )
      const newList = [
        {
          title: '新任务',
          has_messages: false,
          conversation_id: BLANK_CONVERSATION_ID,
          created_at: new Date().toISOString(),
          conversation_config: {},
        } as Conversation,
        ...prevList,
      ]
      return newList
    })
  })

  const onCreateConversationAndRefresh = useMemoizedFn(async () => {
    const res = await onCreateConversation()
    if (res) {
      setTaskId(res)
      loadConversationList(true)
      return res
    }
  })

  const onConversationTitleGenerate = useMemoizedFn(
    async (id: string, content: string) => {
      await generateNextAgentConversationTitle(id, content)
      setTimeout(async () => {
        await loadConversationList(true)
      }, 100)
    },
  )

  const onConversationDelete = useCallback(
    async (id: string) => {
      await deleteNextAgentConversation(id)
      setConversationList(prev => {
        const newList = prev.filter(item => item.conversation_id !== id)
        if (taskId === id) {
          setTaskId(BLANK_CONVERSATION_ID)
        }
        if (newList.length === 0) {
          onCreateBlankConversation()
        }
        return newList
      })
    },
    [conversationList, taskId],
  )

  const onUpdateConversation = useMemoizedFn(
    async (
      id: string,
      payload: {
        title?: string
        task_state?: TaskStatus
        conversation_config?: Record<string, any>
      },
    ) => {
      if (updateLoading) {
        message.warning('配置保存中，请稍后更改')
        return
      }
      setUpdateLoading(true)
      try {
        if (payload?.title) {
          await updateNextAgentConversation(id, payload)
        }
        const index = conversationList.findIndex(
          item => item.conversation_id === id,
        )
        setConversationList(prev => {
          const newList = [...prev]
          newList[index] = {
            ...newList[index],
            ...payload,
          }
          return newList
        })
        setUpdateLoading(false)
      } catch {
        setUpdateLoading(false)
      }
    },
  )

  const loadMoreConversations = useMemoizedFn(async () => {
    if (!hasMore || loading) {
      return
    }
    await loadConversationList(false)
  })

  // 检查是否有未完成的任务
  const hasIncompleteTasks = useMemoizedFn(() => {
    const hasIncomplete = conversationList.some(conversation => {
      const taskState = conversation.task_state || conversation.status
      return taskState && !COMPLETED_STATUSES.includes(taskState)
    })
    return hasIncomplete
  })

  // 轮询任务状态
  const pollTaskStatus = useMemoizedFn(async () => {
    if (isPolling) return

    const incompleteConversations = conversationList.filter(conversation => {
      const taskState = conversation.task_state
      return taskState && !COMPLETED_STATUSES.includes(taskState)
    })

    if (incompleteConversations.length === 0) {
      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      stopPolling()
      return
    }

    const conversationIds = incompleteConversations
      .map(c => c.conversation_id)
      .slice(0, 20) // 只拿前20个，保证性能

    try {
      setIsPolling(true)
      const updateConversationList = await pollConversationIds(conversationIds)
      if (updateConversationList?.length) {
        // 更新对话列表中的任务状态
        setConversationList(prev => {
          return prev.map(conversation => {
            const updatedStatus = updateConversationList.find(
              (item: any) =>
                item.conversation_id === conversation.conversation_id,
            )
            if (updatedStatus) {
              return {
                ...conversation,
                title: updatedStatus.title,
                task_state: updatedStatus.task_state,
                status: updatedStatus.status,
              }
            }
            return conversation
          })
        })
      }
    } catch (error) {
      console.error('轮询任务状态失败:', error)
    } finally {
      setIsPolling(false)
    }
  })

  // 开始轮询
  const startPolling = useMemoizedFn(() => {
    if (pollingTimerRef.current) return

    pollingTimerRef.current = setInterval(() => {
      pollTaskStatus()
    }, 5000) // 每5000秒轮询一次
  })

  // 停止轮询
  const stopPolling = useMemoizedFn(() => {
    if (pollingTimerRef.current) {
      clearInterval(pollingTimerRef.current)
      pollingTimerRef.current = null
    }
  })

  // 监听对话列表变化，决定是否需要轮询
  useEffect(() => {
    if (hasIncompleteTasks()) {
      startPolling()
    } else {
      stopPolling()
    }

    return () => {
      stopPolling()
    }
  }, [conversationList])

  async function init() {
    await loadConversationList(true, setConversationListCollapsed)
    if (!taskId) {
      onCreateBlankConversation()
    }
  }

  useEffect(() => {
    init()
  }, [])

  return {
    conversationList,
    onCreateBlankConversation,
    onCreateConversationAndRefresh,
    loadConversationList,
    hasMoreConversation: hasMore,
    conversationLoading: loading,
    updateLoading,
    onConversationDelete,
    onConversationTitleGenerate,
    onUpdateConversation,
    loadMoreConversations,
  }
}
