import { useRef, useState } from 'react'

export interface MessageInputActionRef {
  setContent: (text: string) => void
}

export function useChatUI() {
  const [conversationListCollapsed, setConversationListCollapsed] =
    useState(true)

  const scrollRef = useRef<HTMLDivElement>(null)
  const messageInputRef = useRef<HTMLTextAreaElement>(null)
  const messageInputActionRef = useRef<MessageInputActionRef>(null)

  return {
    conversationListCollapsed,
    setConversationListCollapsed,
    scrollRef,
    messageInputRef,
    messageInputActionRef,
  }
}
