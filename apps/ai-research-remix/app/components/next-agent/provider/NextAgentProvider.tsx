import type { PropsWithChildren } from 'react'
import { createContext, useContext, useMemo } from 'react'
import { noop } from 'lodash-es'
import { TaskStatus } from '@apis/mindnote/next-agent-chat.type'
import type { ConversationProps } from '../hooks/useConversation'
import { useConversation } from '../hooks/useConversation'
import { useChatUI } from '../hooks/useChatUI'
import { useMessage } from '../hooks/useMessage'
import { WhichEventProvider } from './WhichEventProvider'

type NextAgentContextProps = ReturnType<typeof useConversation> &
  ReturnType<typeof useChatUI> &
  ReturnType<typeof useMessage>

const NextAgentContext = createContext<NextAgentContextProps>({
  conversationList: [],
  messageList: [],
  conversationLoading: false,
  updateLoading: false,
  hasMoreConversation: true,
  loadConversationList: noop as any,
  loadMoreConversations: noop as any,
  onCreateConversationAndRefresh: noop as any,
  onCreateBlankConversation: noop as any,
  onConversationDelete: noop as any,
  onConversationTitleGenerate: noop as any,
  conversationListCollapsed: true,
  setConversationListCollapsed: noop as any,
  sendMessage: noop as any,
  abortMessage: noop as any,
  taskStatus: TaskStatus.NOT_STARTED,
  scrollRef: null as any,
  messageInputRef: null as any,
  messageInputActionRef: null as any,
  onUpdateConversation: noop as any,
  conversationIsReady: true,
  autoScroll: false,
  setAutoScroll: noop as any,
  readyState: undefined as any,
})

export function NextAgentProvider(props: PropsWithChildren<ConversationProps>) {
  const chatUIProps = useChatUI()
  const conversationProps = useConversation(
    chatUIProps.setConversationListCollapsed,
  )

  const messageProps = useMessage(
    chatUIProps.messageInputRef,
    chatUIProps.scrollRef,
    conversationProps.onConversationTitleGenerate,
    conversationProps.onUpdateConversation,
  )

  const value = useMemo(() => {
    return {
      ...conversationProps,
      ...chatUIProps,
      ...messageProps,
      conversationType: props.type,
    }
  }, [conversationProps, chatUIProps, messageProps, props.type])

  return (
    <NextAgentContext.Provider value={value}>
      <WhichEventProvider stages={messageProps.messageList}>
        {props.children}
      </WhichEventProvider>
    </NextAgentContext.Provider>
  )
}

export function useNextAgent() {
  const context = useContext(NextAgentContext)
  if (!context) {
    throw new Error('useNextAgent must be used within a NextAgentProvider')
  }
  return context
}
