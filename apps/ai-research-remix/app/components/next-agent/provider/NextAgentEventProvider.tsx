import type { Reference } from '@apis/mindnote/chat.type'
import type { PropsWithChildren } from 'react'
import { createContext, useContext } from 'react'

export interface NextAgentEvents {
  onReferenceClick?: (reference: Reference) => void
  onReferencesClick?: (references: Reference[], id: string) => void
  onMessageSendHook?: () => void
  onConversationChangeHook?: (newId: string) => void
}

export const NextAgentEventContext = createContext<NextAgentEvents>({})

export function NextAgentEventProvider(
  props: PropsWithChildren<NextAgentEvents>,
) {
  const { children, ...nextAgentEvents } = props
  return (
    <NextAgentEventContext.Provider value={nextAgentEvents}>
      {children}
    </NextAgentEventContext.Provider>
  )
}

export function useNextAgentEvent() {
  const context = useContext(NextAgentEventContext)
  if (!context) {
    throw new Error(
      'useNextAgentEvent must be used within a NextAgentEventProvider',
    )
  }
  return context
}
