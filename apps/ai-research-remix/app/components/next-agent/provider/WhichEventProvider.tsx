import { createContext, useContext, useMemo, useState } from 'react'
import { EventType, ToolType } from '@apis/mindnote/next-agent-chat.type'
import type { Event, ToolTypeValue } from '@apis/mindnote/next-agent-chat.type'
import { useMemoizedFn } from 'ahooks'

interface WhichEventContextType {
  event?: string
  stages?: { id: string; events: Event[] }[]
  setEvent: (eventId?: string) => void
}

const WhichEventContext = createContext<WhichEventContextType | null>(null)

type WhichEventContextProps = React.PropsWithChildren<{
  stages?: WhichEventContextType['stages']
}>

export function WhichEventProvider({
  stages,
  children,
}: WhichEventContextProps) {
  const [event, setEvent] = useState<string>()

  const _setEvent = useMemoizedFn((eventId?: string) => {
    const noStages = !stages?.length
    if (noStages) {
      return
    }

    // 没有指定 eventId，不设置快照
    if (!eventId) {
      setEvent(undefined)
      return
    }

    const event = findEvent(stages, eventId)
    if (!event) {
      return
    }

    setEvent(eventId)
  })

  const value = useMemo(
    () => ({
      event,
      stages,
      setEvent: _setEvent,
    }),
    [event, stages],
  )

  return (
    <WhichEventContext.Provider value={value}>
      {children}
    </WhichEventContext.Provider>
  )
}

const ignore: ToolTypeValue[] = [ToolType.BrowserUseTakeover]

export function useEventSnapshot(): {
  snapshot: Readonly<Event> | null
  allSnapshots: ReadonlyArray<Event> | undefined
  setEventSnapshot: (eventId?: string) => void
} {
  const context = useContext(WhichEventContext)

  if (!context) {
    throw new Error('useEventSnapshot must be used within a WhichEventProvider')
  }

  const id = context.event
  const stages = context.stages
  const snapshot = id && stages ? findEvent(stages, id) : null

  const allSnapshots = useMemo(
    () =>
      stages?.reduce<Event[]>((result, stage) => {
        // 快照只显示 tool_call 事件
        const events = stage.events
          .filter(el => el.event_type === EventType.TOOL_CALL)
          .filter(el => !ignore.includes(el.content.tool_name as ToolTypeValue))
        return [...result, ...events]
      }, []),
    [stages],
  )

  return {
    snapshot,
    allSnapshots,
    setEventSnapshot: context.setEvent,
  }
}

export function useEventInProgress() {
  const context = useContext(WhichEventContext)

  if (!context) {
    throw new Error(
      'useEventInProgress must be used within a WhichEventProvider',
    )
  }

  const inProgress = useMemo(() => {
    const allEvents = context.stages?.flatMap(stage => stage.events)
    const lastIndex = allEvents?.findLastIndex(
      event =>
        event.event_type === EventType.TOOL_CALL &&
        !ignore.includes(event.content.tool_name as ToolTypeValue),
    )
    if (lastIndex === undefined || lastIndex < 0) {
      return null
    }
    return allEvents![lastIndex]
  }, [context.stages])

  return inProgress
}

export function findEvent(
  stages: NonNullable<WhichEventContextType['stages']>,
  eventId: string,
) {
  for (const stage of stages) {
    const foundEvent = stage.events.find(item => item.event_id === eventId)
    if (foundEvent) {
      return foundEvent
    }
  }
  return null
}
