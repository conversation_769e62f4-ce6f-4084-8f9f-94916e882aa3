import {
  EventActionType,
  TaskStatus,
} from '@apis/mindnote/next-agent-chat.type'
import React from 'react'
import { type MessageRenderProps } from '@/store/task-event'
import McpContent from './mcp'
import Action from './action'
import Content from './content'
import Attachment from './attachment'
import Operation from './operation'

export type MessageItemProps = MessageRenderProps & {
  supportMarkdown?: boolean
}

function MessageItem({
  action,
  content,
  mcpContent,
  attachment,
  operation,
  base,
  supportMarkdown = false,
}: MessageItemProps) {
  const showAttachment =
    !!attachment &&
    base?.plan_step_state === TaskStatus.COMPLETED &&
    (action?.action_type === EventActionType.StepSummary ||
      action.action_type === EventActionType.Summary)

  // 处理attachment数组的渲染
  const renderAttachments = () => {
    if (!showAttachment || !attachment) return null

    // 如果是数组，渲染多个Attachment组件
    if (Array.isArray(attachment)) {
      return (
        <div className='flex items-center w-100% gap-24px overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100'>
          {attachment.map((item, index) => (
            <div key={`${item.file_name}-${index}`} className='flex-shrink-0'>
              <Attachment {...item} />
            </div>
          ))}
        </div>
      )
    }

    // 如果是单个对象，渲染单个Attachment组件
    return <Attachment {...attachment} />
  }

  return (
    <div
      className='flex flex-col items-start text-[14px] rounded-[4px]'
      data-event-id={base?.event_id}
    >
      {!mcpContent && action && <Action {...action} base={base} />}
      {content && (
        <Content {...content} base={base} supportMarkdown={supportMarkdown} />
      )}
      {renderAttachments()}
      {mcpContent && <McpContent {...mcpContent} base={base} />}
      {operation && <Operation {...operation} base={base} />}
    </div>
  )
}

export default React.memo(MessageItem)
