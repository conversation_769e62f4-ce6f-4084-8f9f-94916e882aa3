import { useMemo, type FC } from 'react'
import type { Attachment as AttachmentType } from '@apis/mindnote/next-agent-chat.type'
import classNames from 'classnames'
import { getFileIconByType } from '@/util/file'
import { useTaskArtifact } from '@/store/task-event'
import { Tabs, useTaskStore } from '@/store/task'
import { Icon } from '../../../base/icon'

export interface AttachmentProps extends AttachmentType {
  className?: string
}

const Attachment: FC<AttachmentProps> = props => {
  const {
    file_name,
    file_type,
    className = '',
    file_url,
    isSummary = false,
  } = props
  const showPanel = useTaskStore(state => state.showPanel)

  const setActiveTab = useTaskStore(state => state.setActiveTab)

  const { open, close } = useTaskArtifact()

  // 获取文件类型图标
  const icon = useMemo(() => {
    const icon = getFileIconByType(file_type || '')
    return icon
  }, [file_type])

  const handleClick = () => {
    showPanel()
    setActiveTab(Tabs.Artifact)
    if (isSummary) {
      close()
    } else {
      open({
        path: file_url!,
        file_name: file_name || '',
        file_type: file_type || '',
      })
    }
  }
  return (
    <div
      className={classNames(
        'flex items-center p-12px bg-white min-w-220px box-border rounded-[12px] border border-[#E6E6E6] mt-6px cursor-pointer hover:bg-[rgba(228,228,228,0.5)]',
        className,
      )}
      onClick={() => handleClick()}
    >
      {isSummary ? (
        <span className='flex items-center justify-center w-24px h-24px rounded-[8px] mr-4px text-32px'>
          <Icon
            icon='i-icons-next-agent-file'
            className='text-#8d8d99 size-24px'
          />
        </span>
      ) : (
        <span className='flex items-center justify-center w-32px h-32px bg-#F5F6FA rounded-[8px] mr-8px text-20px'>
          {icon}
        </span>
      )}

      <span className='text-14px/22px text-[#222]'>{file_name}</span>
    </div>
  )
}

export default Attachment
