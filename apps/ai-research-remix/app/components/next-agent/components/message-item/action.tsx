// import { IconFont } from '@bty/components'
import type { FC, ReactNode } from 'react'
import { cn } from '@bty/util'
import { EventType } from '@apis/mindnote/next-agent-chat.type'
import { Icon } from '../../../base/icon'
import type { MessageRenderProps } from '@/store/task-event'
import { useEventSnapshot } from '../../provider/WhichEventProvider'
import { Tabs, useTaskStore } from '@/store/task'

export interface ActionProps {
  icon: string
  name?: string
  className?: string
  arguments?: string[]
  base: MessageRenderProps['base']
}

export type IconType =
  | 'prepare'
  | 'plan_analyze'
  | 'file_operator'
  | 'mcp'
  | 'update'
  | 'browser_use'
  | 'tool_call'
  | 'code_execute'
  | 'web_fetch'
  | 'create_file'
  | 'str_replace'
  | 'terminal_operator'
  | 'read_file'
  | 'web_search'

// 根据图标类型返回对应的图标组件
function getIconComponent(iconType: IconType): ReactNode {
  switch (iconType) {
    case 'prepare':
    case 'tool_call':
      return (
        <Icon
          icon='i-icons-next-agent-prepare'
          size='size-16px'
          className='text-#8D8D99 mr-4px'
        />
      )

    case 'str_replace':
    case 'update':
      return (
        <Icon
          icon='i-icons-next-agent-update'
          size='size-16px'
          className='text-#8D8D99 mr-4px'
        />
      )
    case 'read_file':
      return (
        <Icon
          icon='i-icons-next-agent-refer'
          size='size-16px'
          className='text-#8D8D99 mr-4px'
        />
      )
    case 'create_file':
    case 'file_operator':
      return (
        <Icon
          icon='i-icons-next-agent-file'
          size='size-16px'
          className='text-#8D8D99 mr-4px'
        />
      )

    case 'plan_analyze':
      return (
        <Icon
          icon='i-icons-next-agent-plan_analyze'
          size='size-16px'
          className='text-#8D8D99 mr-4px'
        />
      )

    case 'mcp':
      return (
        <Icon
          icon='i-icons-next-agent-mcp'
          size='size-16px'
          className='text-#8D8D99 mr-4px'
        />
      )

    case 'browser_use':
      return (
        <Icon
          icon='i-icons-search-network'
          size='size-16px'
          className='text-#8D8D99 mr-4px'
        />
      )

    case 'code_execute':
      return (
        <Icon
          icon='i-icons-next-agent-code'
          size='size-16px'
          className='text-#8D8D99 mr-4px'
        />
      )

    case 'web_fetch':
    case 'web_search':
      return (
        <Icon
          icon='i-icons-next-agent-analysis'
          size='size-16px'
          className='text-#8D8D99 mr-4px'
        />
      )

    case 'terminal_operator':
      return (
        <Icon
          icon='i-icons-next-agent-code'
          size='size-16px'
          className='text-#8D8D99 mr-4px'
        />
      )

    default:
      return (
        <Icon
          icon='i-icons-search-network'
          size='size-16px'
          className='text-#8D8D99 mr-4px'
        />
      )
  }
}

const Action: FC<ActionProps> = props => {
  const { icon, name, className = '', arguments: args, base } = props

  const { setEventSnapshot } = useEventSnapshot()
  const showPanel = useTaskStore(state => state.showPanel)
  const setActiveTab = useTaskStore(state => state.setActiveTab)

  const handleClick = () => {
    if (base?.event_type === EventType.PLAN_ANALYZE) {
      return
    }
    setEventSnapshot(base?.event_id)
    showPanel()
    setActiveTab(Tabs.Computer)
  }

  if (!name || !icon) {
    return null
  }

  const iconComponent = getIconComponent(icon as IconType)

  return (
    <div
      onClick={() => handleClick()}
      className={cn(
        'cursor-pointer flex items-center px-6px py-4px my-4px rd-10px text-14px/22px bg-[rgba(228,228,228,0.5)] max-w-full overflow-hidden c-#3F3F44 !hover:bg-[rgba(228,228,228,0.9)]',
        className,
      )}
    >
      {iconComponent}
      {name && <div className='shrink-0 text-14px/22px c-#3F3F44'>{name}</div>}
      {args && (
        <div className='max-w-560px ml-4px c-#8D8D99 truncate'>
          {args?.join(' ')}
        </div>
      )}
    </div>
  )
}

export default Action
