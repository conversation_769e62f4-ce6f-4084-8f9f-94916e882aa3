import type { FC } from 'react'
import { Markdown } from '@bty/components'
import classNames from 'classnames'
import { EventActionType } from '@apis/mindnote/next-agent-chat.type'
import { useLatestTaskEvent, type MessageRenderProps } from '@/store/task-event'
import { EventType } from '../../types/message'

export interface ContentProps {
  content?: string | undefined
  className?: string
  base: MessageRenderProps['base']
  supportMarkdown?: boolean
}

const Content: FC<ContentProps> = ({
  content,
  className = '',
  base,
  supportMarkdown = false,
}) => {
  const latestEvent = useLatestTaskEvent()

  // 根据事件类型渲染不同的样式
  if (base?.event_type === EventType.USER_INPUT) {
    return (
      <div
        className={classNames(
          'bg-#EFECFF text-14px/22px px-12px py-6px rd-10px inline-flex whitespace-pre-wrap break-words c-#17171D',
          {
            'text-16px/24px rd-12px':
              base?.action_type === EventActionType.QuestionInput,
          },
        )}
      >
        {content}
      </div>
    )
  }

  if (base?.event_type === EventType.ASK_USER) {
    const isLatest = base?.event_id === latestEvent?.event_id
    console.log(base, 'icnidsnaisd-base ASK_USER')
    return (
      <div
        className={classNames(
          'text-16px/24px p-10px rd-10px inline-flex whitespace-pre-wrap break-words c-#3F3F44 w-100%',
          {
            'bg-[rgba(56,166,255,0.08)]!': isLatest,
          },
        )}
      >
        {content}
      </div>
    )
  }

  // 默认样式 - 渲染内容
  const renderContent = () => {
    if (supportMarkdown) {
      return (
        <Markdown
          content={content}
          overrides={{
            root: '[&>:only-child]:!m-0 [&]:text-#3F3F44 [&]:text-14px/22px',
          }}
        />
      )
    }
    return content
  }

  return (
    <div
      className={classNames(
        'my-4px flex-1 flex flex-col text-14px/22px c-#3F3F44',
        className,
      )}
    >
      {renderContent()}
    </div>
  )
}

export default Content
