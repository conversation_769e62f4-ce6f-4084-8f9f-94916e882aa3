import { useState, type FC } from 'react'

import {
  EventStatus,
  TaskStatus,
  ToolType,
  type Operation as OperationType,
} from '@apis/mindnote/next-agent-chat.type'
import classNames from 'classnames'
import { Button } from 'antd'
import { useMemoizedFn } from 'ahooks'
import NiceModal from '@ebay/nice-modal-react'
import { useNextAgent } from '../../provider/NextAgentProvider'
import { TaskComputerUseModal } from '../../task/task-computer-use-modal'
import {
  useLatestTaskEvent,
  useTask,
  type MessageRenderProps,
} from '@/store/task-event'

export interface OperationProps extends OperationType {
  className?: string
  onClick?: (type: string) => void
  content?: string
  base: MessageRenderProps['base']
}

const Operation: FC<OperationProps> = props => {
  const { className, content, base } = props
  const [isClicked, setIsClicked] = useState(false)
  const { sendMessage, taskStatus } = useNextAgent()

  const taskId = useTask()

  const latestEvent = useLatestTaskEvent()

  const isLatest = latestEvent?.event_id === base?.event_id
  const disabled =
    base?.event_status !== EventStatus.RUNNING ||
    taskStatus === TaskStatus.COMPLETED ||
    taskStatus === TaskStatus.CANCELED ||
    !isLatest

  const handleOperationClick = useMemoizedFn((params: Record<string, any>) => {
    setIsClicked(true)
    sendMessage({
      message_type: params?.operation_type,
      content: '',
    })

    if (params?.operation_type === ToolType.TakeOver) {
      NiceModal.show(TaskComputerUseModal, {
        owner: 'human',
        taskId,
        callback: params => {
          sendMessage({
            message_type: params.message_type,
            content: '',
          })
        },
      })
    }
  })

  return (
    <div
      className={classNames(
        'flex flex-1 w-full items-center p-12px bg-white rounded-[12px] border border-[#E6E6E6] mt-6px cursor-pointer hover:bg-[rgba(228,228,228,0.5)]',
        className,
      )}
    >
      {content && (
        <div className='m-w-70% ml-4px c-#8D8D99 truncate'>{content}</div>
      )}

      <div className='flex items-center gap-12px ml-auto'>
        <Button
          onClick={() => {
            handleOperationClick({ operation_type: ToolType.JumpOver })
          }}
          disabled={disabled || isClicked}
        >
          尝试跳过
        </Button>
        <Button
          onClick={() => {
            // TODO: 接管不需要手动触发，由 后端 自动触发 不调用 ventActionType.TakeOver逻辑
            handleOperationClick({ operation_type: ToolType.TakeOver })
          }}
          disabled={disabled || isClicked}
          type='primary'
          className='text-white'
        >
          立即接管
        </Button>
      </div>
    </div>
  )
}

export default Operation
