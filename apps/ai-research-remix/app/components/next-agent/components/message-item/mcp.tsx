import { useEffect, useMemo, useState, type FC, useCallback } from 'react'
import { message, Tooltip } from 'antd'
import { tryParseToJsonObject } from '@bty/util'
import { TaskStatus, ToolType } from '@apis/mindnote/next-agent-chat.type'
import { type MessageRenderProps } from '@/store/task-event'
import { Icon } from '../../../base/icon'
import { useNextAgent } from '../../provider/NextAgentProvider'
import { useMcpStore } from '@/store/mcp'
import { Tabs, useTaskStore } from '@/store/task'
import { useEventSnapshot } from '../../provider/WhichEventProvider'
import {
  MCP_STATUS_MAP,
  STATUS_ICON_MAP,
  type McpStatus,
  executeMcpTool,
  processMcpToolName,
  delayedExecution,
  isClientPlatform,
} from '../../utils'

// 组件属性接口
export interface McpContentProps {
  content?: string
  className?: string
  base?: MessageRenderProps['base']
  status?: string
  metadata?: Record<string, any>
  tool_call_id?: string
}

const McpContent: FC<McpContentProps> = ({
  className = '',
  base,
  metadata,
  status: initialStatus,
  tool_call_id,
}) => {
  const { sendMessage } = useNextAgent()
  const mcpList = useMcpStore(state => state.list)
  const showPanel = useTaskStore(state => state.showPanel)
  const setActiveTab = useTaskStore(state => state.setActiveTab)
  const { setEventSnapshot } = useEventSnapshot()

  const [status, setStatus] = useState<string>(initialStatus!)
  // 同步外部状态变化
  useEffect(() => {
    if (initialStatus !== 'ready') {
      setStatus(initialStatus!)
    }
  }, [initialStatus])

  // 解析真实工具名称
  const realToolName = useMemo(() => {
    return processMcpToolName(metadata?.tool_name)
  }, [metadata?.tool_name])

  // 发送MCP消息的通用函数
  const sendMcpMessage = useCallback(
    async (messageData: {
      unique_id: string
      result: any
      status: 'success' | 'failed'
    }) => {
      await sendMessage({
        message_type: ToolType.MCP,
        content: JSON.stringify(messageData),
      })

      // 延迟发送退出消息
      delayedExecution(async () => {
        await sendMessage({
          message_type: ToolType.TakeOverExit,
          content: '',
        })
      })
    },
    [sendMessage],
  )

  const handleOpenLog = useCallback(() => {
    console.log('打开日志')
    setEventSnapshot(base?.event_id)
    showPanel()
    setActiveTab(Tabs.Computer)
  }, [])

  // 处理MCP工具执行
  const handleRunMcpTool = useCallback(async () => {
    if (!metadata?.function_id || !tool_call_id) {
      message.error('缺少必要的工具信息')
      return
    }

    const mcp = mcpList.find(item => item.functionId === metadata.function_id)
    if (!mcp) {
      message.error('未找到对应的MCP工具')
      setStatus('failed')
      return
    }
    if (mcp?.source === 'CLIENT' && !isClientPlatform()) {
      message.warning('请在客户端运行该工具')
      return
    }
    setStatus('running')

    const toolInput = tryParseToJsonObject(base?.tool_input || '')

    try {
      const result = await executeMcpTool(mcp, realToolName, toolInput)
      const isSuccess = result.status === 'SUCCEEDED'
      setStatus(isSuccess ? 'success' : 'failed')

      if (base?.plan_step_state === TaskStatus.PAUSE) {
        await sendMcpMessage({
          unique_id: tool_call_id,
          result: result.data,
          status: isSuccess ? 'success' : 'failed',
        })
      }
    } catch (error) {
      setStatus('failed')
      if (base?.plan_step_state === TaskStatus.PAUSE) {
        await sendMcpMessage({
          unique_id: tool_call_id,
          result: {},
          status: 'failed',
        })
      }
    }
  }, [
    metadata,
    mcpList,
    base?.tool_input,
    base?.plan_step_state,
    realToolName,
    sendMcpMessage,
  ])

  // 渲染状态图标
  const renderStatusIcon = () => {
    const iconConfig = STATUS_ICON_MAP[status]
    return <Icon icon={iconConfig.icon} className={iconConfig.className} />
  }
  // 渲染运行按钮
  const renderRunButton = () => {
    if (status !== 'ready') return null

    return (
      <Tooltip placement='top' title='开始运行'>
        <span
          className='ml-5px cursor-pointer size-22px hover:bg-white/60% rd-4px flex items-center justify-center'
          onClick={e => {
            e.stopPropagation()
            handleRunMcpTool()
          }}
        >
          <Icon icon='i-icons-run' className='size-16px c-primary' />
        </span>
      </Tooltip>
    )
  }

  // 主要内容渲染
  const renderContent = () => (
    <div className='py-4px px-6px flex items-center bg-#626999/8% rd-10px w-fit my-9px hover:bg-#626999/12% cursor-pointer'>
      {renderStatusIcon()}
      <span className='text-14px/22px c-#8D8D99 ml-5px'>
        {MCP_STATUS_MAP[status as McpStatus]}
      </span>
      <span className='text-14px/22px ml-5px'>
        {metadata?.name}&nbsp;/&nbsp;{realToolName}
      </span>
      {renderRunButton()}
    </div>
  )

  return (
    <div
      onClick={() => {
        console.log('打开日志')
        handleOpenLog()
      }}
      className={`flex-1 flex flex-col ${className}`}
    >
      {renderContent()}
    </div>
  )
}

export default McpContent
