import type { FC } from 'react'
import React, { useState, useMemo, useCallback } from 'react'
import classNames from 'classnames'
import {
  TaskStatus,
  EventType,
  EventActionType,
  ToolType,
} from '@apis/mindnote/next-agent-chat.type'
import type { Event } from '@apis/mindnote/next-agent-chat.type'
import MessageItem from '../message-item'
import type { MessageItemProps } from '../message-item'
import { Icon } from '../../../base/icon'

/**
 * 步骤类型枚举 - 定义步骤的不同展示方式
 */
export enum StepType {
  /** 标准步骤 - 带有标题、折叠功能和连接线 */
  STANDARD = 'standard',
  /** 用户输入步骤 - 简单步骤，没有标题和折叠功能 */
  USER_INPUT = 'user_input',
}

/**
 * 扩展的事件类型
 */
type ExtendedEvent = Event & { renderProps: MessageItemProps }

/**
 * 事件组件属性
 */
interface EventProps {
  event: ExtendedEvent
  index: number
  supportMarkdown?: boolean
}

/**
 * 步骤组件属性
 */
export interface StepProps {
  /** 步骤标题 */
  title: string
  /** step步骤状态 */
  stepState: TaskStatus
  /** task任务状态 */
  taskState: TaskStatus
  /** 步骤包含的事件列表 */
  events: ExtendedEvent[]
  /** 是否默认折叠 */
  isCollapsed?: boolean
  /** 步骤类型 */
  type?: StepType
  /** 最新事件 */
  latestEvent?: Event
  /** 点击事件回调（暂未使用，保留以备后续扩展） */
  onClick?: (
    type: string,
    eventId: string,
    params?: Record<string, any>,
  ) => void
}

/**
 * 状态图标配置映射
 */
const STATUS_ICON_CONFIG: Partial<
  Record<TaskStatus, { icon: string; size: string } | { isSpinner: true }>
> = {
  [TaskStatus.NOT_STARTED]: { icon: 'i-icons-step-success', size: 'size-12px' },
  [TaskStatus.COMPLETED]: { icon: 'i-icons-step-success', size: 'size-12px' },
  [TaskStatus.CANCELED]: { icon: 'i-icons-step-success', size: 'size-12px' },
  [TaskStatus.FAILED]: { icon: 'i-icons-step-success', size: 'size-12px' },
  [TaskStatus.IN_PROGRESS]: { isSpinner: true },
  [TaskStatus.PAUSE]: { isSpinner: true },
} as const

/**
 * CSS 样式常量
 */
const STYLES = {
  // 连接线样式 - 使用 CSS 定位，从状态图标中心开始
  connector: {
    width: '1px',
    borderLeft: '1px dashed #E6E6E6',
    // 从状态图标中心开始：py-12px + pt-5px + 图标高度一半(6px) = 27px
    top: '27px',
    height: 'calc(100% - 27px)',
  },
  // 进度点样式 - 独立的占位符，表示下一个输出正在处理中
  progressDot: 'ml-22px mt-10px z-1 relative size-12px rd-12px flex-center',
  // 状态图标容器样式 - 改为顶部对齐，确保在多行标题时正确对齐
  statusIconContainer:
    'flex items-start justify-center rounded-full mr-3px z-1 relative w-20px h-20px pt-5px',
  // 加载动画样式
  spinner:
    'relative size-12px z-2 bg-#fff rd-12px border-2 border-solid border-#625DFF border-t-transparent animate-spin',
  // 步骤标题容器样式
  titleContainer: 'inline-flex items-start cursor-pointer relative z-1 py-12px',
  // 事件列表容器样式
  eventsContainer: 'ml-22px',
  // 事件列表样式
  eventsList: 'flex flex-col gap-2px',
} as const

/**
 * 根据事件类型渲染对应组件
 */
const EventComponent: FC<EventProps> = ({
  event,
  index,
  supportMarkdown = false,
}) => {
  // 防御性编程：确保 renderProps 存在
  if (!event?.renderProps) {
    console.warn(`Event at index ${index} missing renderProps:`, event)
    return null
  }

  return (
    <MessageItem supportMarkdown={supportMarkdown} {...event.renderProps} />
  )
}

/**
 * 状态图标组件 - 展示步骤当前状态
 */
const StatusIcon: FC<{ state: TaskStatus }> = ({ state }) => {
  const config = STATUS_ICON_CONFIG[state]

  // 如果没有配置，返回 null
  if (!config) return null

  // 渲染加载动画
  if ('isSpinner' in config && config.isSpinner) {
    return <div className={STYLES.spinner} />
  }

  // 渲染普通图标
  if ('icon' in config) {
    return <Icon icon={config.icon} size={config.size} />
  }

  return null
}

/**
 * 步骤标题组件
 */
const StepTitle: FC<{
  title: string
  state: TaskStatus
  hasChildren: boolean
  collapsed: boolean
  onToggle: () => void
}> = ({ title, state, hasChildren, collapsed, onToggle }) => {
  return (
    <div className='relative z-1'>
      <div className={STYLES.titleContainer} onClick={onToggle}>
        <div className={STYLES.statusIconContainer}>
          <StatusIcon state={state} />
        </div>
        <div className='flex-1 text-[16px] font-500 c-font leading-[20px]'>
          {title}
        </div>
        {hasChildren && (
          <div className='text-#666 pt-2px'>
            <Icon
              icon='i-icons-arrow'
              size='size-16px'
              className={classNames('c-#8D8D99', {
                'rotate-180': collapsed,
              })}
            />
          </div>
        )}
      </div>
    </div>
  )
}

/**
 * 事件列表组件
 */
const EventsList: FC<{ events: ExtendedEvent[] }> = ({ events }) => {
  const renderEvents = useCallback(() => {
    return events.map((event, index) => (
      <EventComponent
        key={event.event_id || `event-${index}`}
        event={event}
        index={index}
      />
    ))
  }, [events])

  return <div className={STYLES.eventsList}>{renderEvents()}</div>
}

/**
 * 连接线组件 - 使用纯 CSS 定位
 */
const Connector: FC = () => (
  <div
    className='absolute left-[10px] z-0 step-connector'
    style={STYLES.connector}
  />
)

/**
 * 进度点组件 - 独立的占位符，表示正在处理下一个输出
 */
const ProgressDot: FC = () => (
  <div className={STYLES.progressDot}>
    <div className='animate-breath absolute inset-0 rd-12px bg-#625DFF/50' />
    <div className='size-6px rd-12px bg-#625DFF' />
  </div>
)

/**
 * 用户输入步骤组件 - 简化版本，只显示事件列表
 */
const UserInputStep: FC<{ events: ExtendedEvent[] }> = ({ events }) => (
  <div className='relative'>
    <EventsList events={events} />
  </div>
)

/**
 * 步骤总结组件 - 显示步骤完成时的总结信息
 */
const StepSummary: FC<{ summaryEvents: ExtendedEvent[] }> = ({
  summaryEvents,
}) => {
  if (summaryEvents.length === 0) return null

  return (
    <div className=''>
      <div className='flex flex-col gap-4px'>
        {summaryEvents.map((event, index) => (
          <div key={event.event_id || `summary-${index}`}>
            <EventComponent supportMarkdown event={event} index={index} />
          </div>
        ))}
      </div>
    </div>
  )
}

/**
 * 标准步骤组件 - 完整版本，包含标题、折叠功能等
 */
const StandardStep: FC<{
  title: string
  stepState: TaskStatus
  taskState: TaskStatus
  events: ExtendedEvent[]
  isCollapsed: boolean
  latestEvent?: Event
}> = ({ title, stepState, taskState, events, isCollapsed, latestEvent }) => {
  const [collapsed, setCollapsed] = useState(isCollapsed)

  const taskIsComplete = useMemo(() => {
    return (
      taskState === TaskStatus.COMPLETED ||
      taskState === TaskStatus.CANCELED ||
      taskState === TaskStatus.FAILED
    )
  }, [taskState])
  // 切换折叠状态
  const handleToggle = useCallback(() => {
    setCollapsed(prev => !prev)
  }, [])

  const { regularEvents, summaryEvents } = useMemo(() => {
    // 分离普通事件和总结事件
    const regularEvents: typeof events = []
    const summaryEvents: typeof events = []

    events.forEach(event => {
      if (
        event.event_type === EventType.STEP_SUMMARY ||
        event.event_type === EventType.SUMMARY
      ) {
        summaryEvents.push(event)
      } else {
        regularEvents.push(event)
      }
    })

    return { regularEvents, summaryEvents }
  }, [events])

  // 判断是否有子事件
  const hasChildren = useMemo(
    () => regularEvents.length > 0,
    [regularEvents.length],
  )

  // 是否显示连接线和进度点
  const showConnectorAndDot = hasChildren && !collapsed

  // 计算实际显示的步骤状态（与 StepTitle 保持一致）
  const displayStepState = taskIsComplete ? TaskStatus.COMPLETED : stepState

  const isAskUser =
    latestEvent?.event_type === EventType.ASK_USER ||
    latestEvent?.content?.action_type === EventActionType.AskUser

  // TODO: action_type后续需要改造
  const isMcp = (latestEvent?.content as any).action_type === ToolType.MCP

  return (
    <>
      {/* 步骤主体部分 */}
      <div className='relative'>
        {/* 步骤标题 */}
        <StepTitle
          title={title}
          state={displayStepState}
          hasChildren={hasChildren}
          collapsed={collapsed}
          onToggle={handleToggle}
        />

        {/* 事件列表 */}
        {showConnectorAndDot && (
          <div className={STYLES.eventsContainer}>
            <EventsList events={regularEvents} />
          </div>
        )}

        {/* 连接线 */}
        {showConnectorAndDot && <Connector />}

        {/* 进度点 */}
        {((!isAskUser && displayStepState === TaskStatus.IN_PROGRESS) ||
          (isMcp && displayStepState === TaskStatus.PAUSE)) &&
          showConnectorAndDot && <ProgressDot />}
      </div>

      {/* 步骤总结 - 完全独立于步骤主体，显示在虚线外面 */}

      {stepState === TaskStatus.COMPLETED && summaryEvents.length > 0 && (
        <StepSummary summaryEvents={summaryEvents} />
      )}
    </>
  )
}

/**
 * 步骤组件 - 展示任务的执行步骤
 *
 * 支持两种类型：
 * - STANDARD: 标准步骤，带有标题、折叠功能和连接线
 * - USER_INPUT: 用户输入步骤，简单展示事件列表
 */
export const Step: FC<StepProps> = ({
  title,
  stepState,
  taskState,
  events = [], // 提供默认值
  isCollapsed = false,
  type = StepType.STANDARD,
  latestEvent,
}) => {
  // 防御性编程：确保 events 是数组
  const safeEvents = Array.isArray(events) ? events : []

  // 根据步骤类型渲染不同的组件
  if (type === StepType.USER_INPUT) {
    return <UserInputStep events={safeEvents} />
  }

  return (
    <StandardStep
      title={title}
      stepState={stepState}
      taskState={taskState}
      events={safeEvents}
      isCollapsed={isCollapsed}
      latestEvent={latestEvent}
    />
  )
}

export default React.memo(Step)
