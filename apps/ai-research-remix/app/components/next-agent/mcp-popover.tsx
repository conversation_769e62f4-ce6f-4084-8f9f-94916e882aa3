import { memo, forwardRef, useImperativeHandle, useMemo } from 'react'
import { useMemoizedFn, useRequest } from 'ahooks'
import {
  getMcpEnablesBySession,
  setMcpEnablesBySession,
} from '@apis/mindnote/mcp'
import { useLoaderData } from '@remix-run/react'

import { checkClientSupport } from '../mcp/util'
import { MessageMcpConfig } from '../mcp/message-mcp'
import { useTask } from '@/store/task-event'
import { useMcpStore } from '@/store/mcp'
import { BLANK_CONVERSATION_ID } from './const'

export interface MCPConfigRef {
  triggerMcp: (targetConversationId: string) => Promise<void>
}

export const MCPConfig = memo(
  forwardRef<MCPConfigRef>((_, ref) => {
    const isClient = !!window.MindNote
    const taskId = useTask()
    const { user } = useLoaderData<any>()
    const targetConversationId = useMemo(() => {
      return taskId || `${user.userId}-${BLANK_CONVERSATION_ID}`
    }, [taskId])
    const mcpList = useMcpStore(state => state.list)
    const refreshMcpList = useMcpStore(state => state.refreshMcpList)

    const { data: mcpIds = [], refreshAsync: refreshEnabledMCPIds } =
      useRequest(
        async () => {
          const res = await getMcpEnablesBySession(
            targetConversationId,
            'sa_conversation',
            isClient ? 'CLIENT' : 'CLOUD',
          )
          return res?.enable_function ?? []
        },
        {
          ready: !!targetConversationId,
          refreshDeps: [targetConversationId],
        },
      )

    const enabledMCPIds = useMemo(() => {
      const currentMcpIdMcp: Record<string, true> = {}
      mcpList.forEach(item => (currentMcpIdMcp[item.functionId] = true))
      return mcpIds.filter(id => currentMcpIdMcp[id])
    }, [mcpIds, mcpList])

    const handleEnable = useMemoizedFn(async (id: string, enable: boolean) => {
      let newEnabledMCPIds = [...enabledMCPIds]
      if (enable) {
        newEnabledMCPIds.push(id)
      } else {
        newEnabledMCPIds = newEnabledMCPIds.filter(item => item !== id)
      }
      await setMcpEnablesBySession(
        targetConversationId!,
        'sa_conversation',
        isClient ? 'CLIENT' : 'CLOUD',
        newEnabledMCPIds,
      )
      await refreshEnabledMCPIds()
    })

    useImperativeHandle(ref, () => ({
      triggerMcp: async (conversationId: string) => {
        await refreshMcpList()
        await setMcpEnablesBySession(
          conversationId,
          'sa_conversation',
          isClient ? 'CLIENT' : 'CLOUD',
          enabledMCPIds,
        )
        await refreshEnabledMCPIds()
      },
    }))

    return (
      <div
        onClick={e => {
          e.stopPropagation()
        }}
        className='flex gap-12px mt-12px'
      >
        {(!isClient || checkClientSupport()) && (
          <MessageMcpConfig
            enabledMCPIds={enabledMCPIds}
            onEnable={handleEnable}
          />
        )}
      </div>
    )
  }),
)
