import { memo } from 'react'
import { cn } from '@bty/util'
import { Image } from 'antd'
import { Icon } from '../base/icon'
import { basePath } from '@/const'

export const McpEnvStatus = memo<{
  clientEnvStatus: 'checking' | 'failed' | 'success'
  installMcpEnvLoading: boolean
}>(props => {
  const { clientEnvStatus, installMcpEnvLoading } = props
  const isEnvError = clientEnvStatus === 'failed' && !installMcpEnvLoading

  const text =
    clientEnvStatus === 'success'
      ? '环境正常'
      : clientEnvStatus === 'checking'
        ? '环境检测中'
        : installMcpEnvLoading
          ? '环境安装中'
          : '环境异常'

  return (
    <div
      className={cn(
        'px-4px py-3px b-1 b-solid rd-4px text-12px/12px font-400 flex items-center gap-2px',
        {
          'b-#FF5219/20% bg-#FFF1EE c-#FF5219': isEnvError,
          'b-#00B078/20% bg-#EBF9F5 c-#00B078': clientEnvStatus === 'success',
        },
      )}
    >
      {isEnvError && (
        <Icon icon='i-icons-error' className='!size-12px c-#FF5219' />
      )}
      {clientEnvStatus === 'success' && !installMcpEnvLoading && (
        <Icon icon='i-icons-run-success' className='!size-12px c-#00B078' />
      )}
      {(clientEnvStatus === 'checking' || installMcpEnvLoading) && (
        <Image
          src={`${basePath}/ask-think-loading.png`}
          width={12}
          className='animate-spin'
        />
      )}
      <span>{text}</span>
    </div>
  )
})
