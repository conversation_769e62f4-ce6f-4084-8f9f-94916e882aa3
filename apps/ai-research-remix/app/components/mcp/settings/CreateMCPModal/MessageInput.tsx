import { useRef } from 'react'
import TextAreaAutoSize from 'react-textarea-autosize'

interface MessageInputProps {
  content: string
  setContent: (content: string) => void
  handleSubmit: (content: string) => void
}
const placeholderText =
  '分析桌面上的女装Excel，找出销量不好的店铺，从小红书上搜索此店铺地址的女装流行趋势，给出分析建议'

export function MessageInput(props: MessageInputProps) {
  const { content, setContent, handleSubmit } = props

  const inputRef = useRef<HTMLTextAreaElement>(null)

  const handleSend = () => {
    if (!content.trim()) return
    handleSubmit(content)
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter') {
      if (e.shiftKey) return // 允许换行
      e.preventDefault()
      handleSend()
    }
    if (e.key === 'Tab' && !content.trim()) {
      e.preventDefault()
      setContent(placeholderText)
    }
  }

  return (
    <div
      className='group relative p-[2px] box-border bg-white rounded-[12px] justify-center items-center border-[#E1E1E5] border border-solid border-opacity-60 shadow-[0px_4px_20px_0px_rgba(0,0,0,0.08)]'
      onClick={() => {
        inputRef.current?.focus()
      }}
    >
      {/* 输入框边框渐变 */}
      <div className='hidden absolute inset-0 pointer-events-none rounded-[12px] border-0 bg-gradient-to-r from-[#36CDFF] via-[#684AFF] to-[#963AFF] group-focus-within:block'></div>
      <div className=' relative z-[2] w-full min-h-50px flex gap-x-12px justify-between bg-white rounded-[10px] box-border p-[9px]'>
        <TextAreaAutoSize
          ref={inputRef}
          value={content}
          onChange={e => setContent(e.target.value)}
          onKeyDown={handleKeyDown}
          autoFocus
          wrap='soft'
          className='block h-full w-full p-[0] placeholder:align-middle resize-none border-0 bg-white outline-0 text-[#17171d] focus-visible:outline-none mt-2px'
          minRows={1}
          maxRows={5}
          style={{ fontSize: '16px', height: 32, overflow: 'hidden' }}
        />
        {!content && (
          <div className='absolute top-0 left-0 w-full h-full flex items-center gap-x-4px p-9px'>
            <span className='text-[#8d8d99]/60 text-[14px]/[22px]'>
              {placeholderText}
            </span>
            <div className='bg-[rgba(98,105,153,0.04)] flex items-center justify-center w-33px h-20px rounded-4px c-[rgba(141,141,153,0.6)] border-1 border-[rgba(225,225,229,0.6)]'>
              Tab
            </div>
          </div>
        )}
        <div className='flex items-center'>
          <div className='ml-auto'>
            <div
              className={`w-32px h-32px rounded-full flex-center bg-gradient-to-br from-[#36CDFF] via-[#684AFF] to-[#963AFF] cursor-pointer hover:op-80 ${
                !content.trim() ? '!opacity-50 cursor-not-allowed' : ''
              }`}
              tabIndex={0}
              aria-label='发送'
              onClick={e => {
                e.stopPropagation()
                handleSend()
              }}
              onKeyDown={e => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault()
                  handleSend()
                }
              }}
              role='button'
            >
              {/* 替换为你自己的Icon组件 */}
              <svg className='!size-12px' viewBox='0 0 24 24' fill='none'>
                <path d='M2 21L23 12L2 3V10L17 12L2 14V21Z' fill='white' />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
