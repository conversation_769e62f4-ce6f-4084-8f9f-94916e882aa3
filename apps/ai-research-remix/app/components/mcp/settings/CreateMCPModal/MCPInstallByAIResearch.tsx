import { useState } from 'react'
import { <PERSON><PERSON>, Checkbox, message } from 'antd'
import cn from 'classnames'
import NiceModal from '@ebay/nice-modal-react'
import { useMcpStore } from '@/store/mcp'
import { ModalContentType } from '../ai-create-modal'
import { Icon } from '@/components/base/icon'
import { McpSetUpModal } from './McpSetUpModal'
import { EmptyList } from './EmptyList'

interface McpItemProps {
  code: string
  description: string
  external_source: string
  external_source_url: string
  is_auto_executed: boolean
  is_key_required: boolean
  is_self_developed: boolean
  name: string
  plugin_type: string
  source: string
  tags: string[]
  type: string
  url: string
  config: {
    args: string[]
    command: string
    env: {
      [key: string]: string
    }
  }
}
interface McpItemDataProps {
  name: string
  description: string
  code: string
  is_key_required?: boolean
  checked: boolean
  external_source_url: string
  type: string
  url: string
  env?: string
  headers?: string
  args?: string
  config: {
    args: string[]
    command?: string
    url?: string
    env: {
      [key: string]: string
    }
  }
  setMcpMarketList: React.Dispatch<React.SetStateAction<McpItemProps[]>>
  onChange: (key: string, checked: boolean) => void
}

interface MCPInstallByAIResearchProps {
  mcpSearchData: McpItemProps[]
  handleCancel: () => void
  setContentType: (contentType: ModalContentType) => void
  setCurrentStep: (step: number) => void
}

function McpInstalled(props: McpItemDataProps) {
  const {
    name,
    description,
    is_key_required,
    checked,
    code,
    config,
    external_source_url,
    type,
    url,
    env,
    args,
    headers,
    onChange,
    setMcpMarketList,
  } = props

  const handleCheckboxChange = (e: any) => {
    onChange(code, e.target.checked)
  }
  const handleDivClick = () => {
    onChange(code, !checked)
  }
  const handleSetupClick = (e: React.MouseEvent) => {
    const mcpType = config?.command ? ('command' as const) : ('sse' as const)

    const value = {
      name,
      code,
      type: (type as 'command' | 'sse') || mcpType,
      external_source_url,
      command: config?.command,
      args: args || config?.args?.join(' '),
      url: url || config?.url,
      headers: headers || '',
      env: env || (config?.env ? Object.entries(config.env).join('\n') : ''),
    }
    e.stopPropagation()
    NiceModal.show(McpSetUpModal, {
      value,
      onFinish: async (values: any, code: string) => {
        setMcpMarketList((prev: McpItemProps[]) => {
          return prev.map(item => {
            if (item.code === code) {
              return { ...item, ...values }
            }
            return item
          })
        })
      },
    })
  }
  return (
    <div
      className={cn(
        'group flex flex-col gap-y-4px bg-[rgba(98,105,153,0.06)] rounded-8px p-12px cursor-pointer border-1 border-transparent',
        {
          '!border-#7B61FF border-1': checked,
        },
      )}
      onClick={handleDivClick}
    >
      <div className='flex justify-between items-center'>
        <div className='flex gap-x-6px'>
          <span className='c-#17171D text-14px/16px font-500'>{name}</span>
          {is_key_required && (
            <span className='c-#CC7900 text-11px/12px bg-[rgba(204,121,0,0.08)] rounded-4px px-4px py-2px'>
              需要Key
            </span>
          )}
        </div>
        <div className='flex gap-x-12px'>
          <Icon
            icon='i-icons-setting'
            className='opacity-0 group-hover:opacity-100 hover:bg-#7B61FF'
            onClick={handleSetupClick}
          />
          <Checkbox
            className='w-16px h-16px'
            checked={checked}
            onChange={handleCheckboxChange}
            onClick={e => e.stopPropagation()}
          />
        </div>
      </div>
      <p className='c-#8D8D99 text-12px/18px'>{description}</p>
    </div>
  )
}

export function MCPInstallByAIResearch(props: MCPInstallByAIResearchProps) {
  const { mcpSearchData, handleCancel, setContentType, setCurrentStep } = props
  const [mcpMarketList, setMcpMarketList] =
    useState<McpItemProps[]>(mcpSearchData)
  const [selectedKeys, setSelectedKeys] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const { batchAddMcp } = useMcpStore()
  const handleCheckboxChange = (key: string, checked: boolean) => {
    setSelectedKeys(prev => {
      if (checked) {
        return [...prev, key]
      } else {
        return prev.filter(item => item !== key)
      }
    })
  }
  const searchAgain = () => {
    setCurrentStep(1)
    setContentType(ModalContentType.LOADING)
  }
  const handleConfirm = async () => {
    if (selectedKeys.length === 0) {
      message.warning('请至少选择一个MCP')
      return
    }
    setLoading(true)
    try {
      const selectedItems =
        mcpMarketList?.filter(item => selectedKeys.includes(item?.code)) || []
      const res = await batchAddMcp(selectedItems as any)
      if (res) {
        message.success(`成功添加 ${selectedItems.length} 个MCP`)
        setSelectedKeys([])
        handleCancel()
      }
    } catch (e) {
      console.error('批量添加MCP失败:', e)
    } finally {
      setLoading(false)
    }
  }
  return (
    <div className='flex flex-col gap-y-12px h-556px overflow-y-auto items-center'>
      {mcpMarketList?.length > 0 ? (
        <>
          <div className='text-16px/24px font-500 c-#17171D mb-16px text-center'>
            请选择待安装MCP
          </div>
          <div className='flex flex-col w-720px gap-y-12px border-1 border-[rgba(225,225,229,0.6)] rounded-12px max-h-556px overflow-y-auto bg-[rgba(255,255,255,0.4)] p-12px'>
            {mcpMarketList?.map(item => (
              <McpInstalled
                key={item.code}
                {...item}
                checked={selectedKeys.includes(item?.code)}
                onChange={handleCheckboxChange}
                setMcpMarketList={setMcpMarketList}
              />
            ))}
          </div>
          <Button
            type='primary'
            className='w-104px h-36px mt-24px'
            onClick={handleConfirm}
            disabled={selectedKeys?.length === 0}
            loading={loading}
          >
            确认添加
          </Button>
        </>
      ) : (
        <EmptyList searchAgain={searchAgain} />
      )}
    </div>
  )
}
