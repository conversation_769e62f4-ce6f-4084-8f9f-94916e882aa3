import { Steps, Button, Form, message, Popconfirm } from 'antd'
import { Icon } from '@/components/base/icon'
import AILoadingIcon from '../../../../../public/ai-loading.png'
import CollecttionLoading from '../../../../../public/collection-loading.gif'
import { DynamicItem } from './DynamicForm'

interface mcpAnalyseItemData {
  function: string
  keyword: string
  keyword_en: string
  reason: string
}

interface AICreateStepBarProps {
  currentStep: number
  mcpAnalyseData: mcpAnalyseItemData[]
  handleSearchMcp: (newMcpAnalyseData: mcpAnalyseItemData[]) => void
}
export function AICreateStepBar(props: AICreateStepBarProps) {
  const { currentStep, mcpAnalyseData, handleSearchMcp } = props
  const [form] = Form.useForm()
  const initialValues = {
    newData:
      mcpAnalyseData?.map(item => ({
        keyword: item.keyword,
        function: item.function,
        keyword_en: item.keyword_en,
        reason: item.reason,
      })) || [],
  }

  const stepItems = [
    {
      title: '需求分析拆解',
      icon:
        currentStep === 0 ? (
          <img
            src={AILoadingIcon}
            alt='AILoadingIcon'
            className='w-16px h-16px animate-spin'
          />
        ) : (
          <Icon icon='i-icons-ai-selected' />
        ),
    },
    {
      title: '生成MCP任务列表',
      icon:
        currentStep >= 1 ? (
          <Icon icon='i-icons-ai-selected' />
        ) : (
          <Icon icon='i-icons-ai-unselect' />
        ),
    },
    {
      title: '搜索符合条件的MCP',
      icon:
        currentStep === 2 ? (
          <img
            src={AILoadingIcon}
            alt='AILoadingIcon'
            className='w-16px h-16px animate-spin'
          />
        ) : currentStep > 2 ? (
          <Icon icon='i-icons-ai-selected' />
        ) : (
          <Icon icon='i-icons-ai-unselect' />
        ),
    },
  ]

  const handleSubmit = (values: any) => {
    handleSearchMcp(values?.newData)
  }
  const handleRestore = () => {
    form.setFieldsValue(initialValues)
    message.success('已恢复到原始数据')
  }
  return (
    <div className='flex flex-col items-center gap-y-24px w-720px'>
      <img
        src={CollecttionLoading}
        alt='CollecttionLoading'
        className='w-100px h-100px'
      />
      <Steps
        current={currentStep}
        size='small'
        items={stepItems}
        className='
        [&_.ant-steps-item-container]:flex
        [&_.ant-steps-item-container]:items-center
        [&_.ant-steps-item-title::after]:border-t-dashed 
        [&_.ant-steps-item-title::after]:border-t-1 
        [&_.ant-steps-item-title::after]:border-gray-300 
        [&_.ant-steps-item-title::after]:!bg-transparent
         [&_.ant-steps-item-finish_.ant-steps-item-title::after]:!border-#7B61FF
    '
      />
      {currentStep === 1 && (
        <div className='flex flex-col items-center gap-y-24px'>
          <div className='w-720px bg-[rgba(98,105,153,0.06)] rounded-8px px-12px py-8px h-400px overflow-y-auto'>
            <Form
              form={form}
              layout='inline'
              initialValues={initialValues}
              onFinish={handleSubmit}
            >
              <Form.List name='newData'>
                {(fields, { add, remove }) => {
                  return (
                    <>
                      <div className='flex items-center text-12px/14px c-#8D8D99 mb-5px'>
                        <div className='w-170px py-6px'>搜索关键词</div>
                        <div className='w-482px py-6px ml-8px flex items-center justify-between'>
                          <span>功能描述</span>
                          <Popconfirm
                            icon={null}
                            title='是否恢复AI的拆解结果，覆盖现在的任务列表？'
                            onConfirm={handleRestore}
                          >
                            <div className='w-24px h-24px hover:bg-[rgba(98,105,153,0.08)] rounded-4px flex-center'>
                              <Icon
                                icon='i-icons-recovery'
                                className='cursor-pointer'
                              />
                            </div>
                          </Popconfirm>
                        </div>
                        <div className='w-24px h-24px flex hover:bg-[rgba(98,105,153,0.08)] rounded-4px items-center justify-center cursor-pointer ml-8px'>
                          <Icon
                            icon='i-icons-add'
                            onClick={() =>
                              add({
                                keyword: '',
                                function: '',
                                keyword_en: '',
                                reason: '',
                              })
                            }
                            className='c-#17171D'
                          />
                        </div>
                      </div>
                      {fields?.map((field: any) => (
                        <DynamicItem
                          key={field?.key}
                          field={field}
                          onRemove={() => {
                            if (fields.length > 1) {
                              remove(field.name)
                            } else {
                              message.warning('至少需要保留一个任务项')
                            }
                          }}
                          canRemove={fields.length > 1}
                        />
                      ))}
                    </>
                  )
                }}
              </Form.List>
            </Form>
          </div>
          <Button
            type='primary'
            className='w-104px h-36px'
            onClick={() => form.submit()}
          >
            确认继续
          </Button>
        </div>
      )}
    </div>
  )
}
