import { memo, useState } from 'react'
import NiceModal from '@ebay/nice-modal-react'
import { Button, Form, Input, message, Select } from 'antd'
import { useMemoizedFn, useRequest } from 'ahooks'
import { generateMCPCode } from '@apis/mindnote/mcp'
import { cn } from '@bty/util'
import { localize } from '@bty/localize'
import { Modal } from '@/components/base/modal'
import { Icon, IconButton } from '@/components/base/icon'
import { useGetValue } from '@/hooks/use-get-value'
import { FormLabel } from '../mcp-editor-modal'

interface MCPInputData {
  name?: string
  code: string
  type: 'command' | 'sse' | 'streamableHttp'
  command?: string
  args?: string
  env?: string
  external_source_url: string
}

interface CustomMCPModalProps {
  value?: MCPInputData
  onFinish: (values: any, code: string) => Promise<void> | void
}

const McpSetUpModalInner = memo<CustomMCPModalProps>(props => {
  const { value, onFinish } = props
  const [loading, setLoading] = useState(false)
  const [form] = Form.useForm()
  const modal = NiceModal.useModal()

  const innerCode = useGetValue(['code'], form) ?? ''
  const innerName = useGetValue(['name'], form) ?? ''

  const { runAsync: generateCode, loading: codeGenerateLoading } = useRequest(
    generateMCPCode,
    {
      manual: true,
    },
  )

  const handleCancel = useMemoizedFn(() => {
    modal.hide()
    setTimeout(() => {
      modal.remove()
    }, 300)
  })

  const handleFinish = useMemoizedFn(async () => {
    setLoading(true)
    try {
      const values = await form.validateFields()
      await onFinish(values, value?.code ?? '')
      handleCancel()
    } finally {
      setLoading(false)
    }
  })

  return (
    <Modal
      open={modal.visible}
      footer={null}
      closable={false}
      maskClosable={false}
      width={600}
      styles={{
        body: {
          padding: 0,
        },
      }}
    >
      <div className='flex flex-col'>
        {/* 头部 */}
        <div className='flex items-center justify-between px-16px py-12px b-b-1px b-#E1E1E5/60%'>
          <div className='text-16px/24px font-500'>设置MCP</div>
          <div className='flex items-center gap-x-12px'>
            <div
              className='c-#7B61FF text-14px/16px font-500 cursor-pointer hover:c-#7B61FF/80%'
              onClick={() => {
                window.open(value?.external_source_url, '_blank')
              }}
            >
              查看来源
            </div>
            <IconButton
              icon='i-icons-close'
              size='size-24px'
              iconSize='size-16px'
              className='rd-4px!'
              onClick={handleCancel}
            />
          </div>
        </div>

        {/* 表单内容 */}
        <div className='max-h-70vh of-auto py-24px px-32px'>
          <Form
            form={form}
            initialValues={value}
            layout='vertical'
            requiredMark={false}
          >
            {/* 名称 */}
            <Form.Item
              label={<FormLabel label='名称' required />}
              name='name'
              rules={[
                {
                  required: true,
                  message: '请输入名称',
                },
              ]}
            >
              <Input
                variant='filled'
                className='w-full'
                placeholder='请输入名称'
                maxLength={50}
                suffix={
                  <span className='text-12px'>
                    <span className='text-font-fade'>{innerName.length}</span>
                    <span className='text-font-fade/60'> / 50</span>
                  </span>
                }
                autoCorrect='off'
                autoComplete='off'
                autoCapitalize='off'
                spellCheck={false}
              />
            </Form.Item>

            {/* ID */}
            <Form.Item
              label={
                <div className='font-500 flex items-center justify-between w-full'>
                  <div className='flex items-center'>
                    <span>ID</span>
                    <span className='text-error ml-3px relative top-2px'>
                      *
                    </span>
                  </div>
                  <div
                    className={cn(
                      'flex items-center gap-4px cursor-pointer c-#7B61FF text-14px/16px font-500',
                      {
                        'opacity-50 cursor-not-allowed pointer-events-none':
                          codeGenerateLoading,
                      },
                    )}
                    onClick={() => {
                      const name = form.getFieldValue('name')
                      if (!name) {
                        message.warning('请先输入名称')
                        return
                      }
                      generateCode(name, 'CLIENT').then(res => {
                        form.setFieldValue('code', res.code)
                      })
                    }}
                  >
                    <Icon
                      icon='i-icons-auto-tag'
                      className={cn('size-16px c-#7B61FF', {
                        'animate-spin': codeGenerateLoading,
                      })}
                    />
                    <span>自动生成</span>
                  </div>
                </div>
              }
              name='code'
              className='mt-8px [&_label]:w-full [&_label]:after:hidden!'
              rules={[
                {
                  required: true,
                  message: '请输入ID',
                },
                {
                  pattern: /^[a-zA-Z_$][0-9a-zA-Z_$]*$/,
                  message: '仅支持字母、数字、下划线',
                },
                {
                  validator: (_, value) => {
                    if (value && value.includes('__')) {
                      return Promise.reject('不允许出现连续的下划线')
                    }
                    return Promise.resolve()
                  },
                },
              ]}
            >
              <Input
                variant='filled'
                maxLength={30}
                suffix={
                  <span className='text-12px'>
                    <span className='text-font-fade'>{innerCode.length}</span>
                    <span className='text-font-fade/60'> / 30</span>
                  </span>
                }
                placeholder='输入ID，作为唯一标识，仅支持字母、数字、下划线'
                autoCorrect='off'
                autoComplete='off'
                autoCapitalize='off'
                spellCheck={false}
              />
            </Form.Item>
            <Form.Item
              name='type'
              label={
                <div className='flex items-center'>
                  <span className='font-500'>
                    {localize('mcp.form.type', '类型')}
                  </span>
                </div>
              }
            >
              <Select
                className='w-full [&.ant-select-disabled_.ant-select-selector]:b-none!'
                variant='filled'
                options={[
                  {
                    label: localize(
                      'mcp.form.command.label',
                      '标准输入/输出(Stdio)',
                    ),
                    value: 'command',
                  },
                  {
                    label: localize(
                      'mcp.form.sse.label',
                      '服务器发送事件(SSE)',
                    ),
                    value: 'sse',
                  },
                  {
                    label: localize(
                      'mcp.form.streamableHttp.label',
                      '可流式传输的Http(StreamableHttp)',
                    ),
                    value: 'streamableHttp',
                  },
                ]}
                placeholder={localize(
                  'mcp.form.type.placeholder',
                  '请选择类型',
                )}
              />
            </Form.Item>
            <Form.Item noStyle dependencies={['type']}>
              {({ getFieldValue }) => {
                const type = getFieldValue('type')

                if (type === 'command') {
                  return (
                    <>
                      <Form.Item label={<FormLabel label='Command' required />}>
                        <div className='flex items-center gap-12px'>
                          <Form.Item name='command' noStyle>
                            <Select
                              className='w-80px!'
                              variant='filled'
                              options={[
                                { label: 'npx', value: 'npx' },
                                { label: 'uv', value: 'uv' },
                                { label: 'uvx', value: 'uvx' },
                              ]}
                            />
                          </Form.Item>
                          <Form.Item
                            name='args'
                            rules={[
                              {
                                required: true,
                                message: localize(
                                  'mcp.form.args.required',
                                  '请输入Command指令',
                                ),
                              },
                            ]}
                            noStyle
                          >
                            <Input
                              variant='filled'
                              className='w-full'
                              placeholder={localize(
                                'mcp.form.args.placeholder',
                                '输入Command指令',
                              )}
                              autoCorrect='off'
                              autoComplete='off'
                              autoCapitalize='off'
                              spellCheck={false}
                            />
                          </Form.Item>
                        </div>
                      </Form.Item>
                      <Form.Item
                        label={
                          <FormLabel
                            label={localize('mcp.form.env.label', '环境变量')}
                          />
                        }
                        name='env'
                      >
                        <Input.TextArea
                          rows={3}
                          variant='filled'
                          placeholder='KEY=value&#13;KEY2=value2'
                          autoCorrect='off'
                          autoComplete='off'
                          autoCapitalize='off'
                          spellCheck={false}
                        />
                      </Form.Item>
                    </>
                  )
                }

                if (type === 'sse' || type === 'streamableHttp') {
                  return (
                    <>
                      <Form.Item
                        name='url'
                        required
                        label={<FormLabel label='URL' required />}
                        rules={[
                          {
                            required: true,
                            message: localize(
                              'mcp.form.url.required',
                              '请输入 URL',
                            ),
                          },
                          {
                            type: 'url',
                            message: localize(
                              'mcp.form.url.invalid',
                              '请输入正确的URL',
                            ),
                          },
                        ]}
                      >
                        <Input
                          variant='filled'
                          placeholder={localize(
                            'mcp.form.url.placeholder',
                            '输入 URL',
                          )}
                          autoCorrect='off'
                          autoComplete='off'
                          autoCapitalize='off'
                          spellCheck={false}
                        />
                      </Form.Item>
                      <Form.Item
                        label={
                          <FormLabel
                            label={localize('mcp.form.headers.label', '请求头')}
                          />
                        }
                        name='headers'
                      >
                        <Input.TextArea
                          rows={3}
                          variant='filled'
                          placeholder='KEY=value&#13;KEY2=value2'
                          autoCorrect='off'
                          autoComplete='off'
                          autoCapitalize='off'
                          spellCheck={false}
                        />
                      </Form.Item>
                    </>
                  )
                }
              }}
            </Form.Item>

            {/* 按钮区域 */}
            <div className='flex items-center justify-end gap-12px mt-24px'>
              <Button onClick={handleCancel}>取消</Button>
              <Button type='primary' loading={loading} onClick={handleFinish}>
                {loading ? '正在保存' : '完成'}
              </Button>
            </div>
          </Form>
        </div>
      </div>
    </Modal>
  )
})

// 导出弹框
export const McpSetUpModal = NiceModal.create(McpSetUpModalInner)
