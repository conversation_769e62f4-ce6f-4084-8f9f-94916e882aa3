import { Button } from 'antd'
import mcpEmpty from '../../../../../public/mcp-list-empty.png'

interface EmptyListType {
  searchAgain: () => void
}
export function EmptyList(props: EmptyListType) {
  const { searchAgain } = props
  return (
    <div className='flex flex-col items-center justify-center h-full'>
      <img src={mcpEmpty} alt='mcpEmpty' className='w-140px h-140px' />
      <span className='text-14px/16px c-[rgba(141,141,153,0.8)]'>
        未找到符合条件的MCP，返回上一步重新搜索
      </span>
      <Button type='default' className='mt-24px' onClick={searchAgain}>
        返回上一步
      </Button>
    </div>
  )
}
