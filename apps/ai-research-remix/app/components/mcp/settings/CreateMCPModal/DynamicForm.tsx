import { Input, Form } from 'antd'
import { Icon } from '@/components/base/icon'

interface DynamicItemProps {
  field: any
  onRemove?: () => void
  canRemove?: boolean
}

export function DynamicItem(props: DynamicItemProps) {
  const { field, onRemove, canRemove } = props
  return (
    <div className='flex gap-x-8px items-center mb-8px'>
      <Form.Item {...field} name={[field.name, 'keyword']} className='!me-0 '>
        <Input className='w-170px h-36px' />
      </Form.Item>
      <Form.Item {...field} name={[field.name, 'function']} className='!me-0 '>
        <Input className='w-482px h-36px' />
      </Form.Item>
      {canRemove && (
        <div className='w-24px h-24px hover:bg-[rgba(255,82,25,0.08)] rounded-4px flex items-center justify-center cursor-pointer hover:c-#FF5219'>
          <Icon icon='i-icons-delete' onClick={onRemove} />
        </div>
      )}
    </div>
  )
}
