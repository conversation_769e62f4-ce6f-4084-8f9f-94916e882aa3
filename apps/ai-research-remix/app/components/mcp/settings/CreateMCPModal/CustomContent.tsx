import { useMemo } from 'react'
import { Icon } from '@/components/base/icon'

interface CustomContentProps {
  apiKeyLink: string | undefined
  mcpServerLink: string | undefined
  selectedValue: string
}

interface StepItem {
  title: string
  skipText: string
  icon: string
  label: string
  url: string | undefined
}

export function CustomContent(props: CustomContentProps) {
  const { apiKeyLink, mcpServerLink, selectedValue } = props
  const handleSkip = (url: string | undefined) => {
    if (url) {
      window.open(url, '_blank')
    }
  }
  const stepList = useMemo((): StepItem[] => {
    const stepConfigs = {
      BatterYeah: [
        {
          title: '1. 选择空间，查看「MCP」',
          skipText: '前往查看',
          icon: 'i-icons-open-new-label',
          label: '打开平台，选择空间以查看、创建MCP服务',
          url: mcpServerLink,
        },
        {
          title: '2. 获取「工作空间ID」和「API Key」',
          skipText: '前往获取',
          icon: 'i-icons-open-new-label',
          label: '打开设置，获取相应的「工作空间ID」和API Key',
          url: apiKeyLink,
        },
      ],
      ModelScope: [
        {
          title: '1. 查看MCP',
          skipText: '前往查看',
          icon: 'i-icons-open-new-label',
          label: '打开平台，查看并部署MCP服务',
          url: mcpServerLink,
        },
        {
          title: '2. 获取API Key',
          skipText: '前往获取',
          icon: 'i-icons-open-new-label',
          label: '打开账号设置，获取个人API Key',
          url: apiKeyLink,
        },
      ],
    }

    return (
      stepConfigs[selectedValue as keyof typeof stepConfigs] ||
      stepConfigs.BatterYeah
    )
  }, [selectedValue, mcpServerLink, apiKeyLink])

  const renderStepItem = (item: StepItem) => (
    <div key={item.title}>
      <div className='flex justify-between items-center'>
        <span className='text-14px/24px c-#17171D '>{item.title}</span>
        <div
          className='flex items-center gap-x-4px cursor-pointer'
          onClick={() => handleSkip(item.url)}
        >
          <span className='text-14px/14px font-500 c-#7B61FF'>
            {item.skipText}
          </span>
          <Icon icon={item.icon} className='c-#7B61FF text-16px' />
        </div>
      </div>
      <div className='text-14px/22px c-#8D8D99 mt-4px'>{item.label}</div>
    </div>
  )

  return (
    <div className='border-1 border-[rgba(225,225,229,0.6)] p-12px rounded-8px flex flex-col gap-y-16px'>
      {stepList.map(renderStepItem)}
    </div>
  )
}
