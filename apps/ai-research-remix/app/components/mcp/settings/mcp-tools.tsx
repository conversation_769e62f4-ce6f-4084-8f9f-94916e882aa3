import { memo, useEffect, useState, useMemo } from 'react'
import cn from 'classnames'
import { message, Tooltip } from 'antd'
import copy from 'copy-to-clipboard'
import type { McpListItem } from '@/store/mcp'
import { IconButton } from '@/components/base/icon'

interface McpToolsProps {
  mcp: McpListItem
}

export const McpTools = memo<McpToolsProps>(props => {
  const { mcp } = props
  const [currentToolId, setCurrentToolId] = useState<string | null>(
    mcp.tools[0]?.name ?? null,
  )

  // 通过 useMemo 计算当前选中的工具，确保数据是最新的
  const currentTool = useMemo(() => {
    return mcp.tools.find(tool => tool.name === currentToolId) ?? null
  }, [mcp.tools, currentToolId])

  // 当工具列表变化时，确保有选中的工具
  useEffect(() => {
    if (mcp.tools.length) {
      // 如果当前选中的工具不存在，或者没有选中任何工具，则选中第一个
      if (
        !currentToolId ||
        !mcp.tools.find(tool => tool.name === currentToolId)
      ) {
        setCurrentToolId(mcp.tools[0].name)
      }
    } else {
      setCurrentToolId(null)
    }
  }, [mcp.tools, currentToolId])

  // 当 mcp.functionId 变化时，重置选中状态
  useEffect(() => {
    setCurrentToolId(mcp.tools[0]?.name ?? null)
  }, [mcp.functionId])

  return (
    <div className='size-full flex of-hidden'>
      <div className='flex-1 of-y-auto mt-16px pb-24px px-24px flex gap-12px flex-col'>
        {mcp.tools.map(tool => {
          return (
            <Tooltip key={tool.name} title={tool.description} placement='right'>
              <div
                className={cn(
                  'flex flex-col p-12px rd-8px b-1 b-#E1E1E5/80% hover:bg-#626999/8% cursor-pointer',
                  {
                    'bg-#7B61FF/8% b-#7B61FF': tool.name === currentToolId,
                  },
                )}
                onClick={() => setCurrentToolId(tool.name)}
              >
                <div className='text-16px/24px font-500'>{tool.name}</div>
                <div className='text-14px/20px font-400 c-#8D8D99 mt-4px line-clamp-2'>
                  {tool.description}
                </div>
              </div>
            </Tooltip>
          )
        })}
      </div>
      <div className='pt-16px pr-24px pb-24px shrink-0 of-hidden box-border h-full of-y-auto w-1/2 max-w-688px'>
        <div className='rd-12px b-1 b-#E1E1E5/80% of-y-auto h-full flex flex-col py-12px px-24px'>
          <div className='shrink-0'>
            <span className='text-16px/24px font-500'>{currentTool?.name}</span>
          </div>
          <div className='shrink-0 mt-12px'>
            <div className='text-14px/24px font-500'>工具描述</div>
            <div className='mt-8px text-14px/24px font-400 c-#8D8D99'>
              {currentTool?.description ?? '无描述'}
            </div>
          </div>
          <div className='mt-24px'>
            <div className='text-14px/24px font-500'>工具参数</div>
            <div className='bg-#626999/8% rd-8px font-400 text-14px/22px mt-12px'>
              {Object.keys(currentTool?.inputSchema ?? {}).length ? (
                <pre className='px-12px py-8px relative'>
                  <Tooltip title='复制'>
                    <IconButton
                      icon='i-icons-copy'
                      className='absolute top-8px right-12px rd-4px! size-24px!'
                      iconSize='size-16px!'
                      onClick={() => {
                        copy(JSON.stringify(currentTool?.inputSchema, null, 2))
                        message.success('复制成功')
                      }}
                    />
                  </Tooltip>
                  <code>
                    {JSON.stringify(currentTool?.inputSchema, null, 2)}
                  </code>
                </pre>
              ) : (
                <div className='px-12px py-8px'>无参数</div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
})
