import { memo, useState } from 'react'
import type { McpConfig } from '@bty/global-types/mcp'
import { <PERSON><PERSON>, Toolt<PERSON>, Button } from 'antd'
import { localize } from '@bty/localize'
import type { useExampleList } from '../hooks/useExampleList'
import { Icon, IconButton } from '@/components/base/icon'

interface ExamplePopoverProps {
  onCreateWithExample: (exampleMcp: McpConfig) => void
  list: ReturnType<typeof useExampleList>['exampleList']
}

export const ExamplePopover = memo<ExamplePopoverProps>(props => {
  const { list, onCreateWithExample } = props
  const [open, setOpen] = useState(false)

  return (
    <Popover
      open={open}
      onOpenChange={setOpen}
      trigger='click'
      arrow={false}
      placement='bottomLeft'
      overlayInnerStyle={{
        padding: 0,
      }}
      content={
        <div className='flex flex-col gap-4px w-600px'>
          <div className='flex items-center justify-between px-12px pt-12px'>
            <span className='text-14px/14px'>
              {localize('tips', '选择一个示例快速开始')}
            </span>
            <Icon
              icon='i-icons-close'
              className='size-14px! c-#626999/60% cursor-pointer'
              onClick={() => setOpen(false)}
            />
          </div>
          <div className='flex flex-wrap gap-12px mt-12px max-h-280px of-y-scroll px-12px pb-12px'>
            {list.map(exampleMcp => {
              return (
                <Tooltip
                  key={exampleMcp.name}
                  title={exampleMcp.description}
                  placement='left'
                >
                  <div
                    className='group w-282px shrink-0 p-16px b-1 hover:b-primary b-transparent rd-8px cursor-pointer relative overflow-hidden'
                    style={{
                      background:
                        'linear-gradient(106deg, #F8F7FF 0%, #F3EFFF 100%)',
                    }}
                    onClick={() => {
                      onCreateWithExample(exampleMcp)
                      setOpen(false)
                    }}
                  >
                    <div className='flex items-center mb-8px'>
                      <span className='text-14px/16px font-500 c-#17171D'>
                        {exampleMcp.name}
                      </span>
                      {exampleMcp.need_key && (
                        <span className='ml-6px rd-4px bg-#CC7900/8% px-4px py-2px text-12px/12px font-400 c-#CC7900'>
                          {localize('mcp.need_key', '需要Key')}
                        </span>
                      )}
                    </div>
                    <div className='text-12px/18px c-#8D8D99 line-clamp-2 group-hover:invisible'>
                      {exampleMcp.description}
                    </div>
                    <Button
                      type='primary'
                      className='shrink-0 invisible w-248px group-hover:visible of-hidden absolute bottom-12px'
                      icon={<Icon icon='i-icons-add' />}
                      onClick={e => {
                        e.stopPropagation()
                        onCreateWithExample(exampleMcp)
                        setOpen(false)
                      }}
                    >
                      {localize('mcp.create_now', '立即创建')}
                    </Button>
                  </div>
                </Tooltip>
              )
            })}
          </div>
        </div>
      }
    >
      <Tooltip title={localize('mcp.example.create.tooltip', '示例创建')}>
        <IconButton
          icon='i-icons-example'
          size='size-24px'
          iconSize='size-16px'
          className='rd-4px! mr-8px'
        />
      </Tooltip>
    </Popover>
  )
})
