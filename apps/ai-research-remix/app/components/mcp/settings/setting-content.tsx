import { memo, useEffect, useMemo, useState } from 'react'
import type { McpConfig } from '@bty/global-types/mcp'
import { localize } from '@bty/localize'
import { Button, InputNumber, message, Switch, Tooltip } from 'antd'
import cn from 'classnames'
import { useMemoizedFn } from 'ahooks'
import { McpEnvReady } from '../mcp-env-ready'
import type { useExampleList } from '../hooks/useExampleList'
import type { McpListItem } from '@/store/mcp'
import { useMcpStore } from '@/store/mcp'
import { Icon } from '@/components/base/icon'
import { NoMcpWithClient } from './no-mcp-with-client'
import { McpIntroduction } from './mcp-introduction'
import { McpTools } from './mcp-tools'

interface SettingContentProps {
  noMcp: boolean
  currentEditorMcpId: string
  clientEnvStatus: 'checking' | 'failed' | 'success'
  installMcpEnvLoading: boolean
  exampleList: ReturnType<typeof useExampleList>['exampleList']
  onInstallMcpEnv: () => void
  onAddExampleMcp: (exampleMcp: McpConfig) => void
  onCreateMcp: () => void
  onEditMcp: (functionId: string, mcp: McpListItem) => void
}

export const SettingContent = memo<SettingContentProps>(props => {
  const isClient = !!window.MindNote
  const {
    noMcp,
    clientEnvStatus,
    installMcpEnvLoading,
    exampleList,
    currentEditorMcpId,
    onInstallMcpEnv,
    onAddExampleMcp,
    onCreateMcp,
    onEditMcp,
  } = props

  const [currentTab, setCurrentTab] = useState<'introduction' | 'tools'>(
    'introduction',
  )
  const [timeout, setTimeout] = useState<number | null>(null)

  const mcpList = useMcpStore(state => state.list)
  const connectMcp = useMcpStore(state => state.connectMcp)
  const updateMcp = useMcpStore(state => state.updateMcp)

  const currentMCP = useMemo(() => {
    return mcpList.find(item => item.functionId === currentEditorMcpId)
  }, [mcpList, currentEditorMcpId])

  useEffect(() => {
    setCurrentTab('introduction')
  }, [currentEditorMcpId])

  const handleMCPConfigUpdate = useMemoizedFn(
    async (key: keyof McpConfig, value: any) => {
      await updateMcp(currentEditorMcpId, {
        [key]: value,
      })
      message.success(localize('mcp.update_mcp_success', '更新成功'))
    },
  )

  if (isClient && (clientEnvStatus !== 'success' || installMcpEnvLoading)) {
    return (
      <div className='size-full flex items-center justify-center ml--288px'>
        <McpEnvReady
          clientEnvStatus={clientEnvStatus}
          installMcpEnvLoading={installMcpEnvLoading}
          onInstallMcpEnv={onInstallMcpEnv}
        />
      </div>
    )
  }

  if (noMcp) {
    if (isClient) {
      return (
        <NoMcpWithClient
          list={exampleList}
          onCreateMcp={onCreateMcp}
          onCreateWithExample={onAddExampleMcp}
        />
      )
    } else {
      return (
        <div className='size-full flex items-center justify-center'>
          <div className='flex flex-col items-center'>
            <img src='/list-empty.png' className='size-140px' />
            <div className='text-14px/16px font-400 c-#8D8D99'>
              {localize('mcp.no_mcp', '暂无MCP服务')}
            </div>
            <Button className='mt-24px' type='primary' onClick={onCreateMcp}>
              立即创建
            </Button>
          </div>
        </div>
      )
    }
  }

  if (!currentMCP) {
    return (
      <div className='size-full flex items-center justify-center'>
        <div className='flex items-center flex-col'>
          <img src='/list-empty.png' className='size-140px' />
          <div className='text-14px/16px font-400 mt-16px c-#8D8D99'>
            不存在的MCP，请在左侧选择一个MCP查看
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className='size-full of-hidden flex flex-col'>
      <div className='shrink-0 pt-16px px-24px flex items-center justify-between'>
        <div className='flex items-center'>
          <Tooltip title={currentMCP.name}>
            <span className='text-20px/24px font-500 truncate w-full flex-1'>
              {currentMCP.name}
            </span>
          </Tooltip>
          {currentMCP?.external_source_url && (
            <Tooltip title='前往查看MCP源详情'>
              <a
                href={currentMCP.external_source_url}
                target='_blank'
                rel='noreferrer'
                className='ml-8px'
              >
                <Icon icon='i-icons-open-link' />
              </a>
            </Tooltip>
          )}
          <div className='flex items-center ml-24px shrink-0'>
            <div
              className={cn(
                'py-10px px-20px text-16px/16px font-500 rd-8px hover:c-#7B61FF cursor-pointer',
                {
                  'c-#7B61FF bg-#7B61FF/8%': currentTab === 'introduction',
                },
              )}
              onClick={() => setCurrentTab('introduction')}
            >
              <span>介绍</span>
            </div>
            <Tooltip
              title={
                currentMCP.state !== 'success'
                  ? 'MCP未连接'
                  : !currentMCP.tools.length
                    ? '未检测到该MCP的工具'
                    : ''
              }
            >
              <div
                className={cn(
                  'py-10px px-20px text-16px/16px font-500 rd-8px hover:c-#7B61FF cursor-pointer group',
                  {
                    'c-#7B61FF bg-#7B61FF/8%': currentTab === 'tools',
                    'cursor-not-allowed c-#8D8D99 hover:bg-transparent!':
                      currentMCP.state !== 'success' ||
                      !currentMCP.tools.length,
                  },
                )}
                onClick={() => {
                  if (
                    currentMCP.state !== 'success' ||
                    !currentMCP.tools.length
                  ) {
                    return
                  }
                  setCurrentTab('tools')
                }}
              >
                <span>工具</span>
                {!!currentMCP.tools?.length && (
                  <span
                    className={cn(
                      'ml-2px font-400 c-#8D8D99 group-hover:c-#7B61FF',
                      {
                        'c-#7B61FF!': currentTab === 'tools',
                      },
                    )}
                  >
                    ({currentMCP.tools.length})
                  </span>
                )}
              </div>
            </Tooltip>
          </div>
        </div>
        <div className='flex items-center gap-24px min-w-430px'>
          <div className='flex items-center'>
            <span className='text-14px/14px font-400'>自动执行</span>
            <Tooltip title='打开后，无需手动确认，AI自动执行此MCP'>
              <Icon
                icon='i-icons-question'
                className='size-12px c-#8d8d99/40% ml-5px'
              />
            </Tooltip>
            <Switch
              size='small'
              className='ml-8px'
              defaultChecked={currentMCP.auto_execute}
              onChange={value => handleMCPConfigUpdate('auto_execute', value)}
            />
          </div>
          <div className='flex items-center'>
            <span className='text-14px/14px font-400'>超时时间</span>
            <Tooltip title='超过时长后，工具停止执行，范围1-500秒'>
              <Icon
                icon='i-icons-question'
                className='size-12px c-#8d8d99/40% ml-5px'
              />
            </Tooltip>
            <InputNumber
              variant='filled'
              className='ml-8px w-50px'
              min={1}
              max={500}
              controls={false}
              precision={0}
              defaultValue={currentMCP.timeout || 30}
              onChange={value => setTimeout(value)}
              onPressEnter={() => {
                if (timeout && timeout !== currentMCP.timeout) {
                  handleMCPConfigUpdate('timeout', timeout)
                }
              }}
              onBlur={() => {
                if (timeout && timeout !== currentMCP.timeout) {
                  handleMCPConfigUpdate('timeout', timeout)
                }
              }}
            />
            <span className='text-14px/14px font-400 ml-8px'>秒</span>
          </div>
          <div>
            <Button
              disabled={currentMCP.state === 'connecting'}
              icon={
                <Icon
                  icon='i-icons-retry'
                  className={cn({
                    'animate-spin': currentMCP.state === 'connecting',
                  })}
                />
              }
              onClick={() => connectMcp(currentMCP.functionId)}
            >
              重新连接
            </Button>
          </div>
        </div>
      </div>
      <div className='relative size-full of-hidden'>
        <div
          className={cn(
            'flex size-full of-hidden transition-transform duration-150 ease-in-out w-200%',
            currentTab === 'introduction'
              ? 'translate-x-0'
              : 'translate-x--50%',
          )}
        >
          <div className='h-full w-1/2 of-hidden shrink-0'>
            <McpIntroduction
              mcp={currentMCP}
              exampleList={exampleList}
              onAddExampleMcp={onAddExampleMcp}
              onEditMcp={onEditMcp}
            />
          </div>
          <div className='h-full w-1/2 of-hidden shrink-0'>
            <McpTools mcp={currentMCP} />
          </div>
        </div>
      </div>
    </div>
  )
})
