import { memo, useState, useMemo } from 'react'
import { localize } from '@bty/localize'
import type { McpConfig } from '@bty/global-types/mcp'
import { But<PERSON>, Tooltip } from 'antd'
import type { useExampleList } from '../hooks/useExampleList'
import { Icon } from '@/components/base/icon'

export interface NoMcpWithClientProps {
  onCreateWithExample: (exampleMcp: McpConfig) => void
  onCreateMcp: () => void
  list: ReturnType<typeof useExampleList>['exampleList']
}

export const NoMcpWithClient = memo<NoMcpWithClientProps>(props => {
  const { onCreateMcp, onCreateWithExample, list } = props

  // 分页状态
  const [currentPage, setCurrentPage] = useState(0)
  const itemsPerPage = 6 // 每页显示6个项目

  // 计算分页数据
  const { totalPages, currentItems, canGoPrev, canGoNext } = useMemo(() => {
    const total = Math.ceil(list.length / itemsPerPage)
    const start = currentPage * itemsPerPage
    const end = start + itemsPerPage
    const items = list.slice(start, end)

    return {
      totalPages: total,
      currentItems: items,
      canGoPrev: currentPage > 0,
      canGoNext: currentPage < total - 1,
    }
  }, [list, currentPage, itemsPerPage])

  // 翻页函数
  const goToPrevPage = () => {
    if (canGoPrev) {
      setCurrentPage(prev => prev - 1)
    }
  }

  const goToNextPage = () => {
    if (canGoNext) {
      setCurrentPage(prev => prev + 1)
    }
  }

  return (
    <div className='size-full flex items-center justify-center'>
      <div className='flex flex-col items-center'>
        <img src='/list-empty.png' className='size-140px' />
        <div className='text-14px/16px font-400 c-#8D8D99 mt-16px'>
          {localize('mcp.no_mcp', '暂无MCP服务')}
        </div>
        <div className='mt-40px flex items-center'>
          <span
            className='c-#7B61FF font-500 text-14px/16px cursor-pointer'
            role='button'
            onClick={onCreateMcp}
          >
            {localize('mcp.create_mcp', '创建MCP服务')}
          </span>
          <span className='mx-2px'>{localize('mcp.create_mcp_or', '或')}</span>
          <span>
            {localize('mcp.create_mcp_from_example', '选择一个示例快速开始')}
          </span>
        </div>

        {/* 分页容器 */}
        <div className='relative w-fit max-w-576px mt-16px'>
          {/* 左箭头 - 只在非第一页且hover时显示 */}
          {totalPages > 1 && canGoPrev && (
            <div
              role='button'
              className='absolute z-99 left--68px top-1/2 transform -translate-y-1/2 hover:c-#7B61FF size-44px b-1 b-#E1E1E5/80% rounded-full flex items-center justify-center cursor-pointer'
              onClick={goToPrevPage}
            >
              <Icon
                icon='i-icons-scroll-to-bottom'
                className='size-16px! rotate-90deg'
              />
            </div>
          )}

          {/* 右箭头 - 只在非最后一页且hover时显示 */}
          {totalPages > 1 && canGoNext && (
            <div
              role='button'
              className='absolute z-99 right--68px top-1/2 transform -translate-y-1/2 hover:c-#7B61FF size-44px b-1 b-#E1E1E5/80% rounded-full flex items-center justify-center cursor-pointer'
              onClick={goToNextPage}
            >
              <Icon
                icon='i-icons-scroll-to-bottom'
                className='size-16px! rotate--90deg'
              />
            </div>
          )}

          {/* 卡片容器 */}
          <div className='flex-wrap gap-12px flex w-fit'>
            {currentItems.map(exampleMcp => (
              <Tooltip
                key={exampleMcp.name}
                title={exampleMcp.description}
                placement='left'
              >
                <div
                  className='group w-282px shrink-0 p-16px b-1 hover:b-primary b-transparent rd-8px cursor-pointer relative overflow-hidden'
                  style={{
                    background:
                      'linear-gradient(106deg, #F8F7FF 0%, #F3EFFF 100%)',
                  }}
                  onClick={() => onCreateWithExample(exampleMcp)}
                >
                  <div className='flex items-center mb-8px'>
                    <span className='text-14px/16px font-500 c-#17171D'>
                      {exampleMcp.name}
                    </span>
                    {exampleMcp.need_key && (
                      <span className='ml-6px rd-4px bg-#CC7900/8% px-4px py-2px text-12px/12px font-400 c-#CC7900'>
                        {localize('mcp.need_key', '需要Key')}
                      </span>
                    )}
                    {exampleMcp.is_self_developed && (
                      <span className='ml-6px rd-4px bg-[rgba(47,117,255,0.08)] px-4px py-2px text-12px/12px font-400 c-#2F75FF'>
                        {localize('mcp.self_developed', '自研')}
                      </span>
                    )}
                  </div>
                  <div className='text-12px/18px c-#8D8D99 line-clamp-2 group-hover:invisible'>
                    {exampleMcp.description}
                  </div>
                  <div
                    className='absolute bottom-0 left-0 w-full h-60px flex items-center justify-center translate-y-full group-hover:translate-y--4px transition-transform duration-200'
                    style={{
                      background:
                        'linear-gradient(0deg, #FFF 0%, rgba(255, 255, 255, 0.8) 70%, rgba(255, 255, 255, 0) 100%)',
                    }}
                  >
                    <Button
                      type='primary'
                      className='w-95% absolute bottom-4px'
                      icon={<Icon icon='i-icons-add' />}
                      role='none'
                      onClick={e => {
                        e.stopPropagation()
                        onCreateWithExample(exampleMcp)
                      }}
                    >
                      {localize('mcp.create_now', '立即创建')}
                    </Button>
                  </div>
                </div>
              </Tooltip>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
})
