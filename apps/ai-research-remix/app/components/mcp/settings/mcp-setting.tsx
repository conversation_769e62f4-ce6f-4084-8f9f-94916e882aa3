import { useEffect, useState } from 'react'
import { useSearchParams } from '@remix-run/react'
import { useMemoizedFn } from 'ahooks'
import type { McpConfig } from '@bty/global-types/mcp'
import { localize } from '@bty/localize'
import NiceModal from '@ebay/nice-modal-react'
import { getMCPSyncConfig } from '@apis/mindnote/mcp'
import type { McpListItem } from '@/store/mcp'
import { useMcpStore } from '@/store/mcp'
import { useEnvInstall } from '../hooks/useEnvInstall'
import { useExampleList } from '../hooks/useExampleList'
import { MCPEditorModal } from './mcp-editor-modal'
import { MCPCustomModal } from './mcp-custom-modal'
import { SettingSide } from './setting-side'
import { SettingContent } from './setting-content'
import { MCPAICreateModal } from './ai-create-modal'

export function MCPSetting() {
  const isClient = !!window.MindNote
  const [searchParams] = useSearchParams()
  const id = searchParams.get('id')
  const [currentEditorMcpId, setCurrentEditorMcpId] = useState<string>(id ?? '')
  const mcpList = useMcpStore(state => state.list)
  const addMcp = useMcpStore(state => state.addMcp)
  const updateMcp = useMcpStore(state => state.updateMcp)

  const { handleInstallMcpEnv, installMcpEnvLoading, clientEnvStatus } =
    useEnvInstall()

  const { exampleList } = useExampleList()

  useEffect(() => {
    if (mcpList.length && !currentEditorMcpId) {
      setCurrentEditorMcpId(mcpList[0].functionId)
    }
  }, [mcpList, currentEditorMcpId])

  const handleSyncMcp = async () => {
    const result = await getMCPSyncConfig()
    NiceModal.show(MCPCustomModal, {
      config: result?.config,
      platform: result?.platform,
      config_id: result?.config_id,
    })
  }

  const handleAICreate = () => {
    NiceModal.show(MCPAICreateModal, {})
  }

  const handleCreateMcp = useMemoizedFn((defaultValue?: McpConfig) => {
    const baseConfig = {
      code: `mcp_${+new Date()}`,
      name: localize('mcp.new_mcp_title', '未命名MCP服务'),
      auto_execute: false,
      is_enable: true,
      source: isClient ? 'CLIENT' : 'CLOUD',
    }

    const newMCPConfig: McpConfig = isClient
      ? {
          ...baseConfig,
          type: 'command',
          command: 'npx',
          args: '',
          env: '',
        }
      : {
          ...baseConfig,
          type: 'sse',
          url: '',
        }

    NiceModal.show(MCPEditorModal, {
      isEdit: false,
      exampleList,
      value: defaultValue ?? newMCPConfig,
      onFinish: async (values: McpConfig) => {
        const functionId = await addMcp(values)
        setCurrentEditorMcpId(functionId)
      },
    })
  })

  const handleEditMcp = useMemoizedFn(
    async (functionId: string, config: Partial<McpConfig>) => {
      const mcp = mcpList.find(mcp => mcp.functionId === functionId)
      if (mcp) {
        await updateMcp(functionId, { ...config })
      }
    },
  )

  const onEditMCP = useMemoizedFn(
    async (functionId: string, mcp: McpListItem) => {
      NiceModal.show(MCPEditorModal, {
        isEdit: true,
        exampleList,
        value: mcp,
        onFinish: async (values: McpConfig) => {
          await handleEditMcp(functionId, values)
        },
      })
    },
  )

  return (
    <div className='flex-1 flex of-hidden'>
      {/* 左侧列表部分 */}
      <SettingSide
        mcpList={mcpList}
        onRename={(id, name) => handleEditMcp(id, { name })}
        onCreate={() => handleCreateMcp()}
        onSelect={setCurrentEditorMcpId}
        currentEditorMcpId={currentEditorMcpId}
        clientEnvStatus={clientEnvStatus}
        installMcpEnvLoading={installMcpEnvLoading}
        onEdit={onEditMCP}
        handleSyncMcp={handleSyncMcp}
        handleAICreate={handleAICreate}
      />
      {/* 右侧内容部分 */}
      <SettingContent
        exampleList={exampleList}
        currentEditorMcpId={currentEditorMcpId}
        clientEnvStatus={clientEnvStatus}
        installMcpEnvLoading={installMcpEnvLoading}
        onInstallMcpEnv={handleInstallMcpEnv}
        onAddExampleMcp={handleCreateMcp}
        onCreateMcp={() => handleCreateMcp()}
        noMcp={!mcpList.length}
        onEditMcp={onEditMCP}
      />
    </div>
  )
}
