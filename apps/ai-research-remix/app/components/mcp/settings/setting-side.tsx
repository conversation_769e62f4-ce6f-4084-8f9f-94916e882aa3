import { memo, useMemo, useState } from 'react'
import { localize } from '@bty/localize'
import { Button, Input, Dropdown } from 'antd'
import type { MenuProps } from 'antd'
import { useMemoizedFn } from 'ahooks'
import { cn } from '@bty/util'
import { useMcpStore, type McpListItem } from '@/store/mcp'
import { Icon } from '@/components/base/icon'
import { McpEnvStatus } from '../mcp-env-status'
import { MCPList } from './mcp-list'

interface SettingSideProps {
  mcpList: McpListItem[]
  onSelect: (functionId: string) => void
  currentEditorMcpId: string
  clientEnvStatus: 'checking' | 'failed' | 'success'
  installMcpEnvLoading: boolean
  onCreate: () => void
  onRename: (functionId: string, name: string) => Promise<void>
  onEdit: (functionId: string, mcp: McpListItem) => Promise<void>
  handleSyncMcp: () => void
  handleAICreate: () => void
}

export const SettingSide = memo<SettingSideProps>(props => {
  const isClient = !!window.MindNote
  const {
    mcpList,
    onSelect,
    currentEditorMcpId,
    clientEnvStatus,
    installMcpEnvLoading,
    onCreate,
    onRename,
    onEdit,
    handleSyncMcp,
    handleAICreate,
  } = props

  const items: MenuProps['items'] = [
    {
      key: '1',
      label: (
        <div onClick={onCreate} className='flex gap-x-8px items-center'>
          <Icon icon='i-icons-add' />
          <span>自定义创建</span>
        </div>
      ),
    },
    ...(isClient
      ? [
          {
            key: '2',
            label: (
              <div
                className='flex gap-x-8px items-center'
                onClick={handleAICreate}
              >
                <Icon icon='i-icons-ai-generate' />
                <span>AI自动创建</span>
              </div>
            ),
          },
        ]
      : []),
    {
      key: '3',
      label: (
        <div onClick={handleSyncMcp} className='flex gap-x-8px items-center'>
          <Icon icon='i-icons-tongbu' />
          <span>第三方同步</span>
        </div>
      ),
    },
  ]

  const deleteMcp = useMcpStore(state => state.deleteMcp)
  const [searchText, setSearchText] = useState('')
  const filteredMcpList = useMemo(() => {
    if (!searchText) return mcpList
    return mcpList.filter(mcp =>
      mcp.name.toLowerCase().includes(searchText.toLowerCase()),
    )
  }, [mcpList, searchText])

  const handleGoToPlatform = () => {
    const platformUrl = process.env.VITE_AI_BASE_URL
    if (platformUrl) {
      window.open(`${platformUrl}/mcp`, '_blank')
    }
  }

  const handleDelete = useMemoizedFn(async (functionId: string) => {
    const newList = await deleteMcp(functionId)
    if (currentEditorMcpId === functionId) {
      onSelect(newList[0]?.functionId ?? '')
    }
  })

  return (
    <div
      className={cn('w-288px shrink-0 h-full of-hidden flex flex-col', {
        'b-r-1 b-#E1E1E5/60% bg-#F7F7FA/60%':
          !isClient || (isClient && clientEnvStatus === 'success'),
      })}
    >
      <div className='flex shrink-0 items-center pl-16px pt-22px pb-10px'>
        <span className='text-20px/24px font-500'>MCP</span>
        {isClient && (
          <div className='ml-8px'>
            <McpEnvStatus
              clientEnvStatus={clientEnvStatus}
              installMcpEnvLoading={installMcpEnvLoading}
            />
          </div>
        )}
      </div>
      {(!isClient || (isClient && clientEnvStatus === 'success')) && (
        <>
          <div className='px-16px pt-12px shrink-0'>
            <Input
              variant='filled'
              placeholder={localize('mcp.search_mcp_title', '搜索MCP服务名称')}
              prefix={<Icon icon='i-icons-search' className='text-16px/16px' />}
              value={searchText}
              onChange={e => setSearchText(e.target.value)}
            />
          </div>
          <div className='pt-12px flex-1 of-hidden flex flex-col'>
            <div className='px-16px shrink-0 mb-12px'>
              <Dropdown menu={{ items }} placement='bottom'>
                <Button
                  type='primary'
                  className='w-full font-500'
                  icon={<Icon icon='i-icons-add' />}
                >
                  {localize('mcp.create_mcp', '创建MCP服务')}
                </Button>
              </Dropdown>
            </div>
            <MCPList
              list={filteredMcpList}
              onSelect={onSelect}
              onDelete={handleDelete}
              onEdit={onEdit}
              onRename={onRename}
              selectedMcpId={currentEditorMcpId}
            />
          </div>

          <div className='w-full mt-16px'>
            <div
              className='border-1 border-[rgba(225,225,229,0.8)] cursor-pointer bg-#FFFFFF rounded-8px mx-16px mb-16px gap-x-6px flex justify-center items-center py-11px'
              onClick={handleGoToPlatform}
            >
              <Icon icon='i-icons-betteryeah' />
              <span className='text-14px/14px font-500 c-#17171D'>
                前往BetterYeah平台创建
              </span>
            </div>
          </div>
        </>
      )}
    </div>
  )
})
