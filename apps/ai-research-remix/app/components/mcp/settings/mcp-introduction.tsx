import { memo } from 'react'
import type { McpConfig } from '@bty/global-types/mcp'
import { Button, Spin, Tooltip } from 'antd'
import { localize } from '@bty/localize'
import { useMcpStore, type McpListItem } from '@/store/mcp'
import type { useExampleList } from '../hooks/useExampleList'
import { basePath } from '@/const'
import { Icon } from '@/components/base/icon'

interface McpIntroductionProps {
  mcp: McpListItem
  exampleList: ReturnType<typeof useExampleList>['exampleList']
  onAddExampleMcp: (exampleMcp: McpConfig) => void
  onEditMcp: (functionId: string, mcp: McpListItem) => void
}

export const McpIntroduction = memo<McpIntroductionProps>(props => {
  const isClient = !!window.MindNote
  const { mcp, exampleList, onAddExampleMcp, onEditMcp } = props

  const connectMcp = useMcpStore(state => state.connectMcp)
  const generateMcpDescription = useMcpStore(
    state => state.generateMcpDescription,
  )

  const renderContent = () => {
    if (mcp.state === 'connecting') {
      return (
        <div className='size-full flex items-center justify-center'>
          <div className='flex flex-col items-center gap-12px'>
            <Spin />
            <div className='text-14px/14px mt-16px font-400 c-#8D8D99'>
              正在连接...
            </div>
          </div>
        </div>
      )
    } else if (mcp.state === 'failed') {
      return (
        <div className='size-full flex items-center justify-center'>
          <div className='flex flex-col items-center'>
            <img
              src={`${basePath}/mcp-connect-error.png`}
              width={140}
              height={140}
              alt='mcp-connect-error'
            />
            <div className='text-14px/16px mt-16px font-400 c-#8D8D99'>
              连接失败，请检查MCP服务是否正常运行
            </div>
            <div className='flex items-center gap-12px mt-24px'>
              <Button onClick={() => onEditMcp(mcp.functionId, mcp)}>
                检查参数
              </Button>
              <Button type='primary' onClick={() => connectMcp(mcp.functionId)}>
                重新连接
              </Button>
            </div>
          </div>
        </div>
      )
    } else if (mcp.descriptionState === 'pending') {
      return (
        <div className='size-full flex items-center justify-center'>
          <div className='flex flex-col items-center'>
            <img
              src={`${basePath}/collection-loading.gif`}
              width={100}
              height={100}
              alt='mcp-description-pending'
            />
            <div className='text-14px/14px mt-16px font-400 c-#8D8D99'>
              介绍分析中...
            </div>
          </div>
        </div>
      )
    } else if (mcp.descriptionState === 'failed') {
      return (
        <div className='size-full flex items-center justify-center'>
          <div className='flex flex-col items-center'>
            <img
              src={`${basePath}/failed.png`}
              width={140}
              height={140}
              alt='mcp-description-failed'
            />
            <div className='text-14px/14px mt-16px font-400 c-#8D8D99'>
              介绍生成失败
            </div>
            <Button
              type='primary'
              onClick={() =>
                generateMcpDescription(mcp.functionId, mcp.tools ?? [])
              }
              className='mt-24px'
            >
              重新生成
            </Button>
          </div>
        </div>
      )
    } else if (mcp.descriptionState === 'success') {
      return (
        <div className='px-24px pb-24px pt-28px'>
          <div className='flex items-center gap-6px text-16px/24px font-500'>
            <Icon icon='i-icons-introduction-color' className='size-20px!' />
            <span>工具介绍</span>
          </div>
          <div className='mt-12px c-#17171D/80%'>
            {mcp.description?.introduction ? (
              mcp.description.introduction
            ) : (
              <span className='text-14px/20px c-#8D8D99'>暂无工具介绍</span>
            )}
          </div>
          <div className='flex items-center gap-6px text-16px/24px font-500 mt-32px'>
            <Icon icon='i-icons-setting-color' className='size-20px!' />
            <span>主要功能</span>
          </div>
          <div className='mt-12px'>
            {mcp.description?.tools.length ? (
              mcp.description.tools.map(tool => (
                <div key={tool.title} className='mt-12px'>
                  <div className='text-15px/24px font-500'>{tool.title}</div>
                  <div className='text-14px/22px mt-6px'>
                    <span className='c-#17171D/80% font-500'>功能说明：</span>
                    <span className='font-400 c-#17171D/80%'>
                      {tool.content}
                    </span>
                  </div>
                </div>
              ))
            ) : (
              <span className='text-14px/20px c-#8D8D99'>
                未生成主要功能介绍
              </span>
            )}
          </div>
          <div className='flex items-center gap-6px text-16px/24px font-500 mt-32px'>
            <Icon icon='i-icons-example-color' className='size-20px!' />
            <span>使用示例</span>
          </div>
          <div className='mt-12px'>
            {mcp.description?.examples.length ? (
              mcp.description.examples.map(example => (
                <div key={example.title} className='mt-12px'>
                  <div className='text-15px/24px font-500'>{example.title}</div>
                  <div className='text-14px/22px mt-6px'>
                    <span className='font-400 c-#17171D/80%'>
                      {example.content}
                    </span>
                  </div>
                </div>
              ))
            ) : (
              <span className='text-14px/20px c-#8D8D99'>暂无示例</span>
            )}
          </div>
        </div>
      )
    }
  }
  return (
    <div className='size-full flex of-hidden'>
      <div className='flex-1 mt-28px of-hidden of-y-auto'>
        <div className='mx-auto h-full max-w-720px'>{renderContent()}</div>
      </div>
      {isClient && (
        <div className='pt-16px shrink-0 of-hidden box-border h-full'>
          <div className='w-330px rd-12px of-hidden h-full flex flex-col'>
            <div className='py-12px px-24px shrink-0'>
              <span className='text-16px/24px font-500'>更多MCP示例</span>
            </div>
            <div className='flex-1 h-full flex flex-col gap-12px of-y-auto px-24px pb-24px'>
              {exampleList.map(exampleMcp => (
                <Tooltip
                  key={exampleMcp.name}
                  title={exampleMcp.description}
                  placement='left'
                >
                  <div
                    className='group flex items-center gap-8px justify-between shrink-0 p-16px b-1 hover:b-primary b-transparent rd-8px cursor-pointer of-hidden relative'
                    style={{
                      background:
                        'linear-gradient(106deg, #F8F7FF 0%, #F3EFFF 100%)',
                    }}
                  >
                    <div className='flex-1 w-full of-hidden'>
                      <div className='flex items-center mb-8px'>
                        <span className='text-14px/16px font-500 c-#17171D'>
                          {exampleMcp.name}
                        </span>
                        {exampleMcp.need_key && (
                          <span className='ml-6px rd-4px bg-#CC7900/8% px-4px py-2px text-12px/12px font-400 c-#CC7900'>
                            {localize('mcp.need_key', '需要Key')}
                          </span>
                        )}
                        {exampleMcp.is_self_developed && (
                          <span className='ml-6px rd-4px bg-[rgba(47,117,255,0.08)] px-4px py-2px text-12px/12px font-400 c-#2F75FF'>
                            {localize('mcp.self_developed', '自研')}
                          </span>
                        )}
                      </div>
                      <div className='text-12px/18px c-#8D8D99 line-clamp-2 w-full group-hover:invisible'>
                        {exampleMcp.description}
                      </div>
                    </div>
                    <Button
                      type='primary'
                      className='shrink-0 invisible w-248px group-hover:visible of-hidden absolute bottom-12px'
                      onClick={e => {
                        e.stopPropagation()
                        onAddExampleMcp(exampleMcp)
                      }}
                    >
                      {localize('mcp.create_now', '立即创建')}
                    </Button>
                  </div>
                </Tooltip>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
})
