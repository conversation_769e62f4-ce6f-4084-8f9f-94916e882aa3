import { memo, useState, useRef, useEffect } from 'react'
import { cn } from '@bty/util'
import { Popover, Input, type InputRef, Image, Modal } from 'antd'
import { localize } from '@bty/localize'
import { useMemoizedFn } from 'ahooks'
import { TagsType } from '@bty/global-types/mcp'
import { type McpListItem } from '@/store/mcp'
import { Icon } from '../../base/icon'
import { basePath } from '@/const'

interface McpListProps {
  list: McpListItem[]
  onSelect: (functionId: string) => void
  selectedMcpId?: string
  onDelete: (functionId: string) => Promise<void>
  onEdit?: (functionId: string, mcp: McpListItem) => Promise<void>
  onRename?: (functionId: string, newName: string) => Promise<void>
}

export const COLOR_MAP = {
  success: '#2CB969',
  failed: '#E24640',
  connecting: '#BBBBC2',
}

const McpItem = memo<{
  mcp: McpListItem
  onClick: () => void
  onDelete: () => Promise<void>
  onEdit?: () => Promise<void>
  onRename?: (newName: string) => Promise<void>
  isSelected: boolean
}>(props => {
  const { mcp, onClick, isSelected, onDelete, onEdit, onRename } = props
  const [menuOpen, setMenuOpen] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [editValue, setEditValue] = useState(mcp.name)
  const inputRef = useRef<InputRef>(null)

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isEditing])

  const handleDelete = useMemoizedFn(() => {
    setMenuOpen(false)
    onDelete()
  })

  const handleSetting = useMemoizedFn(() => {
    setMenuOpen(false)
    onEdit?.()
  })

  const handleEdit = useMemoizedFn(async () => {
    if (editValue.trim() === mcp.name) {
      setIsEditing(false)
      return
    }
    if (onRename) {
      try {
        await onRename(editValue.trim())
      } finally {
        setIsEditing(false)
      }
    }
  })

  const handleKeyDown = useMemoizedFn((e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setEditValue(mcp.name)
      setIsEditing(false)
    }
  })

  const handleNameToEditing = useMemoizedFn(() => {
    setIsEditing(true)
    setEditValue(mcp.name)
  })

  return (
    <div
      className={cn(
        'group py-8px px-8px text-14px font-500 flex items-center justify-between hover:bg-#626999/8% rd-8px cursor-pointer',
        {
          'bg-#7B61FF/8%!': isSelected,
        },
      )}
      onClick={onClick}
      onDoubleClick={handleNameToEditing}
    >
      {mcp.state === 'connecting' ? (
        <div className='mr-6px'>
          <Image
            src={`${basePath}/ask-think-loading.png`}
            width={12}
            className='animate-spin'
          />
        </div>
      ) : (
        <div className='mr-6px size-12px flex items-center justify-center'>
          <span
            className='shrink-0 size-6px inline-block rd-full'
            style={{
              backgroundColor: COLOR_MAP[mcp.state],
            }}
          ></span>
        </div>
      )}
      <div className='flex flex-col flex-1 of-hidden'>
        <div className='flex items-center of-hidden flex-1'>
          {isEditing ? (
            <Input
              ref={inputRef}
              value={editValue}
              onChange={e => setEditValue(e.target.value)}
              onBlur={handleEdit}
              onPressEnter={handleEdit}
              onKeyDown={handleKeyDown}
              className='flex-1 h-24px'
              size='small'
              maxLength={25}
              onClick={e => e.stopPropagation()}
            />
          ) : (
            <div className='flex items-center gap-4px of-hidden'>
              <span
                className='truncate text-14px/24px font-500'
                title={mcp.name}
              >
                {mcp.name}
              </span>
              {mcp.state === 'failed' && (
                <span className='shrink-0 b-1 rd-4px bg-#FFF1EE b-1 b-#FF5219/20% c-#FF5219 text-10px/12px py-1px px-4px h-fit'>
                  异常
                </span>
              )}
            </div>
          )}
        </div>
        <div className='flex items-center of-hidden flex-1 gap-x-4px'>
          <span className='shrink-0 rd-4px b-1 b-solid b-#E1E1E5/60% bg-#626999/4% py-2px px-4px text-12px/12px font-400 c-#8D8D99/80%'>
            {mcp.source === 'CLIENT'
              ? localize('mcp.client', '客户端')
              : localize('mcp.cloud', '云端')}
          </span>
          {mcp.tags &&
            mcp.tags?.map((tag: TagsType, index) => {
              return (
                <span
                  key={index}
                  className='shrink-0 rd-4px b-1 b-solid b-#E1E1E5/60% bg-#626999/4% py-2px px-4px text-12px/12px font-400 c-#8D8D99/80%'
                >
                  {TagsType[tag as keyof typeof TagsType]}
                </span>
              )
            })}
          <span
            className='ml-4px truncate text-12px/16px font-400 c-#8D8D99/80%'
            title={mcp.code}
          >
            ID：{mcp.code}
          </span>
        </div>
      </div>
      {!isEditing && (
        <Popover
          open={menuOpen}
          onOpenChange={setMenuOpen}
          trigger='click'
          arrow={false}
          placement='bottomLeft'
          overlayInnerStyle={{
            padding: 4,
            width: 108,
            borderRadius: 8,
          }}
          content={
            <div
              className='flex flex-col gap-4px'
              onClick={e => e.stopPropagation()}
            >
              <div
                className='flex items-center gap-8px p-8px hover:bg-#626999/8% cursor-pointer rd-4px'
                onClick={handleSetting}
              >
                <Icon icon='i-icons-setting' className='!size-16px' />
                <span className='text-14px/14px font-400'>
                  {localize('mcp.menu.setting', 'MCP设置')}
                </span>
              </div>
              <div
                className='flex items-center gap-8px p-8px hover:bg-#626999/8% cursor-pointer rd-4px'
                onClick={() => {
                  setMenuOpen(false)
                  handleNameToEditing()
                }}
              >
                <Icon icon='i-icons-edit' className='!size-16px' />
                <span className='text-14px/14px font-400'>
                  {localize('mcp.menu.rename', '重命名')}
                </span>
              </div>
              <div
                className='flex items-center gap-8px p-8px hover:bg-#FF5219/8% cursor-pointer rd-4px c-#FF5219'
                onClick={handleDelete}
              >
                <Icon icon='i-icons-delete' className='!size-16px' />
                <span className='text-14px/14px font-400'>
                  {localize('mcp.menu.delete', '删除')}
                </span>
              </div>
            </div>
          }
        >
          <span
            className={cn(
              'ml-4px size-24px flex items-center justify-center rd-4px cursor-pointer hover:bg-#626999/8% invisible group-hover:visible',
              {
                'bg-#626999/8% visible': menuOpen,
              },
            )}
            onClick={e => e.stopPropagation()}
          >
            <Icon icon='i-icons-more' className='!size-16px' />
          </span>
        </Popover>
      )}
    </div>
  )
})

export const MCPList = memo<McpListProps>(props => {
  const { list, onSelect, onDelete, onRename, onEdit, selectedMcpId } = props
  const [modal, modalContextHolder] = Modal.useModal()

  const handleDelete = useMemoizedFn(async (functionId: string) => {
    const modalInstance = modal.confirm({
      content: (
        <div className='text-14px/22px font-500'>
          {localize(
            'mcp.delete.confirm.content',
            '删除后，MCP服务将不可用，是否确认删除？',
          )}
        </div>
      ),
      okText: localize('mcp.delete.confirm.delete', '删除'),
      cancelText: localize('mcp.delete.confirm.cancel', '取消'),
      icon: null,
      centered: true,
      width: 480,
      onOk: async () => {
        modalInstance.update({
          okButtonProps: {
            loading: true,
            danger: true,
          },
        })
        try {
          await onDelete(functionId)
        } finally {
          modalInstance.destroy()
        }
        return Promise.reject()
      },
      okButtonProps: {
        danger: true,
      },
    })
  })

  return (
    <div className='of-y-auto flex-1 px-16px pb-12px'>
      {list.map(mcp => {
        return (
          <McpItem
            mcp={mcp}
            key={mcp.functionId}
            onClick={() => onSelect(mcp.functionId)}
            isSelected={selectedMcpId === mcp.functionId}
            onDelete={() => handleDelete(mcp.functionId)}
            onEdit={onEdit ? () => onEdit(mcp.functionId, mcp) : undefined}
            onRename={
              onRename
                ? newName => onRename(mcp.functionId, newName)
                : undefined
            }
          />
        )
      })}
      {modalContextHolder}
    </div>
  )
})
