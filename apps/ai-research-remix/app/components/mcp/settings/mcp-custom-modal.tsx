import NiceModal from '@ebay/nice-modal-react'
import { useState, useMemo } from 'react'
import { Form, Select, Input, Button } from 'antd'
import { localize } from '@bty/localize'
import { useRequest } from 'ahooks'
import { saveAndSyncMCPConfig } from '@apis/mindnote/mcp'
import { Modal } from '../../base/modal'
import { useMcpStore } from '@/store/mcp'
import { FormLabel } from './mcp-editor-modal'
import { CustomContent } from './CreateMCPModal/CustomContent'

interface PlatformConfig {
  api_key: string
  platform?: string
  api_base_url?: string
  api_key_link?: string
  workspace_id: string
  mcp_server_link?: string
}

interface MCPCustomModalInnerProps {
  config: {
    [key: string]: PlatformConfig
  }
  platform: string[]
  config_id: string | undefined
}

function MCPCustomModalInner(props: MCPCustomModalInnerProps) {
  const { config, platform, config_id } = props
  const [selectedValue, setSelectedValue] = useState(platform?.[0])
  const { runAsync: fetchSaveAndSyncMCPConfig, loading } = useRequest(
    saveAndSyncMCPConfig,
    {
      manual: true,
    },
  )
  const addMcpFrontSave = useMcpStore(state => state.addMcpFrontSave)
  const modal = NiceModal.useModal()
  const [form] = Form.useForm()

  const SelectedItem = useMemo(
    () => config?.[selectedValue],
    [config, config_id, selectedValue],
  )

  const handleSubmit = async (values: PlatformConfig) => {
    const { workspace_id, api_key } = values
    const config = {
      ...SelectedItem,
      api_key,
      workspace_id,
    }
    const res = await fetchSaveAndSyncMCPConfig({ config, config_id })
    if (res) {
      form.resetFields()
      setSelectedValue(platform?.[0])
      modal?.hide()
      res?.forEach((item: any) => {
        addMcpFrontSave(item)
      })
    }
  }

  const handleCancel = () => {
    modal.hide()
  }
  return (
    <Modal
      title='创建MCP服务'
      open={modal.visible}
      footer={null}
      maskClosable={false}
      width={600}
      onCancel={handleCancel}
    >
      <Form form={form} layout='vertical' onFinish={handleSubmit}>
        <Form.Item
          name='server_provider'
          label={
            <FormLabel
              label={localize('mcp.form.provider', '选择服务商')}
              required
            />
          }
          initialValue={platform?.[0]}
          rules={[
            {
              message: localize('mcp.form.name.required', '请选择服务商'),
              required: true,
            },
          ]}
        >
          <Select
            value={selectedValue}
            defaultValue={platform?.[0]}
            options={(platform ?? [])?.map(option => ({
              label: option,
              value: option,
            }))}
            onChange={value => {
              setSelectedValue(value)
              form.setFieldsValue({
                api_key:
                  value === 'BatterYeah'
                    ? config?.BatterYeah?.api_key
                    : config?.ModelScope?.api_key,
              })
            }}
          />
        </Form.Item>
        <CustomContent
          apiKeyLink={SelectedItem?.api_key_link}
          selectedValue={selectedValue}
          mcpServerLink={SelectedItem?.mcp_server_link}
        />
        {selectedValue === 'BatterYeah' && (
          <Form.Item
            name='workspace_id'
            className='mt-24px'
            initialValue={SelectedItem?.workspace_id}
            label={
              <div className='text-14px/24px font-500 c-#17171D'>空间ID</div>
            }
            rules={[
              {
                message: localize('mcp.workspace.id.required', '请输入空间ID'),
                required: true,
              },
            ]}
          >
            <Input
              placeholder={localize(
                'enter.mcp.space.workspace.id',
                '请输入待同步MCP所在空间的空间ID',
              )}
            />
          </Form.Item>
        )}
        <Form.Item
          name='api_key'
          className='mt-24px'
          initialValue={config?.BatterYeah?.api_key}
          label={
            <div className='text-14px/24px font-500 c-#17171D'>API Key</div>
          }
          rules={[
            {
              message: localize('mcp.api.key.required', '请输入API Key'),
              required: true,
            },
          ]}
        >
          <Input
            placeholder={localize(
              'enter.mcp.space.api.key',
              '请输入待同步MCP所在空间的API Key',
            )}
          />
        </Form.Item>
        <div className='flex justify-end gap-x-12px'>
          <Button onClick={handleCancel} disabled={loading}>
            取消
          </Button>
          <Button type='primary' htmlType='submit' loading={loading}>
            完成
          </Button>
        </div>
      </Form>
    </Modal>
  )
}

export const MCPCustomModal = NiceModal.create(MCPCustomModalInner)
