import { memo, useState } from 'react'
import NiceModal from '@ebay/nice-modal-react'
import type { McpConfig } from '@bty/global-types/mcp'
import { localize } from '@bty/localize'
import { Button, Form, Input, message, Select, Tooltip } from 'antd'
import { useMemoizedFn, useRequest } from 'ahooks'
import { generateMCPCode } from '@apis/mindnote/mcp'
import { cn } from '@bty/util'
import { Modal } from '../../base/modal'
import { Icon, IconButton } from '../../base/icon'
import type { useExampleList } from '../hooks/useExampleList'
import { useGetValue } from '@/hooks/use-get-value'
import { ExamplePopover } from './example-popover'
import { AiParseConfig } from './ai-parse-config'
import { McpStorePopover } from './mcp-store-popover'

interface MCPEditorModalProps {
  value: McpConfig
  isEdit: boolean
  exampleList: ReturnType<typeof useExampleList>['exampleList']
  onFinish: (values: McpConfig) => Promise<void> | void
}

const MCPEditorModalInner = memo<MCPEditorModalProps>(props => {
  const isClient = !!window.MindNote
  const { value, isEdit, exampleList = [], onFinish } = props

  const [viewMode, setViewMode] = useState<'json' | 'form'>('form')
  const [loading, setLoading] = useState(false)
  const [jsonValue, setJsonValue] = useState('')

  const [form] = Form.useForm()
  const modal = NiceModal.useModal()

  const innerCode = useGetValue(['code'], form) ?? ''
  const innerName = useGetValue(['name'], form) ?? ''

  const handleCreateWithExample = useMemoizedFn((mcp: McpConfig) => {
    form.setFieldsValue(mcp)
  })

  const { runAsync: generateCode, loading: codeGenerateLoading } = useRequest(
    generateMCPCode,
    {
      manual: true,
    },
  )

  const handleCancel = useMemoizedFn(() => {
    modal.hide()
    setTimeout(() => {
      modal.remove()
    }, 300)
  })

  const handleFinish = useMemoizedFn(async () => {
    setLoading(true)
    try {
      const values = await form.validateFields()
      await onFinish(values)
      handleCancel()
    } finally {
      setLoading(false)
    }
  })

  const parseJsonToConfig = useMemoizedFn(() => {
    try {
      const res = JSON.parse(jsonValue)
      const { mcpServers = {} as any } = res
      const mcpServer = Object.entries(mcpServers)[0]
      if (!mcpServer) {
        message.warning('未检测到合法的MCP配置')
        return
      }
      const [key, value] = mcpServer as [string, any]
      const code = key
      const command = value.command ?? ''
      const args = value.args ?? []
      const env = value.env ?? {}
      const url = value.url ?? ''

      if ((!url && !command) || !code) {
        message.warning('非法配置')
        return
      }
      const type = command ? 'command' : 'sse'
      const argsString = args.join(' ')
      const envString = Object.entries(env)
        .map(([key, value]) => `${key}=${value}`)
        .join('\n')

      if (
        type === 'command' &&
        !['npx', 'uv', 'uvx'].includes(value.command ?? '')
      ) {
        message.warning(`非法配置，不支持的命令: ${value.command}`)
        return
      }
      form.setFieldsValue({
        type,
        command,
        code,
        name: code,
        args: argsString,
        env: envString,
        url,
        headers: value.headers ?? '',
      })
      setViewMode('form')
      message.success('解析成功')
    } catch (error) {
      message.warning('解析失败，请检查JSON格式')
    }
  })

  return (
    <Modal
      open={modal.visible}
      footer={null}
      closable={false}
      maskClosable={false}
      width={600}
      styles={{
        body: {
          padding: 0,
        },
      }}
      height='auto'
    >
      <div className='flex flex-col'>
        <div className='flex items-center justify-between px-16px py-12px b-b-1px b-#E1E1E5/60%'>
          <div className='text-16px/24px font-500'>
            {isEdit
              ? localize('mcp.edit_mcp_title', '设置MCP服务')
              : localize('mcp.create_mcp_title', '创建MCP服务')}
          </div>
          <div className='flex items-center'>
            {viewMode === 'form' && (
              <>
                {isClient && (
                  <ExamplePopover
                    list={exampleList}
                    onCreateWithExample={handleCreateWithExample}
                  />
                )}
                <Tooltip
                  title={localize('mcp.json.create.tooltip', 'JSON导入')}
                >
                  <IconButton
                    icon='i-icons-json2'
                    size='size-24px'
                    iconSize='size-16px'
                    className='rd-4px! mr-8px'
                    onClick={() => setViewMode('json')}
                  />
                </Tooltip>
                <AiParseConfig
                  onFinish={config => {
                    if (!isClient && config.type === 'command') {
                      message.warning(
                        localize('ai.parse.warning', 'Web端不支持命令行'),
                      )
                      return
                    }
                    form.setFieldsValue(config)
                  }}
                />
                <McpStorePopover />
                <div className='w-1px h-24px bg-#E1E1E5 mx-12px'></div>
              </>
            )}
            <IconButton
              icon='i-icons-close'
              size='size-24px'
              iconSize='size-16px'
              className='rd-4px!'
              onClick={handleCancel}
            />
          </div>
        </div>
        {viewMode === 'form' && (
          <div className='max-h-70vh of-auto py-24px px-32px'>
            <Form
              form={form}
              initialValues={value}
              layout='vertical'
              requiredMark={false}
            >
              <Form.Item
                label={
                  <FormLabel
                    label={localize('mcp.form.name', '名称')}
                    required
                  />
                }
                name='name'
                rules={[
                  {
                    required: true,
                    message: localize('mcp.form.name.required', '请输入名称'),
                  },
                ]}
              >
                <Input
                  variant='filled'
                  className='w-full'
                  placeholder={localize(
                    'mcp.form.name.placeholder',
                    '请输入名称',
                  )}
                  maxLength={50}
                  suffix={
                    <span className='text-12px'>
                      <span className='text-font-fade'>{innerName.length}</span>
                      <span className='text-font-fade/60'> / 50</span>
                    </span>
                  }
                  autoCorrect='off'
                  autoComplete='off'
                  autoCapitalize='off'
                  spellCheck={false}
                />
              </Form.Item>
              <Form.Item
                label={
                  <div className='font-500 flex items-center justify-between w-full'>
                    <div className='flex items-center'>
                      <span>ID</span>
                      <span className='text-error ml-3px relative top-2px'>
                        *
                      </span>
                    </div>
                    <div
                      className={cn(
                        'flex items-center gap-4px cursor-pointer c-#7B61FF text-14px/16px font-500',
                        {
                          'opacity-50 cursor-not-allowed pointer-events-none':
                            codeGenerateLoading,
                        },
                      )}
                      onClick={() => {
                        const name = form.getFieldValue('name')
                        if (!name) {
                          message.warning('请先输入名称')
                          return
                        }
                        generateCode(name, isClient ? 'CLIENT' : 'CLOUD').then(
                          res => {
                            form.setFieldValue('code', res.code)
                          },
                        )
                      }}
                    >
                      <Icon
                        icon='i-icons-auto-tag'
                        className={cn('size-16px c-#7B61FF', {
                          'animate-spin': codeGenerateLoading,
                        })}
                      />
                      <span>自动生成</span>
                    </div>
                  </div>
                }
                name='code'
                className='mt-8px [&_label]:w-full [&_label]:after:hidden!'
                rules={[
                  {
                    required: true,
                    message: localize('mcp.form.code.required', '请输入ID'),
                  },
                  {
                    pattern: /^[a-zA-Z_$][0-9a-zA-Z_$]*$/,
                    message: localize(
                      'enter_code_validate_message',
                      '仅支持字母、数字、下划线',
                    ),
                  },
                  {
                    validator: (_, value) => {
                      if (value && value.includes('__')) {
                        return Promise.reject(
                          localize(
                            'mcp.form.code.no_consecutive_underscores',
                            '不允许出现连续的下划线',
                          ),
                        )
                      }
                      return Promise.resolve()
                    },
                  },
                ]}
              >
                <Input
                  variant='filled'
                  maxLength={30}
                  suffix={
                    <span className='text-12px'>
                      <span className='text-font-fade'>{innerCode.length}</span>
                      <span className='text-font-fade/60'> / 30</span>
                    </span>
                  }
                  placeholder={localize(
                    'mcp.form.code.placeholder',
                    '输入ID，作为唯一标识，仅支持字母、数字、下划线，如：web_search',
                  )}
                  autoCorrect='off'
                  autoComplete='off'
                  autoCapitalize='off'
                  spellCheck={false}
                />
              </Form.Item>
              <Form.Item
                name='type'
                label={
                  <div className='flex items-center'>
                    <span className='font-500'>
                      {localize('mcp.form.type', '类型')}
                    </span>
                    {!isClient && (
                      <div className='c-#8D8D99 text-12px/16px font-400'>
                        <span>
                          （
                          {localize(
                            'mcp.form.type.web_only',
                            'web端仅支持SSE / HTTP',
                          )}
                          ）
                        </span>
                        {/* <a
                          href='https://ai.aimindnote.com/download'
                          target='_blank'
                          rel='noreferrer'
                          className='c-primary font-500'
                        >
                          客户端
                        </a> */}
                        {/* <span>使用）</span> */}
                      </div>
                    )}
                  </div>
                }
              >
                <Select
                  className='w-full [&.ant-select-disabled_.ant-select-selector]:b-none!'
                  variant='filled'
                  options={[
                    {
                      label: localize(
                        'mcp.form.command.label',
                        '标准输入/输出(Stdio)',
                      ),
                      value: 'command',
                    },
                    {
                      label: localize(
                        'mcp.form.sse.label',
                        '服务器发送事件(SSE)',
                      ),
                      value: 'sse',
                    },
                    {
                      label: localize(
                        'mcp.form.streamableHttp.label',
                        '可流式传输的Http(StreamableHttp)',
                      ),
                      value: 'streamableHttp',
                    },
                  ].filter(item => isClient || item.value !== 'command')}
                  placeholder={localize(
                    'mcp.form.type.placeholder',
                    '请选择类型',
                  )}
                />
              </Form.Item>
              <Form.Item noStyle dependencies={['type']}>
                {({ getFieldValue }) => {
                  const type = getFieldValue('type')

                  if (type === 'command') {
                    return (
                      <>
                        <Form.Item
                          label={<FormLabel label='Command' required />}
                        >
                          <div className='flex items-center gap-12px'>
                            <Form.Item name='command' noStyle>
                              <Select
                                className='w-80px!'
                                variant='filled'
                                options={[
                                  { label: 'npx', value: 'npx' },
                                  { label: 'uv', value: 'uv' },
                                  { label: 'uvx', value: 'uvx' },
                                ]}
                              />
                            </Form.Item>
                            <Form.Item
                              name='args'
                              rules={[
                                {
                                  required: true,
                                  message: localize(
                                    'mcp.form.args.required',
                                    '请输入Command指令',
                                  ),
                                },
                              ]}
                              noStyle
                            >
                              <Input
                                variant='filled'
                                className='w-full'
                                placeholder={localize(
                                  'mcp.form.args.placeholder',
                                  '输入Command指令',
                                )}
                                autoCorrect='off'
                                autoComplete='off'
                                autoCapitalize='off'
                                spellCheck={false}
                              />
                            </Form.Item>
                          </div>
                        </Form.Item>
                        <Form.Item
                          label={
                            <FormLabel
                              label={localize('mcp.form.env.label', '环境变量')}
                            />
                          }
                          name='env'
                        >
                          <Input.TextArea
                            rows={3}
                            variant='filled'
                            placeholder='KEY=value&#13;KEY2=value2'
                            autoCorrect='off'
                            autoComplete='off'
                            autoCapitalize='off'
                            spellCheck={false}
                          />
                        </Form.Item>
                      </>
                    )
                  }

                  if (type === 'sse' || type === 'streamableHttp') {
                    return (
                      <>
                        <Form.Item
                          name='url'
                          required
                          label={<FormLabel label='URL' required />}
                          rules={[
                            {
                              required: true,
                              message: localize(
                                'mcp.form.url.required',
                                '请输入 URL',
                              ),
                            },
                            {
                              type: 'url',
                              message: localize(
                                'mcp.form.url.invalid',
                                '请输入正确的URL',
                              ),
                            },
                          ]}
                        >
                          <Input
                            variant='filled'
                            placeholder={localize(
                              'mcp.form.url.placeholder',
                              '输入 URL',
                            )}
                            autoCorrect='off'
                            autoComplete='off'
                            autoCapitalize='off'
                            spellCheck={false}
                          />
                        </Form.Item>
                        <Form.Item
                          label={
                            <FormLabel
                              label={localize(
                                'mcp.form.headers.label',
                                '请求头',
                              )}
                            />
                          }
                          name='headers'
                        >
                          <Input.TextArea
                            rows={3}
                            variant='filled'
                            placeholder='KEY=value&#13;KEY2=value2'
                            autoCorrect='off'
                            autoComplete='off'
                            autoCapitalize='off'
                            spellCheck={false}
                          />
                        </Form.Item>
                      </>
                    )
                  }
                }}
              </Form.Item>
              <div className='flex items-center justify-end gap-12px mt-24px'>
                <Button onClick={handleCancel}>
                  {localize('mcp.form.cancel', '取消')}
                </Button>
                <Button type='primary' loading={loading} onClick={handleFinish}>
                  {loading
                    ? isEdit
                      ? localize('mcp.form.saving', '正在保存')
                      : localize('mcp.form.creating', '正在创建')
                    : localize('mcp.form.finish', '完成')}
                </Button>
              </div>
            </Form>
          </div>
        )}
        {viewMode === 'json' && (
          <div className='w-full flex flex-col'>
            <Input.TextArea
              className='w-full h-520px! border-none! outline-0! shadow-none! resize-none!'
              placeholder={`仅支持解析一个MCP，如果有多个仅解析第一个，示例：
{
  "mcpServers": {
    "RedNote MCP": {
      "command": "npx",
      "args": ["rednote-mcp", "--stdio"]
    }
  }
}`}
              autoCorrect='off'
              autoComplete='off'
              autoCapitalize='off'
              spellCheck={false}
              value={jsonValue}
              onChange={e => setJsonValue(e.target.value)}
            />
            <div className='shrink-0 pt-12px px-32px pb-24px flex items-center justify-end b-t-1px b-#E1E1E5/60% gap-12px'>
              <Button onClick={() => setViewMode('form')}>
                {localize('mcp.json.back', '返回')}
              </Button>
              <Button type='primary' onClick={parseJsonToConfig}>
                {localize('mcp.json.import', '确认')}
              </Button>
            </div>
          </div>
        )}
      </div>
    </Modal>
  )
})

export function FormLabel(props: { label: string; required?: boolean }) {
  return (
    <span className='font-500'>
      <span>{props.label}</span>
      {props.required && (
        <span className='text-error ml-3px relative top-2px'>*</span>
      )}
    </span>
  )
}

export const MCPEditorModal =
  /* @__PURE__ */ NiceModal.create(MCPEditorModalInner)
