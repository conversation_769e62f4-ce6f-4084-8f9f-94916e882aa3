import { Button, Form, Input, message, Popover, Tooltip } from 'antd'
import { memo, useRef, useState } from 'react'
import { useMemoizedFn } from 'ahooks'
import { localize } from '@bty/localize'
import { getConfigFromUrl } from '@apis/mindnote/mcp'
import type { McpConfig } from '@bty/global-types/mcp'
import { MCP_MARKET_URL } from '../constant'
import { basePath } from '@/const'
import { Icon, IconButton } from '@/components/base/icon'

interface AiParseConfigProps {
  onFinish: (values: McpConfig) => Promise<void> | void
}

export const AiParseConfig = memo<AiParseConfigProps>(props => {
  const { onFinish } = props
  const [aiParsedPopoverOpen, setAiParsedPopoverOpen] = useState(false)
  const [parseConfigLoading, setParseConfigLoading] = useState(false)
  const isCanceledRef = useRef(false)
  const [form] = Form.useForm()

  const handleGetConfigFromUrl = useMemoizedFn(async () => {
    try {
      isCanceledRef.current = false
      const values = await form.validateFields()
      const mcpDocsUrl = values.url
      setParseConfigLoading(true)
      try {
        const res = await getConfigFromUrl(mcpDocsUrl)
        if (isCanceledRef.current) return
        const { mcpServers = {} } = res
        const mcpServer = Object.entries(mcpServers)[0]
        if (!mcpServer) {
          throw new Error('未检测到合法的MCP配置')
        }
        const [key, value] = mcpServer
        const code = key
        const command = value.command ?? ''
        const args = value.args ?? []
        const env = value.env ?? {}
        const url = value.url ?? ''
        if ((!url && !command) || !code) {
          throw new Error('非法配置')
        }
        const type = command ? 'command' : 'sse'
        const argsString = args.join(' ')
        const envString = Object.entries(env)
          .map(([key, value]) => `${key}=${value}`)
          .join('\n')

        if (
          type === 'command' &&
          !['npx', 'uv', 'uvx'].includes(value.command ?? '')
        ) {
          throw new Error(`非法配置，不支持的命令: ${value.command}`)
        }

        onFinish({
          type,
          command,
          code,
          name: code,
          args: argsString,
          env: envString,
          auto_execute: false,
        } as McpConfig)
        setAiParsedPopoverOpen(false)
        form.resetFields()
        message.success(localize('mcp.ai.parse_success', '解析成功'))
      } catch (_error) {
        message.warning('解析失败，未检测到合法的MCP配置')
      } finally {
        setParseConfigLoading(false)
      }
    } catch (_) {}
  })

  return (
    <Popover
      arrow={false}
      placement='bottomLeft'
      overlayInnerStyle={{ padding: 12 }}
      trigger='click'
      open={aiParsedPopoverOpen}
      onOpenChange={setAiParsedPopoverOpen}
      destroyTooltipOnHide={true}
      content={
        <div>
          <div className='mb-12px flex items-center'>
            <span className='font-500'>
              <span>MCP服务链接</span>
              <span className='text-error'>*</span>
            </span>
            <Popover
              trigger='hover'
              placement='top'
              content={
                <div>
                  <div className='flex items-center text-12px/18px font-500'>
                    <span>从</span>
                    <a
                      href={MCP_MARKET_URL}
                      target='_blank'
                      rel='noreferrer'
                      className='c-primary mx-2px'
                    >
                      MCP市场
                    </a>
                    <span>复制MCP服务链接，AI自动添加</span>
                  </div>
                  <img
                    src={`${basePath}/mcp-ai-parse-guide.png`}
                    alt='AI辅助创建示意图'
                    className='w-240px h-130px mt-8px'
                  />
                </div>
              }
            >
              <Icon icon='i-icons-question' className='ml-4px c-#8D8D99/40%' />
            </Popover>
          </div>
          <Form form={form} requiredMark={false} layout='vertical'>
            <Form.Item
              name='url'
              rules={[
                { required: true, message: '请输入MCP服务链接' },
                {
                  type: 'url',
                  message: '请输入正确的URL，以http/https开头',
                },
              ]}
              className='mb-0!'
            >
              <Input
                variant='filled'
                placeholder={localize(
                  'mcp.form.url.placeholder',
                  '请输入MCP服务链接',
                )}
                className='w-376px'
              />
            </Form.Item>
          </Form>
          <div className='flex justify-end gap-12px mt-12px'>
            <Button
              onClick={() => {
                setAiParsedPopoverOpen(false)
                isCanceledRef.current = true
                setParseConfigLoading(false)
                setTimeout(() => {
                  form.resetFields()
                }, 300)
              }}
            >
              {localize('mcp.form.cancel', '取消')}
            </Button>
            <Button
              type='primary'
              loading={parseConfigLoading}
              onClick={() => handleGetConfigFromUrl()}
            >
              {parseConfigLoading
                ? localize('mcp.form.parsing', '正在解析')
                : localize('mcp.form.parse', '解析')}
            </Button>
          </div>
        </div>
      }
    >
      <Tooltip title={localize('mcp.ai.create.tooltip', 'AI辅助创建')}>
        <IconButton
          icon='i-icons-ai-generate'
          size='size-24px'
          iconSize='size-16px'
          className='rd-4px! mr-8px'
        />
      </Tooltip>
    </Popover>
  )
})
