import { localize } from '@bty/localize'
import { Popover, Tooltip } from 'antd'
import { memo, useState } from 'react'
import { Icon, IconButton } from '@/components/base/icon'
import { basePath } from '@/const'

const storeList = [
  {
    name: 'modelscope.cn',
    link: 'https://www.modelscope.cn/mcp',
    icon: `${basePath}/model-scope.png`,
  },
  {
    name: 'mcpmarket.cn',
    link: 'https://mcpmarket.cn',
    icon: `${basePath}/mcpmarket.png`,
  },
  {
    name: 'mcp.so',
    link: 'https://mcp.so',
    icon: `${basePath}/mcpso.png`,
  },
]

export const McpStorePopover = memo(() => {
  const [menuOpen, setMenuOpen] = useState(false)

  return (
    <Popover
      placement='bottomLeft'
      trigger='hover'
      overlayInnerStyle={{ padding: 4 }}
      arrow={false}
      open={menuOpen}
      onOpenChange={setMenuOpen}
      content={
        <div>
          <div className='px-12px pb-4px pt-10px text-14px/14px font-500'>
            {localize('title', 'MCP市场')}
          </div>
          <div className='flex flex-col gap-4px mt-4px w-232px'>
            {storeList.map(item => (
              <div
                key={item.name}
                className='group py-6px px-12px rd-8px hover:bg-#626999/8% flex items-center justify-between cursor-pointer'
                onClick={() => {
                  setMenuOpen(false)
                  window.open(item.link, '_blank')
                }}
              >
                <div className='flex items-center gap-6px'>
                  <img src={item.icon} alt={item.name} className='size-24px' />
                  <div className='text-14px/14px font-400'>{item.name}</div>
                </div>
                <Icon
                  icon='i-icons-open-link'
                  className='c-#626999/60% size-16px! invisible group-hover:visible'
                />
              </div>
            ))}
          </div>
        </div>
      }
    >
      <Tooltip title={localize('mcp.market.tooltip', 'MCP市场')}>
        <IconButton
          icon='i-icons-store'
          size='size-24px'
          iconSize='size-16px'
          className='rd-4px!'
        />
      </Tooltip>
    </Popover>
  )
})
