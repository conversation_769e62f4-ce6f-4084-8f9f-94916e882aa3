import { useState } from 'react'
import NiceModal from '@ebay/nice-modal-react'
import { useRequest } from 'ahooks'
import { getMCPAnalyse, getMCPSearch } from '@apis/mindnote/mcp'
import { Modal } from '../../base/modal'
import mcpBg from '../../../../public/mcp-bg.png'
import { MessageInput } from './CreateMCPModal/MessageInput'
import { AICreateStepBar } from './CreateMCPModal/AICreateStepBar'
import { MCPInstallByAIResearch } from './CreateMCPModal/MCPInstallByAIResearch'

export enum ModalContentType {
  INPUT = 'Input',
  LOADING = 'Loading',
  INSTALL = 'Install',
}

export function AICreateModalInner() {
  const modal = NiceModal.useModal()
  const [contentType, setContentType] = useState(ModalContentType.INPUT)
  const [currentStep, setCurrentStep] = useState(0)
  const [content, setContent] = useState('')

  const { data: mcpAnalyseData, runAsync: getMCPAnalyseAsync } = useRequest(
    getMCPAnalyse,
    {
      manual: true,
    },
  )
  const {
    data: mcpSearchData,
    runAsync: getMCPSearchAsync,
    cancel: cancelGetMCPSearchAsync,
  } = useRequest(getMCPSearch, {
    manual: true,
  })

  const handleCancel = () => {
    cancelGetMCPSearchAsync()
    setContentType(ModalContentType.INPUT)
    setCurrentStep(0)
    modal.hide()
    setTimeout(() => {
      modal.remove()
    }, 300)
  }
  const handleSubmit = async (content: string) => {
    setContentType(ModalContentType.LOADING)
    const res = await getMCPAnalyseAsync(content)
    if (res) {
      setCurrentStep(currentStep + 1)
    }
  }
  const handleSearchMcp = async (
    newMcpAnalyseData: {
      function: string
      keyword: string
      keyword_en: string
      reason: string
    }[],
  ) => {
    if (!newMcpAnalyseData) return
    setCurrentStep(currentStep + 1)
    const res = await getMCPSearchAsync({
      content,
      search_tasks: newMcpAnalyseData,
    })
    if (res) {
      setContent('')
      setContentType(ModalContentType.INSTALL)
    }
    console.log('res', res)
  }

  return (
    <Modal
      open={modal.visible}
      footer={null}
      maskClosable={false}
      width={1120}
      onCancel={handleCancel}
      styles={{
        content: {
          backgroundImage: `url(${mcpBg})`,
          backgroundSize: '100% auto',
          backgroundPosition: 'top',
          backgroundRepeat: 'no-repeat',
        },
      }}
    >
      <div className='h-680px flex flex-col justify-center items-center w-full gap-y-24px'>
        {contentType === ModalContentType.INPUT && (
          <>
            <div className='text-16px/24px font-500'>
              你好，写出你的具体需求或指令，我将为你推荐、安装MCP
            </div>
            <div className='w-832px'>
              <MessageInput
                content={content}
                handleSubmit={handleSubmit}
                setContent={setContent}
              />
            </div>
          </>
        )}
        {contentType === ModalContentType.LOADING && (
          <AICreateStepBar
            currentStep={currentStep}
            mcpAnalyseData={mcpAnalyseData}
            handleSearchMcp={handleSearchMcp}
          />
        )}
        {contentType === ModalContentType.INSTALL && (
          <MCPInstallByAIResearch
            mcpSearchData={mcpSearchData}
            handleCancel={handleCancel}
            setContentType={setContentType}
            setCurrentStep={setCurrentStep}
          />
        )}
      </div>
    </Modal>
  )
}

export const MCPAICreateModal = NiceModal.create(AICreateModalInner)
