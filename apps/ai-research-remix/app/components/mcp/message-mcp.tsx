import { memo, useMemo, useState } from 'react'
import { cn } from '@bty/util'
import { Popover, Switch, Tooltip, Image, Spin, Button } from 'antd'
import { useMemoizedFn } from 'ahooks'
import { localize } from '@bty/localize'
import { useNavigate } from '@remix-run/react'
import type { McpConfig } from '@bty/global-types/mcp'
import NiceModal from '@ebay/nice-modal-react'
import { Icon, IconButton } from '../base/icon'
import type { McpListItem } from '@/store/mcp'
import { useMcpStore } from '@/store/mcp'
import { basePath } from '@/const'
import { COLOR_MAP } from './settings/mcp-list'
import { useEnvInstall } from './hooks/useEnvInstall'
import { McpEnvStatus } from './mcp-env-status'
import { McpEnvReady } from './mcp-env-ready'
import { useExampleList } from './hooks/useExampleList'
import { MCPEditorModal } from './settings/mcp-editor-modal'

interface MessageMcpConfigProps {
  enabledMCPIds: string[]
  onEnable: (id: string, enable: boolean) => Promise<void> | void
}

export const MessageMcpConfig = memo<MessageMcpConfigProps>(props => {
  const { enabledMCPIds, onEnable } = props

  console.log('enabledMCPIds', enabledMCPIds)

  const isClient = !!window.MindNote
  const [mcpListOpen, setMcpListOpen] = useState(false)
  const [loadingMCPIds, setLoadingMCPIds] = useState<Set<string>>(new Set())
  const mcpList = useMcpStore(state => state.list)
  const connectMcp = useMcpStore(state => state.connectMcp)
  const generateMcpDescription = useMcpStore(
    state => state.generateMcpDescription,
  )
  const navigate = useNavigate()
  const addMcp = useMcpStore(state => state.addMcp)
  const updateMcp = useMcpStore(state => state.updateMcp)

  const [connectLoading, setConnectLoading] = useState(false)
  const { exampleList } = useExampleList()

  const toMcpPage = useMemoizedFn((id?: string) => {
    navigate(`/main/mcp?id=${id}`)
  })

  const handleReconnect = useMemoizedFn(() => {
    setConnectLoading(true)
    const connectList = mcpList.map(item => connectMcp(item.functionId))
    Promise.all(connectList).finally(() => {
      setConnectLoading(false)
    })
  })

  const handleCreateMCP = useMemoizedFn(() => {
    setMcpListOpen(false)
    const baseConfig = {
      code: `mcp_${+new Date()}`,
      name: localize('mcp.new_mcp_title', '未命名MCP服务'),
      auto_execute: false,
      is_enable: true,
      source: isClient ? 'CLIENT' : 'CLOUD',
    }

    const newMCPConfig: McpConfig = isClient
      ? {
          ...baseConfig,
          type: 'command',
          command: 'npx',
          args: '',
          env: '',
        }
      : {
          ...baseConfig,
          type: 'sse',
          url: '',
        }

    NiceModal.show(MCPEditorModal, {
      isEdit: false,
      exampleList,
      value: newMCPConfig,
      onFinish: async (values: McpConfig) => {
        const functionId = await addMcp(values)
        // toMcpPage(functionId)
        onEnable(functionId, true)
      },
    })
  })

  const handleEditMCP = useMemoizedFn((mcp: McpListItem) => {
    setMcpListOpen(false)
    NiceModal.show(MCPEditorModal, {
      isEdit: true,
      exampleList,
      value: mcp,
      onFinish: async (values: McpConfig) => {
        const mcpItem = mcpList.find(item => item.functionId === mcp.functionId)
        if (mcpItem) {
          await updateMcp(mcp.functionId, { ...values })
        }
      },
    })
  })

  const { handleInstallMcpEnv, installMcpEnvLoading, clientEnvStatus } =
    useEnvInstall()

  const content = useMemo(() => {
    const groupList = [
      {
        label: '来自客户端',
        key: 'client',
        list: mcpList.filter(item => item.source === 'CLIENT'),
        hidden: !window.MindNote,
      },
      {
        label: '来自云端',
        key: 'cloud',
        list: mcpList.filter(item => item.source === 'CLOUD'),
      },
    ]

    const groupListFinal = groupList.filter(
      item => !item.hidden && !!item.list.length,
    )

    const renderMcpIntroduction = (mcp: McpListItem) => {
      if (mcp.state === 'connecting') {
        return (
          <div className='w-288px h-138px flex items-center justify-center'>
            <div className='flex flex-col items-center gap-12px'>
              <Spin />
              <div className='text-14px/14px mt-16px font-400 c-#8D8D99'>
                正在连接...
              </div>
            </div>
          </div>
        )
      } else if (mcp.state === 'failed') {
        return (
          <div className='w-288px h-198px flex items-center justify-center'>
            <div className='flex flex-col items-center'>
              <img
                src={`${basePath}/mcp-connect-error.png`}
                width={80}
                height={80}
                alt='mcp-connect-error'
              />
              <div className='text-14px/16px mt-12px font-400 c-#8D8D99'>
                连接失败，请检查MCP服务是否正常运行
              </div>
              <div className='flex items-center gap-12px mt-16px'>
                <Button onClick={() => handleEditMCP(mcp)}>检查参数</Button>
                <Button
                  type='primary'
                  onClick={() => connectMcp(mcp.functionId)}
                >
                  重新连接
                </Button>
              </div>
            </div>
          </div>
        )
      } else if (mcp.descriptionState === 'pending') {
        return (
          <div className='w-288px h-138px flex items-center justify-center'>
            <div className='flex flex-col items-center'>
              <img
                src={`${basePath}/collection-loading.gif`}
                width={80}
                height={80}
                alt='mcp-description-pending'
              />
              <div className='text-14px/14px mt-12px font-400 c-#8D8D99'>
                介绍分析中...
              </div>
            </div>
          </div>
        )
      } else if (mcp.descriptionState === 'failed') {
        return (
          <div className='w-288px h-198px flex items-center justify-center'>
            <div className='flex flex-col items-center'>
              <img
                src={`${basePath}/failed.png`}
                width={80}
                height={80}
                alt='mcp-description-failed'
              />
              <div className='text-14px/14px mt-12px font-400 c-#8D8D99'>
                介绍生成失败
              </div>
              <Button
                type='primary'
                onClick={() =>
                  generateMcpDescription(mcp.functionId, mcp.tools ?? [])
                }
                className='mt-16px'
              >
                重新生成
              </Button>
            </div>
          </div>
        )
      } else if (mcp.descriptionState === 'success') {
        return (
          <div className='px-16px pb-16px w-288px max-h-258px of-y-auto'>
            <div className='flex items-center gap-6px text-14px/22px font-500'>
              <Icon icon='i-icons-introduction-color' className='size-16px!' />
              <span>工具介绍</span>
            </div>
            <div className='mt-8px c-#17171D/80%'>
              {mcp.description?.introduction ? (
                mcp.description.introduction
              ) : (
                <span className='text-14px/20px c-#8D8D99'>暂无工具介绍</span>
              )}
            </div>
            <div className='flex items-center gap-6px text-14px/22px font-500 mt-24px'>
              <Icon icon='i-icons-setting-color' className='size-16px!' />
              <span>主要功能</span>
            </div>
            <div className='mt-8px'>
              {mcp.description?.tools.length ? (
                mcp.description.tools.map(tool => (
                  <div key={tool.title} className='mt-8px'>
                    <div className='text-14px/22px font-500'>{tool.title}</div>
                    <div className='text-13px/20px mt-6px'>
                      <span className='c-#17171D/80% font-500'>功能说明：</span>
                      <span className='font-400 c-#17171D/80%'>
                        {tool.content}
                      </span>
                    </div>
                  </div>
                ))
              ) : (
                <span className='text-14px/20px c-#8D8D99'>
                  未生成主要功能介绍
                </span>
              )}
            </div>
            <div className='flex items-center gap-6px text-14px/22px font-500 mt-24px'>
              <Icon icon='i-icons-example-color' className='size-16px!' />
              <span>使用示例</span>
            </div>
            <div className='mt-8px'>
              {mcp.description?.examples.length ? (
                mcp.description.examples.map(example => (
                  <div key={example.title} className='mt-8px'>
                    <div className='text-14px/22px font-500'>
                      {example.title}
                    </div>
                    <div className='text-13px/20px mt-6px'>
                      <span className='font-400 c-#17171D/80%'>
                        {example.content}
                      </span>
                    </div>
                  </div>
                ))
              ) : (
                <span className='text-14px/20px c-#8D8D99'>暂无示例</span>
              )}
            </div>
          </div>
        )
      }
    }

    if (isClient && (clientEnvStatus !== 'success' || installMcpEnvLoading)) {
      return (
        <McpEnvReady
          clientEnvStatus={clientEnvStatus}
          installMcpEnvLoading={installMcpEnvLoading}
          onInstallMcpEnv={handleInstallMcpEnv}
        />
      )
    } else {
      return (
        <div className='w-320px relative'>
          {!mcpList.length && (
            <div className='flex flex-col items-center h-full justify-center min-h-202px'>
              <img src='/list-empty.png' className='w-100px h-100px' />
              <div className='text-14px/16px font-400 c-#8D8D99 mt-16px'>
                暂无MCP服务
              </div>
              <a
                className='c-#7B61FF font-500 text-14px/14px mt-8px'
                onClick={handleCreateMCP}
              >
                立即创建
              </a>
            </div>
          )}
          {!!mcpList.length && (
            <div className='flex items-center justify-between pt-8px px-8px'>
              <div className='text-14px/14px font-500 pl-8px'>MCP</div>
              <div className='flex items-center gap-8px'>
                <Tooltip title={localize('mcp.reconnect.tips', '重新连接')}>
                  <span
                    className='size-24px! rd-4px cursor-pointer hover:bg-bg-hover/8 flex-center'
                    onClick={handleReconnect}
                  >
                    <Icon
                      icon='i-icons-retry'
                      className={cn('size-16px!', {
                        'animate-spin': connectLoading,
                      })}
                    />
                  </span>
                </Tooltip>
                <Tooltip title={localize('mcp.add.tips', '创建MCP')}>
                  <IconButton
                    icon='i-icons-add'
                    className='size-24px! rd-4px!'
                    onClick={handleCreateMCP}
                  />
                </Tooltip>
              </div>
            </div>
          )}
          {!!mcpList.length && (
            <div
              className={cn(
                'flex flex-col p-8px gap-4px max-h-315px of-y-auto w-full',
                {
                  'min-h-202px': isClient && clientEnvStatus !== 'success',
                },
              )}
            >
              {groupListFinal.map(item => {
                return (
                  <div
                    key={item.key}
                    className='not-last-of-type:b-b-1 not-last-of-type:b-#E1E1E5/60% not-last-of-type:pb-4px'
                  >
                    {groupListFinal.length > 1 && (
                      <div className='text-12px/12px font-400 c-#8D8D99 px-8px pt-10px pb-4px'>
                        {item.label}
                      </div>
                    )}
                    <div className='flex flex-col gap-4px mt-4px'>
                      {item.list.map(item => {
                        return (
                          <Popover
                            key={item.functionId}
                            overlayInnerStyle={{
                              padding: 0,
                            }}
                            placement='right'
                            content={
                              <div>
                                <div className='pt-16px px-16px text-14px/22px font-500'>
                                  {item.name}
                                </div>
                                <div className='mt-8px'>
                                  {renderMcpIntroduction(item)}
                                </div>
                              </div>
                            }
                          >
                            <div
                              className='group flex items-center justify-between py-9px pr-12px pl-8px hover:bg-#626999/8% rd-8px cursor-pointer of-hidden'
                              onClick={() => toMcpPage(item.functionId)}
                            >
                              <div className='flex items-center of-hidden'>
                                {item.state !== 'connecting' && (
                                  <span
                                    className='shrink-0 w-6px h-6px inline-block rd-full'
                                    style={{
                                      backgroundColor: COLOR_MAP[item.state],
                                    }}
                                  ></span>
                                )}
                                {item.state === 'connecting' && (
                                  <Image
                                    src={`${basePath}/ask-think-loading.png`}
                                    width={12}
                                    className='animate-spin'
                                  />
                                )}
                                <Tooltip title={item.name}>
                                  <span className='ml-8px truncate text-14px/16px font-400'>
                                    {item.name}
                                  </span>
                                </Tooltip>
                                {item.state === 'connecting' && (
                                  <span className='shrink-0 text-12px/12px font-400 c-#8D8D99 b-1 b-#E1E1E5/60% bg-#626999/4% rd-4px px-4px py-2px ml-4px'>
                                    <span className='ml-2px'>
                                      {localize('mcp.connecting', '连接中')}
                                    </span>
                                  </span>
                                )}
                                {item.state === 'success' && (
                                  <span className='shrink-0 text-12px/12px font-400 c-#8D8D99 b-1 b-#E1E1E5/60% bg-#626999/4% rd-4px px-4px py-2px ml-4px'>
                                    {item.tools.length}
                                    <span className='ml-2px'>
                                      {localize('mcp.tools', '工具')}
                                    </span>
                                  </span>
                                )}
                                {item.state === 'failed' && (
                                  <span className='shrink-0 text-12px/12px font-400 c-#FF512B b-1 b-#FF512B/20% bg-#FF512B/8% rd-4px px-4px py-2px ml-4px'>
                                    <span className='ml-2px'>
                                      {localize('mcp.failed', '异常')}
                                    </span>
                                  </span>
                                )}
                              </div>
                              <div className='shrink-0 flex items-center ml-8px'>
                                <span
                                  className='text-14px/16px font-400 c-#7B61FF cursor-pointer invisible group-hover:visible'
                                  onClick={() => toMcpPage(item.functionId)}
                                >
                                  详情
                                </span>
                                <div onClick={e => e.stopPropagation()}>
                                  <Switch
                                    size='small'
                                    className='ml-8px'
                                    checked={enabledMCPIds.includes(
                                      item.functionId,
                                    )}
                                    loading={loadingMCPIds.has(item.functionId)}
                                    onChange={async checked => {
                                      setLoadingMCPIds(prev =>
                                        new Set(prev).add(item.functionId),
                                      )
                                      try {
                                        await onEnable(item.functionId, checked)
                                      } finally {
                                        setLoadingMCPIds(prev => {
                                          const newSet = new Set(prev)
                                          newSet.delete(item.functionId)
                                          return newSet
                                        })
                                      }
                                    }}
                                    disabled={item.state !== 'success'}
                                  />
                                </div>
                              </div>
                            </div>
                          </Popover>
                        )
                      })}
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </div>
      )
    }
  }, [
    mcpList,
    connectLoading,
    handleReconnect,
    clientEnvStatus,
    installMcpEnvLoading,
    handleInstallMcpEnv,
    enabledMCPIds,
    onEnable,
    loadingMCPIds,
    setLoadingMCPIds,
  ])

  const renderMCPSuffix = () => {
    const commonClassName =
      'p-4px b-1 b-solid rd-4px text-12px/12px font-400 flex items-center gap-2px'
    if (isClient && clientEnvStatus !== 'success') {
      return (
        <McpEnvStatus
          clientEnvStatus={clientEnvStatus}
          installMcpEnvLoading={installMcpEnvLoading}
        />
      )
    } else {
      const connectMcpCount = mcpList.filter(
        item => item.state === 'connecting',
      ).length
      const isConnecting = connectMcpCount > 0
      if (isConnecting) {
        return (
          <div className={cn(commonClassName, 'b-#8D8D99/20% bg-#F6F6F7')}>
            <Image
              src={`${basePath}/ask-think-loading.png`}
              width={12}
              className='animate-spin'
            />
            <span className='text-12px/12px font-400 c-#8D8D99'>
              连接中...{mcpList.length - connectMcpCount}/{mcpList.length}
            </span>
          </div>
        )
      }
    }
  }

  return (
    <div className='flex items-center justify-between'>
      <Popover
        open={mcpListOpen}
        onOpenChange={setMcpListOpen}
        trigger='click'
        placement='bottomLeft'
        arrow={false}
        overlayInnerStyle={{
          padding: 0,
          borderRadius: 8,
          overflow: 'hidden',
        }}
        content={content}
      >
        <div
          className={cn(
            'flex gap-6px items-center py-4px pl-12px pr-5px gap-4px b-1 b-#E1E1E5/80% rd-8px cursor-pointer hover:bg-#626999/8% transition-all',
            {
              'bg-#626999/8%': mcpListOpen,
              'b-1 b-#7B61FF/40%': enabledMCPIds.length > 0,
              'bg-#7B61FF/16%!': enabledMCPIds.length > 0,
            },
          )}
        >
          <Icon icon='i-icons-mcp' size='size-14px' className='text-#7B61FF' />
          <div
            className={cn({
              'text-#7B61FF': enabledMCPIds.length > 0,
            })}
          >
            <span>MCP</span>
            {enabledMCPIds.length > 0 && <span>({enabledMCPIds.length})</span>}
          </div>
          {renderMCPSuffix()}
          <Icon
            icon='i-icons-arrow'
            className={cn(
              'text-#9E9E9E transition-all duration-150 ease-in-out ml--2px',
              {
                'rotate-180': mcpListOpen,
              },
            )}
          />
        </div>
      </Popover>
    </div>
  )
})
