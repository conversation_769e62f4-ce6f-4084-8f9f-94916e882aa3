import { syncMCPTools } from '@apis/mindnote/mcp'
import type { McpTool } from '@bty/global-types/mcp'
import { useMemoizedFn } from 'ahooks'
import { useMcpStore } from '@/store/mcp'
import {
  CLIENT_TOOL_PREFIX,
  CLOUD_TOOL_PREFIX,
} from '@/components/mcp-chat/constant'

export function useMcpEvent() {
  const mcpList = useMcpStore(state => state.list)

  const getMcpToolsMcp = useMemoizedFn(() => {
    const result: Record<string, Array<Omit<McpTool, 'inputSchema'>>> = {}
    mcpList.forEach(mcp => {
      const prefix =
        mcp.source === 'CLOUD' ? CLOUD_TOOL_PREFIX : CLIENT_TOOL_PREFIX

      if (mcp.state === 'success' && mcp.tools.length > 0) {
        result[mcp.functionId] = mcp.tools.map(tool => {
          return {
            name: `${prefix}--${mcp.code}__${tool.name}`,
            parameters: tool.inputSchema,
            description: tool.description,
          }
        })
      }
    })
    return result
  })

  // 同步mcp工具列表到服务端，不在MCP变化的订阅中同步是因为服务端将这个数据存储在redis中
  // 如果服务器重启会导致数据丢失，客户端和服务端数据不同步
  const syncMcpTools = useMemoizedFn(async () => {
    const toolsMap = getMcpToolsMcp()

    //  TODO 暂时不判断是否需要同步，每次都同步，因为判断需要深度对比tool，isAutoExecute，enable等等，耗时久，后续优化时斟酌方案
    // const serverTools = (await getServerMcpTools()) ?? []
    // if (serverTools.length !== tools.length) {
    //   await setServerMcpTools(tools)
    // }

    await syncMCPTools(toolsMap)
    return 'ok'
  })

  const findMcp = useMemoizedFn((functionId: string) => {
    return mcpList.find(mcp => mcp.functionId === functionId)
  })

  return {
    syncMcpTools,
    findMcp,
  }
}
