import { getMcpTemplateList } from '@apis/mindnote/mcp'
import type { McpConfig } from '@bty/global-types/mcp'
import { useRequest } from 'ahooks'

export function useExampleList() {
  const isClient = !!window.MindNote

  const { data: exampleList = [] } = useRequest(async () => {
    const templateList = await getMcpTemplateList(isClient ? 'CLIENT' : 'CLOUD')
    return templateList.map(template => {
      let env = ''
      if (template.config.env) {
        env = Object.entries(template.config.env)
          .map(([key, value]) => `${key}=${value}`)
          .join(' ')
      }
      return {
        name: template.name,
        description: template.description,
        code: template.code,
        type: template.config.url ? 'sse' : 'command',
        command: template.config.command ?? '',
        args: template.config.args?.join(' ') ?? '',
        auto_execute: template.is_auto_executed,
        is_enable: true,
        need_key: template.is_key_required,
        is_self_developed: template.is_self_developed,
        env,
      } as McpConfig & { description: string; need_key: boolean }
    })
  })

  return {
    exampleList,
  }
}
