import { memo } from 'react'
import { <PERSON><PERSON>, Spin, Image } from 'antd'
import { localize } from '@bty/localize'
import { basePath } from '@/const'

interface McpEnvReadyProps {
  clientEnvStatus: 'checking' | 'failed' | 'success'
  installMcpEnvLoading: boolean
  onInstallMcpEnv: () => void
}

export const McpEnvReady = memo<McpEnvReadyProps>(props => {
  const { clientEnvStatus, installMcpEnvLoading, onInstallMcpEnv } = props

  if (clientEnvStatus === 'checking') {
    return (
      <div className='w-320px h-228px flex flex-col items-center justify-center'>
        <Spin />
        <div className='text-14px/16px font-400 c-#8D8D99/80% mt-24px'>
          {localize('mcp.env.checking', '正在检测MCP环境...')}
        </div>
      </div>
    )
  } else if (installMcpEnvLoading) {
    return (
      <div className='w-320px h-228px flex flex-col items-center justify-center'>
        <Spin />
        <div className='text-14px/16px font-400 c-#8D8D99/80% mt-24px'>
          {localize('mcp.env.installing', '正在安装MCP环境...')}
        </div>
      </div>
    )
  } else if (clientEnvStatus === 'failed') {
    return (
      <div className='w-320px h-228px flex items-center justify-center'>
        <div className='flex flex-col items-center'>
          <Image
            src={`${basePath}/mcp-env-error.png`}
            width={100}
            height={100}
          />
          <div className='text-14px/16px font-400 c-#8D8D99/80% mt-16px'>
            {localize('mcp.env.error', '未检测到MCP环境，请先安装MCP环境')}
          </div>
          <Button
            className='mt-16px'
            onClick={onInstallMcpEnv}
            loading={installMcpEnvLoading}
            type='primary'
          >
            {localize('mcp.env.install', '立即安装')}
          </Button>
        </div>
      </div>
    )
  }
})
