import { memo } from 'react'
import { ThinkTask } from '@bty/chat/ui'
import { Tooltip, message as AntdMessage } from 'antd'
import { useMemoizedFn } from 'ahooks'
import { Markdown } from '@bty/components'
import { Icon } from '../base/icon'
import { disconnectMcp, runMcpTool } from '../mcp-chat/utils/mcp'
import type { UserMessage } from '../chat/types/message'
import { RelatedQuestion } from '../chat/related-question'
import { useMcpStore } from '@/store/mcp'
import { useMcpEvent } from '../mcp/hooks/useMcpEvent'
import type {
  AssistantMessage as AssistantMessageType,
  McpFunctionRecord,
  McpRecord,
} from './types/message'
import { useChat } from './provider/ChatProvider'
import { useChatEvent } from './provider/ChatEventProvider'
import { useMcpEvent } from '../mcp/hooks/useMcpEvent'

interface AssistantMessageProps {
  message: AssistantMessageType
  isLastMessage: boolean
  userMessage: UserMessage
}

const STATUS_MAP = {
  ready: '等待确认运行',
  running: '正在运行',
  success: '已运行',
  failed: '已运行',
}

export const AssistantMessage = memo<AssistantMessageProps>(props => {
  const isClient = !!window.MindNote
  const { message, isLastMessage, userMessage } = props

  const mcpList = useMcpStore(state => state.list)
  const {
    sendMessage,
    messageInputActionRef,
    messageInputRef,
    updateMessage,
    abortMessage,
    isStopRequestRef,
  } = useChat()
  const { onMcpToolClick } = useChatEvent()
  const { findMcp } = useMcpEvent()

  const onCopy = () => {
    navigator.clipboard.writeText(message.content.replaceAll(/\[\w+\]/g, ''))
    AntdMessage.success('复制成功')
  }

  const reAsk = () => {
    if (userMessage?.content) {
      messageInputActionRef.current?.setContent(userMessage?.content)
      messageInputRef.current?.focus()
    }
  }

  const updateToolInfo = useMemoizedFn(
    (task_id: string, record: Partial<McpRecord>) => {
      const newMessage = { ...message }
      newMessage.records = newMessage.records.map(item => {
        if (item.task_id === task_id && item.type === 'tool') {
          return { ...item, ...record } as McpRecord
        }
        return item
      })
      updateMessage(message.id, newMessage)
    },
  )

  const onToolRun = useMemoizedFn(async (record: McpFunctionRecord) => {
    if (record.toolInfo.source === 'CLIENT' && !isClient) {
      AntdMessage.warning('请在客户端运行该工具')
      return
    }

    if (record.status === 'running') {
      AntdMessage.warning('工具正在运行，请勿重复运行')
      return
    }

    if (message.isFetching || message.isStreaming) {
      AntdMessage.warning('请等待当前工具执行完成后再运行')
      return
    }

    isStopRequestRef.current = false

    updateToolInfo(record.task_id, {
      status: 'running',
    })

    const mcp = mcpList.find(
      item => item.functionId === record.toolInfo.functionId,
    )
    if (!mcp) {
      AntdMessage.warning('工具调用异常，未找到该工具运行实例')
      return
    }
    const res = await runMcpTool(
      mcp,
      record.toolInfo.toolName,
      record.toolInfo.arguments,
    )
    if (!isStopRequestRef.current) {
      updateToolInfo(record.task_id, {
        status: res.status === 'SUCCEEDED' ? 'success' : 'failed',
        result: res.data,
      })
      sendMessage({
        messageType: 'mcp_tool',
        event_id: record.event_id,
        task_id: record.task_id,
        user_action: 'execution_tool_completed',
        action_result: res,
      })
    }
  })

  const onToolRetry = useMemoizedFn(async (record: McpFunctionRecord) => {
    if (record.toolInfo.source === 'CLIENT' && !isClient) {
      AntdMessage.warning('请在客户端运行该工具')
      return
    }

    if (record.status === 'running') {
      AntdMessage.warning('工具正在运行，请勿重复运行')
      return
    }

    if (message.isFetching || message.isStreaming) {
      AntdMessage.warning('请等待当前工具执行完成后再运行')
      return
    }

    isStopRequestRef.current = false

    const newMessage = { ...message }
    const currentRecordIndex = newMessage.records.findIndex(
      item => item.task_id === record.task_id && item.type === 'tool',
    )
    if (currentRecordIndex === -1) {
      return
    }
    const mcp = findMcp(record.toolInfo.functionId)
    if (!mcp) {
      AntdMessage.warning('工具调用异常，工具不存在')
      return
    }

    ;(newMessage.records[currentRecordIndex] as McpFunctionRecord).status =
      'running'

    const newRecords = newMessage.records.slice(0, currentRecordIndex + 1)
    newMessage.records = [...newRecords]
    newMessage.relatedQuestion = undefined
    newMessage.status = 'success'
    newMessage.failMessage = ''
    newMessage.finishReason = 'success'

    updateMessage(message.id, newMessage)
    const res = await runMcpTool(
      mcp,
      record.toolInfo.toolName,
      record.toolInfo.arguments,
    )
    if (!isStopRequestRef.current) {
      updateToolInfo(record.task_id, {
        status: res.status === 'SUCCEEDED' ? 'success' : 'failed',
        result: res.data,
        failReason: res.status === 'FAILED' ? res.data : undefined,
      })
      sendMessage({
        messageType: 'mcp_tool',
        event_id: record.event_id,
        task_id: record.task_id,
        user_action: 're_execute',
        action_result: res,
      })
    }
  })

  const handleShowToolLog = useMemoizedFn((record: McpFunctionRecord) => {
    onMcpToolClick?.(message.id, record.task_id)
  })

  const onStopTool = useMemoizedFn((record: McpFunctionRecord) => {
    updateToolInfo(record.task_id, {
      status: 'ready',
      result: [],
    })
    const mcp = findMcp(record.toolInfo.functionId)
    if (mcp && mcp.type === 'command') {
      disconnectMcp(mcp)
    }
    abortMessage()
  })

  const contentRender = (record: McpRecord) => {
    if (record.type === 'tool') {
      return (
        <>
          {!!record.reasoning?.trim() && (
            <div className='mb-9px'>
              <ThinkTask
                thinkMessage={{
                  content: record.reasoning,
                  status: 'SUCCEEDED',
                }}
              />
            </div>
          )}
          {!!record.summary?.trim() && (
            <Markdown
              overrides={{ root: '[&>:last-child]:!mb-0' }}
              content={record.summary}
            />
          )}
          <div
            className='py-4px px-6px flex items-center bg-#626999/8% rd-10px w-fit my-9px hover:bg-#626999/12% cursor-pointer'
            onClick={() => handleShowToolLog(record)}
          >
            {record.status === 'ready' && (
              <Icon icon='i-icons-tool' className='size-12px! c-#8D8D99' />
            )}
            {record.status === 'running' && (
              <Icon
                icon='i-icons-loading'
                className='size-14px! animate-spin'
              />
            )}
            {record.status === 'success' && (
              <Icon icon='i-icons-run-success' className='size-14px!' />
            )}
            {record.status === 'failed' && (
              <Icon icon='i-icons-run-failed' className='size-14px!' />
            )}
            <span className='text-14px/22px c-#8D8D99 ml-5px'>
              {STATUS_MAP[record.status]}
            </span>
            <span className='text-14px/22px ml-5px'>
              {record.toolInfo.name}&nbsp;/&nbsp;{record.toolInfo.toolName}
            </span>
            {record.status === 'ready' && (
              <Tooltip placement='top' title='开始运行'>
                <span
                  className='ml-5px cursor-pointer size-22px hover:bg-white/60% rd-4px flex items-center justify-center'
                  onClick={e => {
                    e.stopPropagation()
                    onToolRun(record)
                  }}
                >
                  <Icon icon='i-icons-run' className='size-16px c-primary' />
                </span>
              </Tooltip>
            )}
            {['success', 'failed'].includes(record.status) && (
              <Tooltip placement='top' title='重新运行'>
                <span
                  className='ml-5px cursor-pointer size-22px hover:bg-white/60% rd-4px flex items-center justify-center'
                  onClick={e => {
                    e.stopPropagation()
                    onToolRetry(record)
                  }}
                >
                  <Icon icon='i-icons-reload' className='size-14px!' />
                </span>
              </Tooltip>
            )}
            {record.status === 'running' && (
              <Tooltip placement='top' title='中止运行'>
                <span
                  className='ml-5px cursor-pointer size-22px hover:bg-white/60% rd-4px flex items-center justify-center'
                  onClick={e => {
                    e.stopPropagation()
                    onStopTool(record)
                  }}
                >
                  <Icon icon='i-icons-stop' className='size-14px!' />
                </span>
              </Tooltip>
            )}
          </div>
        </>
      )
    }

    if (record.type === 'thinking') {
      return (
        <div className='mb-9px'>
          <ThinkTask
            thinkMessage={{
              content: record.content,
              status: record.isStreaming ? 'PENDING' : 'SUCCEEDED',
            }}
          />
        </div>
      )
    }

    if (record.type === 'content') {
      return <Markdown content={record.content} />
    }

    return null
  }

  return (
    <div className='group [&_ol:last-child]:mb-0'>
      {message.records.map(record => {
        console.log('====== record', record)
        return (
          <div key={`${message.id}-${record.type}-${record.task_id}`}>
            {contentRender(record)}
          </div>
        )
      })}
      {message.status === 'failed' && (
        <div className='w-fit mt-8px p-12px rd-8px bg-#99310f/8% text-12px/14px text-[#99310f]'>
          {message.failMessage}
        </div>
      )}

      {!message.isFetching &&
        !message.isStreaming &&
        !!message.content &&
        message.status === 'success' && (
          <div className='group-hover:visible mt-8px'>
            <Tooltip placement='top' title='复制'>
              <div
                className='w-24px h-24px rd-4px flex items-center justify-center hover:bg-#626999/8%'
                onClick={onCopy}
              >
                <Icon
                  icon='i-icons-copy'
                  className='text-#626999/60% cursor-pointer'
                />
              </div>
            </Tooltip>
          </div>
        )}

      {message.isFetching && <MessageLoading />}

      {message.finishReason === 'cancelled' &&
        (isLastMessage ? (
          <p className='text-12px/18px mt-8px text-#8D8D99 flex-center w-full'>
            你已停止生成本次回答
            <span
              className='text-#7b61ff cursor-pointer ml-8px'
              onClick={reAsk}
            >
              重新编辑问题
            </span>
          </p>
        ) : (
          <p className='text-12px/18px mt-8px text-#8D8D99 flex-center w-full'>
            （用户手动停止）
          </p>
        ))}

      {isLastMessage && message.relatedQuestion && (
        <div className='mt-8px'>
          <RelatedQuestion
            loading={message.relatedQuestion.status === 'started'}
            questions={message.relatedQuestion.content}
            onSend={(q: string) =>
              sendMessage?.({ content: q, messageType: 'input_message' })
            }
          />
        </div>
      )}
    </div>
  )
})

export function MessageLoading() {
  return (
    <img
      className='ui-w-[18px] ui-h-[18px]'
      src='https://resource.bantouyan.com/battleyeah-ai/agent/loading.gif'
      alt='loading'
    />
  )
}
