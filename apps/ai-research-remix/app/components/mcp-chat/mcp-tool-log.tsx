import { memo, useMemo } from 'react'
import { Markdown } from '@bty/components'
import { Icon } from '../base/icon'
import { basePath } from '@/const'
import { useChat } from './provider/ChatProvider'
import type { AssistantMessage, McpFunctionRecord } from './types/message'

interface McpToolLogProps {
  messageId: string
  taskId: string
  onClose: () => void
}

export const McpToolLog = memo<McpToolLogProps>(props => {
  const { messageId, taskId, onClose } = props
  const { messageList } = useChat()

  const recordInfo = useMemo(() => {
    const message = messageList.find(
      item => item.id === messageId,
    ) as AssistantMessage

    if (!message) {
      return null
    }

    const toolInfo = message.records.find(
      item => item.type === 'tool' && item.task_id === taskId,
    )

    if (!toolInfo) {
      return null
    }

    return toolInfo as McpFunctionRecord
  }, [messageList, taskId, messageId])

  return (
    <div className='w-full h-full p-24px of-hidden'>
      <div className='w-full h-full rd-12px b-1 b-#E1E1E5/80% flex flex-col'>
        {!recordInfo ? (
          <div className='flex w-full h-full items-center justify-center'>
            <div className='text-14px text-#999'>暂无该工具调用日志</div>
          </div>
        ) : (
          <>
            <div className='shrink-0 flex h-50px items-center justify-between px-24px'>
              <div className='text-16px/24px font-500'>
                {recordInfo.toolInfo.name}
              </div>
              <span
                className='cursor-pointer size-32px rd-4px flex items-center justify-center hover:bg-#626999/8%'
                onClick={onClose}
              >
                <Icon icon='i-icons-exit-panel' className='size-16px' />
              </span>
            </div>
            <div className='h-full of-y-auto pt-12px pb-24px px-24px'>
              <div>
                <div className='text-16px/24px font-500'>
                  {recordInfo.toolInfo.toolName}
                </div>
                <div className='text-14px/22px c-#8D8D99 font-400 mt-8px'>
                  {recordInfo.toolInfo.toolDescription}
                </div>
              </div>
              <div className='mt-24px b-t-1 b-#E1E1E5/60%'>
                {recordInfo.status === 'ready' && (
                  <div className='px-12px py-8px flex flex-col items-center pt-100px'>
                    <img src={`${basePath}/404.png`} className='size-100px' />
                    <div className='text-14px/22px c-#8D8D99 font-400 mt-12px'>
                      工具未运行，请点击“运行”按钮运行
                    </div>
                  </div>
                )}
                {recordInfo.status !== 'ready' && (
                  <>
                    <div className='pt-24px'>
                      <div className='text-14px font-500'>参数</div>
                      <div className='bg-#626999/8% rd-8px font-400 text-14px/22px mt-12px'>
                        {recordInfo.toolInfo.arguments ? (
                          <pre className='px-12px py-8px '>
                            <code>
                              {JSON.stringify(
                                recordInfo.toolInfo.arguments,
                                null,
                                2,
                              )}
                            </code>
                          </pre>
                        ) : (
                          <div className='px-12px py-8px'>无参数</div>
                        )}
                      </div>
                    </div>
                    <div className='pt-24px'>
                      <div className='text-14px font-500'>结果</div>
                      {recordInfo.status === 'running' && (
                        <div className='flex items-center flex-col h-300px justify-center'>
                          <img
                            src={`${basePath}/collection-loading.gif`}
                            className='size-100px'
                          />
                          <div className='text-14px/22px c-#8D8D99 font-400 mt-12px'>
                            运行中...
                          </div>
                        </div>
                      )}
                      {recordInfo.status === 'failed' && (
                        <div className='bg-#FFF1ED b-1 b-#FF5219/12% c-#FF5219 px-12px py-11px mt-12px gap-8px rd-8px'>
                          <div className='flex items-center gap-8px'>
                            <Icon
                              icon='i-icons-run-failed'
                              className='size-16px'
                            />
                            <span className='text-14px/16px font-500'>
                              运行失败
                            </span>
                          </div>
                          <div className='b-t-1 b-#E1E1E5/40% mt-12px pt-8px font-400 text-14px/22px'>
                            {recordInfo.failReason ?? '未知的失败原因'}
                          </div>
                        </div>
                      )}
                      {recordInfo.status === 'success' && (
                        <>
                          <div className='bg-#EFFAF3 b-1 b-#2CB969/12% c-#2CB969 px-12px py-11px mt-12px flex items-center gap-8px rd-8px'>
                            <Icon
                              icon='i-icons-run-success'
                              className='size-16px'
                            />
                            <span className='text-14px/16px font-500'>
                              运行成功
                            </span>
                          </div>
                          <div className='bg-#626999/8% rd-8px font-400 text-14px/22px mt-12px'>
                            {recordInfo.result.length ? (
                              <div className='px-12px py-8px'>
                                {recordInfo.result.map((item, index) => {
                                  return (
                                    <div
                                      key={`${item.type}-${recordInfo.task_id}-${index}`}
                                    >
                                      {item.type === 'text' && (
                                        <Markdown>{item.text}</Markdown>
                                      )}
                                      {item.type === 'image' && (
                                        <img
                                          src={
                                            item.data.startsWith('http')
                                              ? item.data
                                              : `data:${item.mimeType ?? 'image/png'};base64,${item.data}`
                                          }
                                          alt={item.data}
                                          title={item.data}
                                          className='w-80% h-auto mx-auto'
                                        />
                                      )}
                                    </div>
                                  )
                                })}
                              </div>
                            ) : (
                              <div className='px-12px py-8px'>无结果</div>
                            )}
                          </div>
                        </>
                      )}
                    </div>
                  </>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
})
