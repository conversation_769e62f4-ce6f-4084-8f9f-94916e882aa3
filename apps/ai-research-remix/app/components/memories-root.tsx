import { memo, useEffect } from 'react'
import { shallow } from 'zustand/shallow'
import { basePath } from '@/const'
import { useMemories } from '@/store/memories'
import { MemoryList } from './memories/memory-list'
import { MemoryEditor } from './memories/memory-editor'
import { MemoriesEmpty } from './memories/memories-empty'
import { Image } from './base/image'

function InnerMemoriesRoot() {
  const { memories, getMemories } = useMemories(
    state => ({
      memories: state.memories,
      getMemories: state.getMemories,
    }),
    shallow,
  )

  useEffect(() => {
    getMemories()
  }, [])

  if (memories === undefined) {
    return (
      <div className='flex-1 flex justify-center items-center bg-white relative of-hidden'>
        <Image
          className='w-80px object-contain'
          src={`${basePath}/collection-loading.gif`}
        />
      </div>
    )
  }

  if (memories.length === 0) {
    return (
      <div className='flex-1 flex bg-white relative of-hidden'>
        <MemoriesEmpty />
      </div>
    )
  }

  return (
    <div className='flex-1 h-full flex overflow-hidden'>
      <MemoryList />
      <MemoryEditor />
    </div>
  )
}

export const MemoriesRoot = memo(InnerMemoriesRoot)
