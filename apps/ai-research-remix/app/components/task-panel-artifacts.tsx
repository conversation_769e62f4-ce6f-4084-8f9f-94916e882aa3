import React, { useCallback, useEffect, useRef, useState } from 'react'
import { cn } from '@bty/util'
import { Markdown, Palette, Preview } from '@bty/components'
import { getArtifacts, getFileUrl } from '@apis/mindnote/next-agent-chat'
import { message } from 'antd'
import type { TaskArtifact } from '@apis/mindnote/next-agent-chat.type'
import {
  useTask,
  useTaskArtifact,
  useTaskArtifactFront,
  useTaskArtifactList,
} from '@/store/task-event'
import { IconButton } from './base/icon'
import { Action, TaskOutputLayout } from './next-agent/task/task-output-layout'
import { TaskLoading } from './next-agent/task/task-loading'
import { TaskArtifactList } from './next-agent/task/task-artifact-list'
import { supportedLangs } from './next-agent/task/task-code'
import { Image } from './base/image'
import { Html } from './next-agent/task/task-html'

export const TaskPanelArtifacts = React.memo(() => {
  const taskId = useTask()

  const { add, open, close, clear } = useTaskArtifact()

  const artifacts = useTaskArtifactList()

  const frontArtifact = useTaskArtifactFront()

  const [isSliding, setIsSliding] = useState(!!frontArtifact)

  const [url, setUrl] = useState<string>()

  useEffect(() => {
    clear()
    getArtifacts(taskId!).then(artifacts => {
      add(...artifacts)
    })
  }, [])

  useEffect(() => {
    setUrl(undefined)
    setIsSliding(false)

    // 目前只有 handleBackToList 置空 frontArtifact
    if (!frontArtifact) {
      return
    }

    setIsSliding(true)
    getFileUrl(taskId!, frontArtifact.path).then(setUrl)
  }, [frontArtifact])

  const handleFileClick = useCallback((artifact: TaskArtifact) => {
    open(artifact)
    setIsSliding(true)
  }, [])

  const handleBackToList = () => {
    setIsSliding(false)
    // 等待动画结束后清空选中文件
    setTimeout(close, 300)
  }

  const ref = useRef<{ getContent: () => string | undefined }>(null)

  const handleAction = async (action: string) => {
    if (url && action === Action.Download) {
      const response = await fetch(url)
      const blob = await response.blob()
      const blobUrl = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = blobUrl
      link.download = frontArtifact?.file_name || '未知文件'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(blobUrl)
      return
    }

    if (action === Action.Copy) {
      const content = ref.current?.getContent()
      navigator.clipboard.writeText(content || '')
      message.success('复制成功')
    }
  }

  // todo: rerender
  let copyable = true
  let children

  if (!url) {
    children = (
      <div className='p-24px h-[calc(100%-44px)] overflow-y-auto'>
        <TaskLoading className='h-full flex flex-col justify-center' />
      </div>
    )
  } else {
    switch (frontArtifact?.file_type) {
      case 'md':
        children = (
          <div className='p-24px h-[calc(100%-44px)] overflow-y-auto'>
            <Markdown ref={ref} url={url!} remote />
          </div>
        )
        break
      case 'html':
        children = (
          <Html
            className='h-[calc(100%-44px)]'
            taskId={taskId!}
            path={frontArtifact.path}
          />
        )
        copyable = false
        break
      case 'csv':
        children = (
          <div className='p-16px h-[calc(100%-44px)] overflow-y-auto'>
            <Preview.CSV
              overrides={{ root: 'bg-white rounded-10px' }}
              ref={ref}
              url={url!}
            />
          </div>
        )
        break
      case 'png':
        children = (
          <div className='p-16px h-[calc(100%-44px)] overflow-y-auto'>
            <Image className='object-contain rounded-10px' src={url} />
          </div>
        )
        copyable = false
        break
      case 'pdf':
        children = (
          <div className='p-16px h-[calc(100%-44px)] overflow-y-auto'>
            <Preview.VirtualPdf url={url!} />
          </div>
        )
        copyable = false
        break
      default:
        if (supportedLangs.includes(frontArtifact?.file_type || '')) {
          children = (
            <div className='p-16px h-[calc(100%-44px)] overflow-y-auto'>
              <Palette
                ref={ref}
                url={url!}
                lang={frontArtifact!.file_type}
                remote
              />
            </div>
          )
        } else {
          children = (
            <div className='p-16px h-[calc(100%-44px)] overflow-y-auto'>
              <p className='text-center text-#8d8d99 mt-100px'>
                此文件类型暂不支持预览
              </p>
            </div>
          )
          copyable = false
        }
        break
    }
  }

  return (
    <div className='h-full bg-#f3f3f5 rounded-12px relative overflow-hidden'>
      <div className='p-16px max-h-full overflow-y-auto'>
        <TaskArtifactList artifacts={artifacts} onClick={handleFileClick} />
      </div>
      <TaskOutputLayout
        className={cn(
          'bg-#f3f3f5 absolute inset-0 transition-transform duration-300 ease-in-out pb-6px',
          isSliding ? 'translate-x-0' : 'translate-x-full',
        )}
        startContent={
          <IconButton
            className='text-#8d8d99 shrink-0'
            icon='i-icons-arrow-2'
            iconSize='size-14px'
            onClick={handleBackToList}
          />
        }
        title={frontArtifact?.file_name}
        actions={copyable ? [Action.Download, Action.Copy] : [Action.Download]}
        onAction={handleAction}
      >
        {children}
      </TaskOutputLayout>
    </div>
  )
})
