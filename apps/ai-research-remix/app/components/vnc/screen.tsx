import { useRef, useCallback, useState } from 'react'
import type { VncScreenHandle } from 'react-vnc'
import { VncScreen } from 'react-vnc'
import copy from 'copy-to-clipboard'
import { Button, Spin } from 'antd'

interface ScreenProps {
  ws: string
  viewOnly?: boolean
  control: string
  password: string
}

function decodeText(str: string) {
  const bytes = new Uint8Array(str.length)
  for (let i = 0; i < str.length; i++) {
    bytes[i] = str.charCodeAt(i)
  }
  const decoder = new TextDecoder('utf-8')
  return decoder.decode(bytes)
}

export function sendControl(address: string, key: string, value: string) {
  fetch(address, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      command: key,
      value,
    }),
  })
}

export function Screen(props: ScreenProps) {
  const { ws, password, control, viewOnly } = props

  const ref = useRef<VncScreenHandle>(null)
  const [isConnected, setIsConnected] = useState(false)
  const clipboardCheckInterval = useRef<any>()
  const clipboardContentRef = useRef('')

  const handleCopy = useCallback((event: CustomEvent) => {
    copy(decodeText(event.detail.text))
  }, [])

  const checkClipboard = useCallback(async () => {
    if (!document.hasFocus() || !ref.current?.rfb) return
    try {
      const text = await navigator.clipboard.readText()
      if (text !== clipboardContentRef.current) {
        clipboardContentRef.current = text
        ref.current.rfb.clipboardPasteFrom(text)
      }
    } catch (err) {}
  }, [])

  const handleChangeInputMethod = useCallback(() => {
    sendControl(control, 'key', 'Control_L+space')
    // ref.current?.sendKey(65507, 'ControlLeft', true)
    // ref.current?.sendKey(32, 'Space', true)
    // ref.current?.sendKey(32, 'Space', false)
    // ref.current?.sendKey(65507, 'ControlLeft', false)
  }, [])

  const handleConnect = useCallback(() => {
    setIsConnected(true)
    setTimeout(() => {
      const oldKeyEvent = ref.current?.rfb._keyboard.onkeyevent
      // MAC 下，Command 键映射为 Control 键
      ref.current!.rfb._keyboard.onkeyevent = (...args: any[]) => {
        if (!clipboardCheckInterval.current) {
          checkClipboard()
          clipboardCheckInterval.current = setInterval(checkClipboard, 1000)
        }

        if (args[0] === 65513 && args[1] === 'MetaLeft' && args[2] === true) {
          ref.current!.sendKey(65507, 'ControlLeft', true)
          return
        }
        if (args[0] === 65513 && args[1] === 'MetaLeft' && args[2] === false) {
          ref.current!.sendKey(65507, 'ControlLeft', false)
          return
        }
        if (args[0] === 65515 && args[1] === 'MetaRight' && args[2] === true) {
          ref.current!.sendKey(65508, 'ControlRight', true)
          return
        }
        if (args[0] === 65515 && args[1] === 'MetaRight' && args[2] === false) {
          ref.current!.sendKey(65508, 'ControlRight', false)
          return
        }
        oldKeyEvent?.(...args)
      }
    }, 1000)
  }, [checkClipboard])

  const handleDisconnect = useCallback(() => {
    clearInterval(clipboardCheckInterval.current)
  }, [])

  return (
    <div className='size-full relative'>
      {!viewOnly && (
        <Button
          className='absolute bottom-10px right-10px'
          size='small'
          onClick={handleChangeInputMethod}
        >
          切换输入法
        </Button>
      )}
      {!isConnected && (
        <Spin
          className='absolute top-0 left-0 size-full flex flex-center'
          spinning={!isConnected}
        />
      )}
      <VncScreen
        url={ws}
        viewOnly={viewOnly}
        scaleViewport
        background='transparent'
        style={{
          width: '100%',
          height: '100%',
        }}
        rfbOptions={{
          credentials: {
            password,
          },
        }}
        ref={ref}
        onClipboard={handleCopy}
        onConnect={handleConnect}
        onDisconnect={handleDisconnect}
      />
    </div>
  )
}
