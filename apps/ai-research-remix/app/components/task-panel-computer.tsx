import React, { use<PERSON>allback } from 'react'
import {
  EventStatus,
  EventType,
  ToolType,
} from '@apis/mindnote/next-agent-chat.type'
import type {
  BrowserUseOutput,
  CommonSearchOutput,
  Event,
  FileOperatorOutput,
  FileOperatorInput,
} from '@apis/mindnote/next-agent-chat.type'
import { isString } from 'lodash-es'
import { tryParseToJsonObject } from '@bty/util'
import { Palette } from '@bty/components'
import { Icon } from './base/icon'
import { TaskTimeline } from './next-agent/task/task-timeline'
import { TaskSearch } from './next-agent/task/task-search'
import {
  useEventInProgress,
  useEventSnapshot,
} from './next-agent/provider/WhichEventProvider'
import { TaskComputerUse } from './next-agent/task/task-computer-use'
import { TaskTerminal } from './next-agent/task/task-terminal'
import { TaskMarkdown } from './next-agent/task/task-markdown'
import { TaskLoading } from './next-agent/task/task-loading'
import { TaskFile } from './next-agent/task/task-file'
import { supportedLangs, TaskCode } from './next-agent/task/task-code'
import { TaskMcp } from './next-agent/task/task-mcp'
import { processMcpToolName } from './next-agent/utils'
import { TaskHtml } from './next-agent/task/task-html'

function resolveFileComponent(props: {
  name: string
  ext: string
  path?: string
  content?: string
}) {
  if (props.ext === 'md') {
    return <TaskMarkdown key={props.path} {...props} />
  }
  if (props.ext === 'html') {
    return <TaskHtml key={props.path} {...props} />
  }
  if (supportedLangs.includes(props.ext)) {
    return <TaskCode key={props.path} {...props} lang={props.ext} />
  }
  // 其他文件类型 (csv, pdf, etc)
  return <TaskFile key={props.path} {...props} type={props.ext} />
}

export const TaskPanelComputer = React.memo(() => {
  const event = useEventInProgress()

  const { snapshot, allSnapshots, setEventSnapshot } = useEventSnapshot()

  const handleTimelineChange = useCallback((event?: Event) => {
    setEventSnapshot(event ? event.event_id : undefined)
  }, [])

  if (!allSnapshots?.length) {
    return
  }

  const resolvedEvent = snapshot || event

  // action_type后续需要改造
  const label =
    (resolvedEvent?.content as any).action_type === ToolType.MCP
      ? '正在调用MCP'
      : resolvedEvent?.content.action_name

  const args = (() => {
    if ((resolvedEvent?.content as any).action_type === ToolType.MCP) {
      const toolName = processMcpToolName(
        resolvedEvent?.content?.metadata?.tool_name,
      )
      return `${resolvedEvent?.content?.metadata?.name}/${toolName}`
    }
    return resolvedEvent?.content.arguments?.join(' ')
  })()

  const resolveComponent = () => {
    const { content, event_status } = resolvedEvent!
    const tool = content.tool_name
    const output = content.tool_output

    // TODO: action_type 以及mcp逻辑等待后续处理 先暂时使用该逻辑处理
    const isMcp =
      resolvedEvent?.event_type === EventType.TOOL_CALL &&
      (resolvedEvent?.content as any)?.action_type === ToolType.MCP

    if (isMcp) {
      const mcpInfo = {
        description: content.metadata?.description,
        tool_input: content.tool_input,
        tool_output: output,
      }
      return <TaskMcp title={args} mcpInfo={mcpInfo} />
    }
    // const isMcp =
    // event.event_type === EventType.TOOL_CALL &&
    // (event.content as any)?.action_type === ToolType.MCP
    switch (tool) {
      case ToolType.WebSearch:
      case ToolType.LocalSearch:
        return <TaskSearch mode={tool} value={output as CommonSearchOutput} />
      case ToolType.BrowserUse: {
        const useVnc = !snapshot
        const title = content.action_name
        const _snapshot = useVnc
          ? undefined
          : event_status === EventStatus.SUCCESS && output
            ? (output as BrowserUseOutput)
            : undefined
        return (
          <TaskComputerUse title={title} snapshot={_snapshot} useVnc={useVnc} />
        )
      }
      case ToolType.TerminalOperator: {
        const command = Array.isArray(output)
          ? output.join('\n')
          : isString(output)
            ? output
            : ''
        return <TaskTerminal command={command} />
      }
      // case ToolType.MCP: {
      //   const mcpInfo = {
      //     description: content.metadata?.description,
      //     tool_input: content.tool_input,
      //     tool_output: output,
      //   }
      //   return <TaskMcp title={args} mcpInfo={mcpInfo} />
      // }
      case ToolType.WebFetch:
        return <TaskFile type='txt' content={output as string} />
      case ToolType.ReadFile: {
        const _output = output as Required<FileOperatorOutput>

        if (!_output) {
          break
        }

        const name = content.arguments?.[0] ?? ''
        const ext = _output.file_type
        return resolveFileComponent({ name, ext, path: _output.path })
      }
      case ToolType.CreateFile:
      case ToolType.StrReplace:
      case ToolType.WriteFile: {
        const input = content.tool_input
          ? (tryParseToJsonObject(content.tool_input) as FileOperatorInput)
          : undefined

        if (!input) {
          break
        }

        const name = content.arguments?.[0] ?? ''
        const ext = input.file_type || input.file_path?.split('.').pop() || ''
        const text = input.file_content || input.new_str
        return resolveFileComponent({ name, ext, content: text })
      }
      default:
        break
    }

    return <TaskLoading />
  }

  return (
    <div className='h-full px-24px flex flex-col'>
      <div className='shrink-0 text-14px h-64px px-16px flex items-center rounded-10px bg-#f3f3f5'>
        <div className='shrink-0 w-32px h-32px flex justify-center items-center rounded-8px bg-white border border-solid border-[rgba(225,225,229,0.6)]'>
          <Icon className='i-icons-vm' />
        </div>
        <span className='shrink-0 text-font ml-12px mr-6px'>{label}</span>
        <span className='text-#9e9e9e truncate'>{args}</span>
      </div>
      <Palette.Provider>
        {resolvedEvent ? resolveComponent() : <TaskLoading />}
      </Palette.Provider>
      <div className='mt-24px -mx-24px px-12px py-17px border-t-1 border-solid border-[rgba(225,225,229,0.6)]'>
        <TaskTimeline
          eventId={resolvedEvent?.event_id}
          events={allSnapshots}
          onChange={handleTimelineChange}
        />
      </div>
    </div>
  )
})
