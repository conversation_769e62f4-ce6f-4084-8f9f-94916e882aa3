import React, { useEffect } from 'react'
import { cn } from '@bty/util'
import { Tabs, useTaskPanel } from '@/store/task'
import { IconButton } from './base/icon'
import { TaskPanelArtifacts } from './task-panel-artifacts'
import { TaskPanelComputer } from './task-panel-computer'
import { useEventSnapshot } from './next-agent/provider/WhichEventProvider'
import { useNextAgent } from './next-agent/provider/NextAgentProvider'

const tabs = [
  { label: '任务运行详情', value: Tabs.Computer },
  { label: '文件', value: Tabs.Artifact },
]

export const TaskPanel = React.memo(() => {
  const { panelVisible, togglePanel, activeTab, setActiveTab } = useTaskPanel()

  const { setConversationListCollapsed } = useNextAgent()

  const { allSnapshots } = useEventSnapshot()

  useEffect(() => {
    if (panelVisible) {
      setConversationListCollapsed(true)
    }
  }, [panelVisible])

  const content = !panelVisible ? null : activeTab === Tabs.Computer ? (
    <div className='flex-1 max-w-full overflow-hidden'>
      <TaskPanelComputer />
    </div>
  ) : activeTab === Tabs.Artifact ? (
    <div className='flex-1 px-24px pb-24px min-w-full overflow-hidden'>
      <TaskPanelArtifacts />
    </div>
  ) : null

  const canShowPanel = !!allSnapshots?.length

  return (
    <>
      {canShowPanel && (
        <IconButton
          className='absolute top-20px right-24px z-10'
          size='size-32px'
          icon={cn('i-icons-collapse transition-transform', {
            'rotate-180': !panelVisible,
          })}
          iconSize='size-16px'
          onClick={togglePanel}
        />
      )}
      <div
        className={cn(
          'sm:shrink-0 h-full flex flex-col transition-all duration-300 ease-in-out overflow-hidden border-left',
          panelVisible ? 'w-1/2 opacity-100' : 'w-0 opacity-0',
        )}
      >
        <div className='shrink-0 flex gap-8px py-20px px-24px'>
          {tabs.map(tab => (
            <button
              key={tab.value}
              className={cn(
                'cursor-pointer px-10px h-32px leading-32px text-16px text-#3f3f44 bg-transparent rounded-10px transition-300',
                {
                  'bg-#f3f3f5 font-medium text-font': activeTab === tab.value,
                },
              )}
              onClick={() => setActiveTab(tab.value)}
            >
              {tab.label}
            </button>
          ))}
        </div>
        {content}
      </div>
    </>
  )
})
