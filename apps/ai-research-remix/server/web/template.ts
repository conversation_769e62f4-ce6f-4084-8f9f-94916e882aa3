/**
 * HTML template for rendering React components with import maps
 */

/**
 * Generate HTML with import map for React components
 *
 * @param options Template options
 * @param options.importMap Import map for dependencies
 * @param options.compiledCode Compiled JavaScript code
 * @param options.defaultExport Default export name for rendering
 * @returns HTML string
 */
export function generateHtml({
  importMap,
  compiledCode,
  defaultExport,
}: {
  importMap: { imports: Record<string, string> }
  compiledCode: string
  defaultExport?: string
}): string {
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>React Component</title>
  <script type="importmap">
    ${JSON.stringify(importMap, null, 2)}
  </script>
  <script src="https://cdn.tailwindcss.com/3.4.16"></script>
</head>
<body>
  <div id="root"></div>
  <script type="module">
    import ReactDOM from 'react-dom/client';

    // 编译后的代码
    ${compiledCode}

    // 渲染组件

    if (${defaultExport}) {
      const root = ReactDOM.createRoot(document.getElementById('root'));
      root.render(React.createElement(${defaultExport}));
    }
  </script>
</body>
</html>`
}
