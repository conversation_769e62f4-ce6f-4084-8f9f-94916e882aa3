import path from 'node:path'
import { parse, transformSync, type Module } from '@swc/core'
import express from 'express'
import { generateHtml } from './template'

export const webRouter = express.Router()

// // 提供 Service Worker 脚本
// webRouter.get('/sw.js', (req, res) => {
//   const taskId = req.query.taskId

//   if (!taskId) {
//     return res.status(400).send('缺少 taskId 参数')
//   }

//   // Service Worker 脚本内容
//   const swCode = `
//     // 保存 taskId
//     const TASK_ID = "${taskId}";

//     // 监听 install 事件
//     self.addEventListener('install', (event) => {
//       // 立即激活
//       event.waitUntil(self.skipWaiting());
//     });

//     // 监听 activate 事件
//     self.addEventListener('activate', (event) => {
//       // 立即接管所有页面
//       event.waitUntil(self.clients.claim());
//     });

//     // 监听 fetch 事件
//     self.addEventListener('fetch', (event) => {
//       const url = new URL(event.request.url);

//       // 只处理同源请求
//       if (url.origin === self.location.origin) {
//         // 检查是否是我们需要拦截的资源请求
//         if (url.pathname.startsWith('/web/') &&
//             !url.pathname.includes('/html') &&
//             !url.pathname.includes('/sw.js') &&
//             !url.search.includes('taskId=')) {
//           // 添加 taskId 参数
//           url.searchParams.append('taskId', TASK_ID);

//           // 创建新的请求
//           const newRequest = new Request(url.toString(), {
//             method: event.request.method,
//             headers: event.request.headers,
//             mode: event.request.mode,
//             credentials: event.request.credentials,
//             redirect: event.request.redirect
//           });

//           // 使用新请求替换原始请求
//           event.respondWith(fetch(newRequest));
//         }
//       }
//     });
//   `

//   res.setHeader('Content-Type', 'application/javascript')
//   res.setHeader('Service-Worker-Allowed', '/web/')
//   res.send(swCode)
// })

webRouter.get('/html', async (req, res) => {
  const file = req.query.file as string
  const taskId = req.query.taskId as string

  if (!file || !taskId) {
    return res.status(400).end()
  }

  const dir = path.dirname(file)

  try {
    const json = await fetch(
      `${process.env.API_BASE_URL_SUB}/v1/super_agent/chat/oss_url`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: req.userToken,
        },
        body: JSON.stringify({
          task_id: taskId,
          file_path: file,
        }),
      },
    ).then(res => res.json())

    const uri = json.data
    const html = await fetch(uri).then(res => res.text())
    const baseTag = `<base href="${`${req.protocol}://${req.get('host')}/web${dir === '/' ? dir : `${dir}/`}`}">`
    // // 创建 Service Worker 脚本内容
    // const swScript = `
    //   <script>
    //     // 注册 Service Worker
    //     if ('serviceWorker' in navigator) {
    //       // 注册 Service Worker，传递 taskId 作为查询参数
    //       navigator.serviceWorker.register('/web/sw.js?taskId=${taskId}', { scope: '/web/' })
    //         .then(registration => {
    //           console.log('Service Worker 注册成功:', registration.scope);
    //         })
    //         .catch(error => {
    //           console.error('Service Worker 注册失败:', error);
    //         });
    //     }
    //   </script>
    // `
    res.setHeader('Content-Type', 'text/html')
    res.cookie('taskId', taskId, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      path: '/web',
      sameSite: 'lax',
      maxAge: 24 * 60 * 60 * 1000, // 24小时
    })
    res.send(html.replace('<head>', `<head>${baseTag}`))
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '处理 HTML 内容失败',
      error: error instanceof Error ? error.message : String(error),
    })
  }
})

// // HTML ZIP 下载路由
// webRouter.get('/html/zip', async (req, res) => {
//   const { file, taskId } = req.query

//   if (!file || !taskId) {
//     return res.status(400).json({
//       success: false,
//       message: '缺少 file 或 taskId 参数',
//     })
//   }

//   try {
//     // 获取 HTML 文件 URL
//     const response = await fetch(
//       `${process.env.API_BASE_URL_SUB}/v1/super_agent/chat/oss_url`,
//       {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//           Authorization: req.userToken,
//         },
//         body: JSON.stringify({
//           task_id: taskId,
//           file_path: file,
//         }),
//       },
//     )
//     const json = await response.json()
//     const htmlUri = json.data

//     // 获取 HTML 内容
//     const htmlResponse = await fetch(htmlUri)
//     const htmlContent = await htmlResponse.text()

//     // 使用 cheerio 解析 HTML 中的资源引用
//     const resources = await extractResourcesWithCheerio(
//       htmlContent,
//       taskId as string,
//       req.userToken,
//     )

//     // 创建 ZIP 文件
//     const archive = archiver('zip', {
//       zlib: { level: 9 }, // 压缩级别
//     })

//     // 设置响应头
//     res.setHeader('Content-Type', 'application/zip')
//     res.setHeader('Content-Disposition', `attachment; filename="${file}.zip"`)

//     // 将 archive 输出到响应
//     archive.pipe(res)

//     // 添加 HTML 文件
//     archive.append(htmlContent, { name: file as string })

//     // 添加资源文件
//     for (const resource of resources) {
//       archive.append(resource.content, { name: resource.path })
//     }

//     // 完成打包
//     await archive.finalize()
//   } catch (error) {
//     res.status(500).json({
//       success: false,
//       message: '创建 ZIP 文件失败',
//       error: error instanceof Error ? error.message : String(error),
//     })
//   }
// })

webRouter.get('/jsx', async (req, res) => {
  const resource = req.query.resource

  if (!resource) {
    return res.status(400).json({
      success: false,
      message: '缺少 resource 参数',
    })
  }

  const code = ''

  const [deps, defaultExport] = await Promise.all([
    extractDependencies(code),
    extractDefaultExport(code),
  ])

  const compiledCode = compileSync(code)

  const importMap: { imports: Record<string, string> } = {
    imports: deps
      .filter(dep => !['react', 'react-dom', 'react-dom/client'].includes(dep))
      .reduce<Record<string, string>>(
        (map, dep) => {
          map[dep] = `https://esm.sh/${dep}?deps=react@19.1.0,react-dom@19.1.0`
          return map
        },
        {
          react: 'https://esm.sh/react@19.1.0',
          'react-dom': 'https://esm.sh/react-dom@19.1.0',
          'react-dom/client': 'https://esm.sh/react-dom@19.1.0/client',
        },
      ),
  }

  const html = generateHtml({
    importMap,
    compiledCode,
    defaultExport,
  })

  res.setHeader('Content-Type', 'text/html')
  res.send(html)
})

webRouter.get('/*', async (req, res) => {
  const filePath = req.path

  // 尝试从多个来源获取 taskId，按优先级：Cookie > Referer
  let taskId = req.cookies?.taskId as string

  if (!taskId) {
    // 从 Referer header 中提取 taskId
    const referer = req.headers.referer
    if (referer && typeof referer === 'string') {
      const refererUrl = new URL(referer)
      const refererParams = new URLSearchParams(refererUrl.search)
      taskId = refererParams.get('taskId') || ''
    }
  }

  if (!taskId) {
    return res.status(400).end()
  }

  try {
    const json = await fetch(
      `${process.env.API_BASE_URL_SUB}/v1/super_agent/chat/oss_url`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: req.userToken,
        },
        body: JSON.stringify({
          task_id: taskId,
          file_path: filePath,
        }),
      },
    ).then(res => res.json())
    const uri = json.data

    const response = await fetch(uri)
    const text = await response.text()
    const mimeType = response.headers.get('content-type')

    res.setHeader('Content-Type', mimeType ?? 'text/plain')
    res.send(text)
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取资源失败',
      error: error instanceof Error ? error.message : String(error),
    })
  }
})

function compileSync(code: string) {
  const result = transformSync(code, {
    jsc: {
      target: 'es2022',
      parser: {
        syntax: 'ecmascript',
        jsx: true,
      },
    },
    module: {
      type: 'es6',
    },
  })

  return result.code
}

async function extractDefaultExport(code: string) {
  const ast = await parse(code, {
    syntax: 'ecmascript',
    jsx: true,
    target: 'es2022',
  })

  if (ast.type === 'Module') {
    return findDefaultExport(ast)
  }
}

function findDefaultExport(module: Module) {
  for (const item of module.body) {
    if (item.type === 'ExportDefaultExpression') {
      if (item.expression.type === 'Identifier') {
        return item.expression.value
      }
    }
    if (item.type === 'ExportDefaultDeclaration') {
      if (item.decl.type === 'FunctionExpression') {
        if (item.decl.identifier?.type === 'Identifier') {
          return item.decl.identifier.value
        }
      }
    }
  }
}

async function extractDependencies(code: string) {
  const ast = await parse(code, {
    syntax: 'ecmascript',
    jsx: true,
    target: 'es2022',
  })

  const dependencies = new Set<string>()

  if (ast.type === 'Module') {
    traverseModule(ast, dependencies)
  }

  return Array.from(dependencies)
}

function traverseModule(module: Module, dependencies: Set<string>) {
  for (const item of module.body) {
    if (item.type === 'ImportDeclaration') {
      const source = item.source.value
      if (!source.startsWith('.') && !source.startsWith('/')) {
        dependencies.add(source)
      }
    }
    // 检查动态导入
    // else if (
    //   item.type === "ExpressionStatement" &&
    //   item.expression.type === "CallExpression" &&
    //   item.expression.callee.type === "Import"
    // ) {
    //   const args = item.expression.arguments;
    //   console.log("args: ", args);
    //   if (args.length > 0 && args[0].type === "StringLiteral") {
    //     const source = args[0].value;
    //     if (!source.startsWith(".") && !source.startsWith("/")) {
    //       dependencies.add(source);
    //     }
    //   }
    // }
  }
}

// // 使用 cheerio 解析 HTML 中的资源
// async function extractResourcesWithCheerio(
//   htmlContent: string,
//   taskId: string,
//   userToken: string,
// ) {
//   const resources: Array<{ path: string; content: Buffer }> = []
//   const $ = cheerio.load(htmlContent)
//   const resourcePaths = new Set<string>()

//   // 提取 script 标签的 src 属性
//   $('script[src]').each((_, element) => {
//     const src = $(element).attr('src')
//     if (
//       src &&
//       !src.startsWith('http') &&
//       !src.startsWith('//') &&
//       !src.startsWith('data:')
//     ) {
//       resourcePaths.add(src)
//     }
//   })

//   // 提取 link 标签的 href 属性（CSS、图标等）
//   $('link[href]').each((_, element) => {
//     const href = $(element).attr('href')
//     const rel = $(element).attr('rel')
//     if (
//       href &&
//       !href.startsWith('http') &&
//       !href.startsWith('//') &&
//       !href.startsWith('data:')
//     ) {
//       // 处理样式表、图标、预加载等
//       if (
//         rel === 'stylesheet' ||
//         rel === 'icon' ||
//         rel === 'shortcut icon' ||
//         rel === 'preload'
//       ) {
//         resourcePaths.add(href)
//       }
//     }
//   })

//   // 提取 img 标签的 src 属性
//   $('img[src]').each((_, element) => {
//     const src = $(element).attr('src')
//     if (
//       src &&
//       !src.startsWith('http') &&
//       !src.startsWith('//') &&
//       !src.startsWith('data:')
//     ) {
//       resourcePaths.add(src)
//     }
//   })

//   // 提取 video 标签的 src 属性
//   $('video[src]').each((_, element) => {
//     const src = $(element).attr('src')
//     if (
//       src &&
//       !src.startsWith('http') &&
//       !src.startsWith('//') &&
//       !src.startsWith('data:')
//     ) {
//       resourcePaths.add(src)
//     }
//   })

//   // 提取 audio 标签的 src 属性
//   $('audio[src]').each((_, element) => {
//     const src = $(element).attr('src')
//     if (
//       src &&
//       !src.startsWith('http') &&
//       !src.startsWith('//') &&
//       !src.startsWith('data:')
//     ) {
//       resourcePaths.add(src)
//     }
//   })

//   // 提取 source 标签的 src 属性
//   $('source[src]').each((_, element) => {
//     const src = $(element).attr('src')
//     if (
//       src &&
//       !src.startsWith('http') &&
//       !src.startsWith('//') &&
//       !src.startsWith('data:')
//     ) {
//       resourcePaths.add(src)
//     }
//   })

//   // 下载每个资源的内容
//   for (const resourcePath of resourcePaths) {
//     try {
//       const resourceContent = await downloadResource(
//         resourcePath,
//         taskId,
//         userToken,
//       )
//       if (resourceContent) {
//         resources.push({
//           path: resourcePath,
//           content: resourceContent.buffer,
//         })
//       }
//     } catch (error) {
//       console.error(`获取资源失败: ${resourcePath}`, error)
//       // 继续处理其他资源，不中断整个流程
//     }
//   }

//   return resources
// }

// // 下载单个资源
// async function downloadResource(
//   resourcePath: string,
//   taskId: string,
//   userToken: string,
// ) {
//   try {
//     const response = await fetch(
//       `${process.env.API_BASE_URL_SUB}/v1/super_agent/chat/oss_url`,
//       {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//           Authorization: userToken,
//         },
//         body: JSON.stringify({
//           task_id: taskId,
//           file_path: resourcePath,
//         }),
//       },
//     )
//     const json = await response.json()
//     const resourceUri = json.data

//     const resourceResponse = await fetch(resourceUri, {
//       headers: {
//         Authorization: userToken,
//       },
//     })

//     if (resourceResponse.ok) {
//       const buffer = Buffer.from(await resourceResponse.arrayBuffer())
//       return {
//         buffer,
//         contentType: resourceResponse.headers.get('content-type') || '',
//       }
//     }
//   } catch (error) {
//     console.error(`下载资源失败: ${resourcePath}`, error)
//   }
//   return null
// }
// //   const resources: Array<{ path: string; content: Buffer }> = []

// //   // 正则匹配 script 和 link 标签中的相对路径
// //   const scriptRegex = /<script[^>]+src=["']([^"']+)["'][^>]*>/gi
// //   const linkRegex = /<link[^>]+href=["']([^"']+)["'][^>]*>/gi

// //   const resourcePaths = new Set<string>()

// //   // 提取 script 资源
// //   const scriptMatches = htmlContent.matchAll(scriptRegex)
// //   for (const match of scriptMatches) {
// //     const src = match[1]
// //     if (!src.startsWith('http') && !src.startsWith('//')) {
// //       resourcePaths.add(src)
// //     }
// //   }

// //   // 提取 link 资源（CSS 等）
// //   const linkMatches = htmlContent.matchAll(linkRegex)
// //   for (const match of linkMatches) {
// //     const href = match[1]
// //     if (!href.startsWith('http') && !href.startsWith('//')) {
// //       resourcePaths.add(href)
// //     }
// //   }

// //   // 获取每个资源的内容
// //   for (const resourcePath of resourcePaths) {
// //     try {
// //       // 获取资源的真实 URL
// //       const response = await fetch(
// //         `${process.env.API_BASE_URL_SUB}/v1/super_agent/chat/oss_url`,
// //         {
// //           method: 'POST',
// //           headers: {
// //             'Content-Type': 'application/json',
// //             Authorization: userToken,
// //           },
// //           body: JSON.stringify({
// //             task_id: taskId,
// //             file_path: resourcePath,
// //           }),
// //         },
// //       )
// //       const json = await response.json()
// //       const resourceUri = json.data

// //       // 获取资源内容
// //       const resourceResponse = await fetch(resourceUri, {
// //         headers: {
// //           Authorization: userToken,
// //         },
// //       })

// //       if (resourceResponse.ok) {
// //         const content = await resourceResponse.arrayBuffer()
// //         resources.push({
// //           path: resourcePath,
// //           content: Buffer.from(content),
// //         })
// //       }
// //     } catch (error) {
// //       console.error(`获取资源失败: ${resourcePath}`, error)
// //       // 继续处理其他资源，不中断整个流程
// //     }
// //   }

// //   return resources
// // }
