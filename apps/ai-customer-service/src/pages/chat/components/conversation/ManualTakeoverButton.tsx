import iconTakeover from '@/assets/takeover-gray.svg?url'
import { TooltipStyled } from '@/components/tooltip/TooltipStyled'
import { cn } from '@bty/util'
import React from 'react'

interface IManualTakeoverButtonProps extends React.PropsWithChildren {
  overrides?: {
    root?: string
  }
  onTooltipOpenChange?: (open: boolean) => void
  onClick?: (e: React.MouseEvent<HTMLDivElement>) => void
}

export const ManualTakeoverButton = React.memo(
  ({
    overrides,
    onTooltipOpenChange,
    onClick,
  }: IManualTakeoverButtonProps) => {
    return (
      <TooltipStyled
        mouseEnterDelay={0.8}
        placement='bottom'
        title={'转接人工客服'}
        onOpenChange={onTooltipOpenChange}
        overlayInnerStyle={{
          height: '34px',
          lineHeight: '18px',
        }}
      >
        <div
          className={cn(
            'h-24px w-24px flex justify-center items-center rounded-4px p-2px cursor-pointer',
            'hover:bg-gray-200/50 active:bg-gray-200',
            overrides?.root,
          )}
          onClick={onClick}
        >
          <img className={'text-#626999 w-19px h-19px'} src={iconTakeover} alt='stop' />
          {/* fill="#626999" fill-opacity="0.6000000238418579" */}
        </div>
      </TooltipStyled>
    )
  },
)
