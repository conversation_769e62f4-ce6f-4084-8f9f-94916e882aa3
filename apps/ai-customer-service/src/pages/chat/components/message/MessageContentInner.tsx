import { Image } from 'antd'
import { memo } from 'react'
import { cn } from '@bty/util'
import { MessageProductCard } from './MessageProductCard'

interface MessageContentInnerProps {
  text: string
  url: string
  image: string
  contextType: string
  isCustomer: boolean
}

// 将文本中的 URL 转换为可点击的链接
function renderTextWithLinks({
  text,
  isCustomer,
}: {
  text: string
  isCustomer: boolean
}) {
  // URL 正则表达式
  const urlRegex = /(https?:\/\/[^\s]+)/g
  const parts = text.split(urlRegex)

  return parts.map((part, index) => {
    if (urlRegex.test(part)) {
      return (
        <a
          key={index}
          className={cn(
            'break-all',
            isCustomer ? 'text-primary' : 'text-white underline',
          )}
          href={part}
          target='_blank'
          rel='noopener noreferrer'
        >
          {part}
        </a>
      )
    }
    return part
  })
}

// 图片加载占位符组件
function ImagePlaceholder({ height }: { height: string }) {
  return (
    <div
      className='flex items-center animate-pulse justify-center bg-gray-150 rounded-12px'
      style={{ height }}
    ></div>
  )
}

export const MessageContentInner = memo(function MessageContentInner({
  text,
  url,
  image,
  contextType,
  isCustomer,
}: MessageContentInnerProps) {
  const isUrl = url || (!url && text?.trim()?.startsWith('http'))

  // 图片固定高度
  const IMAGE_HEIGHT = '200px'

  // 处理产品卡片类型
  if (contextType === 'CARD' && text) {
    if (image) {
      if (url) {
        return (
          <a href={url} target='_blank' rel='noopener noreferrer'>
            <Image
              preview={true}
              className={'rounded-12px'}
              style={{ height: '200px', width: 'auto', objectFit: 'contain' }}
              src={image}
              alt='消息图片'
              placeholder={<ImagePlaceholder height='200px' />}
            />
          </a>
        )
      } else {
        return (
          <div>
            <Image
              preview={true}
              className={'rounded-12px'}
              style={{ height: '200px', width: 'auto', objectFit: 'contain' }}
              src={image}
              alt='消息图片'
              placeholder={<ImagePlaceholder height='200px' />}
            />
          </div>
        )
      }
    }

    return <MessageProductCard productData={text} className='max-w-280px' />
  }

  if (image) {
    return (
      <div
        className='relative overflow-hidden rounded-12px'
        style={{
          height: '200px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Image
          className={'rounded-12px'}
          preview={true}
          src={image}
          alt='消息图片'
          style={{ height: IMAGE_HEIGHT, width: 'auto', objectFit: 'contain' }}
          placeholder={<ImagePlaceholder height='200px' />}
        />
      </div>
    )
  }

  if (isUrl) {
    const url2 = url || text?.trim().split(' ')[0]
    return (
      <a
        className={cn(
          'leading-26px break-all',
          isCustomer ? 'text-primary' : 'text-white',
        )}
        href={url2}
        target='_blank'
        rel='noopener noreferrer'
      >
        {url || text}
      </a>
    )
  }

  return (
    <p className={'leading-26px break-all'}>
      {renderTextWithLinks({ text, isCustomer })}
    </p>
  )
})
