import { useMemo, useState } from 'react'
import { requestChatRecordsList } from '../../../apis/apis'
import { MESSAGE_PAGE_SIZE, RECORD_TEMP_PREFIX } from '../../../constants'
import type { IChatRecord, RecordStatusType } from '../../../types/types'
import {
  ChatRoleEnum,
  ContextType,
  RecordCategoryEnum,
  RecordStatusEnum,
} from '../../../types/types'
import { useChatRecordsData } from './useChatRecordsData'

interface UseChatRecordsStateProps {
  selectedConversationId?: string
}

export function useChatRecordsState({
  selectedConversationId,
}: UseChatRecordsStateProps) {
  const [messageList, setMessageList] = useState<IChatRecord[]>([])
  const [hasMoreMessages, setHasMoreMessages] = useState(true)
  const [loadingMoreMessages, setLoadingMoreMessages] = useState(false)
  const [isLoadingHistory, setIsLoadingHistory] = useState(false)

  // 聊天消息数据
  const {
    chatRecordListLoading,
    chatRecordListDisplayLoading,
    refreshChatRecordList,
  } = useChatRecordsData({
    conversationId: selectedConversationId,
    pageSize: MESSAGE_PAGE_SIZE,
    onSuccess: data => {
      setMessageList(prev => concatMessages({ prevMessages: prev, newMessages: data as IChatRecord[] }))
      setHasMoreMessages((data as IChatRecord[]).length === MESSAGE_PAGE_SIZE)
      setIsLoadingHistory(false) // 刷新消息列表时重置状态
    },
  })

  const concatMessages = ({ prevMessages, newMessages }: { prevMessages: IChatRecord[], newMessages: IChatRecord[] }) => {
    const existingIds = new Set(prevMessages.map(msg => msg.id))
    const uniqOldMessages = newMessages.filter(
      msg => !existingIds.has(msg.id),
    )
    const allMessages = [...prevMessages, ...uniqOldMessages,]
      .sort((a, b) => a.id - b.id)
      .filter(msg => !msg.record_id?.startsWith(RECORD_TEMP_PREFIX))
    return allMessages
  }

  // 加载更多历史消息
  const loadMoreMessages = async () => {
    if (!selectedConversationId || !hasMoreMessages || loadingMoreMessages)
      return

    try {
      setLoadingMoreMessages(true)
      setIsLoadingHistory(true) // 标记正在加载历史消息
      const oldestMessage = messageList[0]
      const list = await requestChatRecordsList({
        conversationId: selectedConversationId,
        pageSize: MESSAGE_PAGE_SIZE,
        currentId: oldestMessage?.id,
        direction: 'prev',
      })

      if (list && list.length > 0) {
        // 转换数据格式，确保类型匹配并去重
        const formattedList = list.map(item => ({
          ...item,
          record_status: item.record_status || RecordStatusEnum.SUCCESS,
        })) as IChatRecord[]

        // 将新消息添加到列表前面，同时去重
        setMessageList(prev => {
          return concatMessages({ prevMessages: prev, newMessages: formattedList })
        })
        setHasMoreMessages(list.length === MESSAGE_PAGE_SIZE)
      } else {
        setHasMoreMessages(false)
      }
    } catch (error) {
      console.error('获取更多消息失败:', error)
    } finally {
      setLoadingMoreMessages(false)
    }
  }

  // 根据选中对话过滤消息列表并反转顺序
  const currentRecordList = useMemo(() => {
    return messageList.filter(m => m.conversation_id === selectedConversationId)
  }, [messageList, selectedConversationId])

  // 乐观更新：立即添加消息到列表
  const addOptimisticMessage = (content: string, imageUrls: string[] = []) => {
    if (!selectedConversationId) return

    setIsLoadingHistory(false) // 添加新消息时确保不是加载历史状态

    const now = new Date().toISOString()
    const tempId = `temp_${Date.now()}_${Math.random()}`
    const newMessages: IChatRecord[] = []

    // 为每个图片创建独立的消息
    imageUrls.forEach((url, index) => {
      const imageMessage: IChatRecord = {
        id: Date.now() + index + 1,
        record_id: `${tempId}_image_${index}`,
        conversation_id: selectedConversationId,
        role: ChatRoleEnum.ASSISTANT,
        role_id: 'assistant',
        role_name: '智能客服',
        context: { image: url },
        context_type: ContextType.IMAGE,
        record_status: RecordStatusEnum.SENDING,
        message_time: now,
        category: RecordCategoryEnum.MANUAL_TAKEOVER,
        created_at: now,
        updated_at: now,
      }
      newMessages.push(imageMessage)
    })

    // 创建文本消息（如果有内容）
    if (content?.trim()) {
      const textMessage: IChatRecord = {
        id: Date.now(),
        record_id: tempId,
        conversation_id: selectedConversationId,
        role: ChatRoleEnum.ASSISTANT,
        role_id: 'assistant',
        role_name: '智能客服',
        context: { text: content },
        context_type: ContextType.TEXT,
        record_status: RecordStatusEnum.SENDING,
        message_time: now,
        category: RecordCategoryEnum.MANUAL_TAKEOVER,
        created_at: now,
        updated_at: now,
      }
      newMessages.push(textMessage)
    }

    // 添加所有消息到列表
    if (newMessages.length > 0) {
      setMessageList(prev => [...prev, ...newMessages])
    }

    return newMessages.map(msg => msg.record_id)
  }

  // 移除乐观更新的消息（发送失败时使用）
  const removeOptimisticMessages = (recordIds: string[]) => {
    setMessageList(prev =>
      prev.filter(msg => !recordIds.includes(msg.record_id)),
    )
  }

  // 更新消息状态（发送成功时使用）
  const updateMessageStatus = (
    recordIds: string[],
    status: RecordStatusType,
  ) => {
    setMessageList(prev =>
      prev.map(msg =>
        recordIds.includes(msg.record_id)
          ? { ...msg, record_status: status }
          : msg,
      ),
    )
  }

  return {
    // 消息数据
    messageList: currentRecordList,
    setMessageList,
    messageLoading: chatRecordListLoading,
    messageDisplayLoading: chatRecordListDisplayLoading,
    refreshMessageList: refreshChatRecordList,
    // 乐观更新方法
    addOptimisticMessage,
    removeOptimisticMessages,
    updateMessageStatus,
    // 加载更多消息
    hasMoreMessages,
    loadMoreMessages,
    isLoadingHistory,
    loadingMoreMessages,
  }
}
