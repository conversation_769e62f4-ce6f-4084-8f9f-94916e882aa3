import { memo, useMemo, useRef } from 'react'
import type { ChatCore } from '@bty/chat/core'
import { useChatStater } from '@bty/chat/core-react'
import type { ApplicationBodyType } from '@apis/application/type.ts'
import cn from 'classnames'
import { useMemoizedFn } from 'ahooks'
import { LoadingOutlined } from '@ant-design/icons'
import { Button, IconFont, Modal } from '@/components'
import { TaskAgentIcon } from '@/features/agent/Task/TaskChatIcon.tsx'
import {
  AgentVariableModal,
  AgentVariablePop,
} from '@/pages/agent/components/AgentVariable.tsx'
import { TaskInfo } from '@/features/agent/Task/TaskInfo.tsx'
import type { useAgentDiff } from '@/pages/agent/hooks/useAgentDiff.ts'
import type { useTask } from '@/features/agent/Task/hooks/useTask.ts'
import { isReadwrite, usePermissions } from '@/components/acl'
import { AgentChatContent } from '@/pages/agent/components/AgentChatContent.tsx'
import { useAgentBatchTestStore } from '@/store/agentBatchTest.ts'
import { AGENT_BATCH_TEST_STATUS } from '@/features/agent/const.ts'
import { AgentDiff } from '@/pages/agent/components/AgentDiff.tsx'
import { useAgentEdit } from '@/features/agent/provider/AgentEditProvider'
import { LimitedAccess } from '@/features/pay/LimitedAccess'

interface AgentChatProps {
  chatCore: ChatCore
  taskProps: ReturnType<typeof useTask>
  agentDiffProps: ReturnType<typeof useAgentDiff>
  applicationInfo: ApplicationBodyType
  showBatchTest: () => void
}

const AgentChatComp = memo<AgentChatProps>(props => {
  const {
    chatCore,
    agentDiffProps,
    showBatchTest,
    taskProps,
    applicationInfo,
  } = props

  const chatDomRef = useRef<HTMLDivElement>(null)
  const conversationId = useChatStater(chatCore.conversation, 'conversationId')
  const agent = useChatStater(chatCore.agent, 'agentConfig')

  const permission = usePermissions({
    type: 'AGENT',
    id: agent
      ? [agent?.application?.flowId, agent?.application?.appGroupId]
      : undefined,
  })
  const readwrite = isReadwrite(permission)

  const { ruleCollapsed, setRuleCollapsed } = useAgentEdit()

  const {
    diffHasOpened,
    diffOpen,
    diffConfigs,
    handleDeleteDiffConfigConversation,
    handleDiffUse,
    handleDiffOpen,
    handleDiffClose,
    handleAddDiffConfig,
  } = agentDiffProps

  const {
    taskOpen,
    showTaskId,
    taskRecordId,
    handleTaskOpen,
    handleTaskClose,
    handleTaskBack,
    handleOpenTask,
  } = taskProps

  const variableConfigs = agent?.config?.rule?.agent_variables ?? []
  const variableModalOpen = useChatStater(chatCore.ui, 'variableModalOpen')
  const closeVarModal = useMemoizedFn(() => {
    chatCore.ui.setVariableOpen(false)
  })

  const isTaskEnable = useMemo(() => {
    const rule = agent?.config?.rule
    return !!rule?.preset_tasks_enabled
  }, [applicationInfo?.config, agent])

  const isFreeModelEnabled = useMemo(() => {
    return !!agent?.config?.rule?.free_model
  }, [agent])

  const { batchTestStatus, runningDoneSteps, totalSteps, testCount } =
    useAgentBatchTestStore(store => {
      return {
        batchTestStatus: store.batchTestStatus,
        runningDoneSteps: store.runningDoneSteps,
        totalSteps: store.totalSteps,
        testCount: store.batchTestCount,
      }
    })

  return (
    <div
      className={cn(
        'flex-1 h-full overflow-hidden bg-#fafafc flex flex-col relative',
        '[&_#bty-chat-container]:bg-#fafafc [&_.chat-input-container]:bg-#fafafc',
        '[&_.chat-input-wrapper]:bg-#fafafc [&_.chat-input-wrapper_textarea]:bg-transparent',
        '[&_.bty-ShortcutTipsWrapper]:bg-transparent',
        '[&_.chat-message-scroll-content]:max-w-full! [&_.bty-chat-input-wrapper-content]:max-w-full [&_.bty-message-list-action-wrapper]:max-w-full',
        '[&_.bty-to-bottom-btn]:right-58px',
        '[&_.chat-message-scroll-content]:px-40px [&_.bty-chat-input-wrapper]:px-40px',
      )}
      style={{
        flexGrow: diffOpen ? diffConfigs.length * 10 : '',
      }}
      ref={chatDomRef}
    >
      <header className='flex-none flex items-center h-36px px-24 mt-12px mb-20px'>
        <h1 className='font-medium text-16px text-font mr-auto'>效果预览</h1>

        {!diffOpen && batchTestStatus === AGENT_BATCH_TEST_STATUS.FINISH && (
          <LimitedAccess
            limitedType='shouldCheckNotEnterpriseVersion'
            limitFeatureName='批量调试'
          >
            <span
              className='flex flex-center text-14px text-#7B61FF! cursor-pointer rounded-6px p-4px hover:bg-[rgba(98,105,153,0.08)] mr-4px'
              onClick={showBatchTest}
            >
              <IconFont name='piliangceshi' className='mr-4px' />
              <span>批量调试</span>
              {!!testCount && (
                <span className='ml-4px flex-center px-4px h-16px bg-primary bg-op-12 rounded-full'>
                  {testCount}
                </span>
              )}
            </span>
          </LimitedAccess>
        )}

        {!diffOpen && batchTestStatus === AGENT_BATCH_TEST_STATUS.RUNNING && (
          <span
            className='flex flex-center text-14px text-#7B61FF! cursor-pointer rounded-6px p-4px hover:bg-[rgba(98,105,153,0.08)] mr-4px'
            onClick={showBatchTest}
          >
            <LoadingOutlined className='mr-4px' />
            <span>批量调试中</span>
            <span className='ml-4px flex-center bg-primary bg-op-12 rounded-8px px-4px py-2px text-12px'>
              {runningDoneSteps}/{totalSteps}
            </span>
          </span>
        )}

        {!diffOpen && isTaskEnable && (
          <TaskAgentIcon
            agentId={applicationInfo.flowId}
            conversationId={conversationId}
            onClick={handleTaskOpen}
          />
        )}

        {variableConfigs && !!variableConfigs.length && (
          <AgentVariablePop
            alwaysActive
            close
            varList={variableConfigs}
            onConfirm={closeVarModal}
            onOpen={handleTaskClose}
          >
            <span className='flex flex-center text-14px text-#7B61FF! mr-8px cursor-pointer rounded-6px p-4px hover:bg-[rgba(98,105,153,0.08)] [&.ant-popover-open]:bg-[rgba(98,105,153,0.08)]'>
              <IconFont name='bianliang' className='mr-4px' />
              <span>变量</span>
            </span>
          </AgentVariablePop>
        )}

        {/* {hasSystemPrompt && !diffOpen && ( */}
        {readwrite && !diffOpen && !isFreeModelEnabled && (
          <span
            className='flex flex-center text-14px text-#7B61FF! cursor-pointer rounded-6px p-4px hover:bg-[rgba(98,105,153,0.08)]'
            onClick={() => {
              handleDiffOpen()
              setRuleCollapsed(true)
            }}
          >
            <IconFont name='muxing' className='mr-4px' />
            <span>多模型对比</span>
          </span>
        )}

        {diffOpen && (
          <Button
            disabled={diffConfigs.length >= 3}
            className='flex flex-center px-12px mr-8px font-normal!'
            onClick={handleAddDiffConfig}
          >
            <IconFont name='tianjiahuihua' className='mr-4px' />
            添加模型{diffConfigs.length}/3
          </Button>
        )}

        {diffOpen && (
          <Button
            className='flex flex-center px-12px font-normal!'
            onClick={() => {
              handleDiffClose()
              setRuleCollapsed(false)
            }}
          >
            <IconFont name='daochu-1' className='mr-4px' />
            退出多模型
          </Button>
        )}
        {diffOpen && (
          <Button
            className='flex flex-center font-normal! ml-8px px-10px!'
            onClick={() => setRuleCollapsed(!ruleCollapsed)}
          >
            <IconFont name={ruleCollapsed ? 'suoxiaocongkou' : 'fangtai-1'} />
          </Button>
        )}
      </header>

      <div
        className={cn('flex-1 overflow-hidden', {
          hidden: diffOpen,
        })}
      >
        <AgentChatContent
          applicationInfo={applicationInfo}
          chatCore={chatCore}
          onTaskClick={handleOpenTask}
        />
      </div>

      {(diffOpen || diffHasOpened.current) && (
        <div
          className={cn('flex-1 overflow-hidden', {
            hidden: !diffOpen,
          })}
        >
          <AgentDiff
            chatCore={chatCore}
            applicationInfo={applicationInfo}
            onUse={handleDiffUse}
            onDelete={handleDeleteDiffConfigConversation}
          />
        </div>
      )}

      <AgentVariableModal
        open={variableModalOpen}
        getContainer={() => chatDomRef.current!}
        close={false}
        varList={variableConfigs ?? []}
        onConfirm={closeVarModal}
      />

      <Modal
        centered={false}
        closable={false}
        footer={null}
        mask={false}
        wrapClassName='static!'
        className='absolute top-110px! bottom-20px right-20px p-0px [&_.ant-modal-content]:h-full z-10'
        width={480}
        styles={{
          body: { padding: 0, height: '100%' },
          content: {
            position: 'absolute',
            top: 0,
            bottom: 0,
            left: 0,
            right: 0,
          },
        }}
        open={taskOpen}
        destroyOnClose
      >
        <TaskInfo
          agentId={applicationInfo.flowId}
          taskRecordId={taskRecordId}
          versionId={applicationInfo.versionId}
          showTaskId={showTaskId}
          conversationId={conversationId}
          onBack={handleTaskBack}
          onClose={handleTaskClose}
        />
      </Modal>
    </div>
  )
})

export default AgentChatComp
