import styled from '@emotion/styled'
import { useBoolean, useRequest, useKeyPress, useMemoizedFn } from 'ahooks'
import { memo, useEffect, useMemo, useRef, useState } from 'react'
import type { PaginatedMessageHistory, Task } from '@bty/global-types/message'
import { type AgentLogItem, type AgentLogDetailParams } from '@apis/agent/type'
import { fetchAgentLogDetail } from '@apis/agent'
import { getPrevChatRecordsById } from '@apis/monitor'
import type { ChatRecordsParams } from '@apis/monitor/type'
import classNames from 'classnames'
import { localize } from '@bty/localize'
import type { MessageItem } from '@bty/chat/core'
import { transformAssistantMessage, transformUserMessage } from '@bty/chat/core'
import type { ChatEvents, MessageListProps } from '@bty/chat/ui'
import { ChatEventProvider, MessageList, MessageError } from '@bty/chat/ui'
import dayjs from 'dayjs'
import type Scrollbar from 'smooth-scrollbar'
import { Preview } from '@bty/components'
import { getLLMModelList } from '@apis/llm'
import { TaskLogModal } from '@/features/chat'
import { IconFont, Modal } from '@/components'
import { useScrollBar } from '@/hooks/useScrollBar.ts'
import { TaskLog } from '@/features/agent/Task/TaskLog'
import { getLastCreateTime } from '../utils'
import { withUserMessage } from '@/features/chat/hoc/withUserMessage.tsx'
import { withAssistantMessage } from '@/features/chat/hoc/withAssistantMessage.tsx'
import { withSuggestPlugin } from '@/pages/agent/components/SuggestPlugin.tsx'
import {
  officePreviewProvider,
  onlyOfficeServerUrl,
} from '@/constants/officePreview'
import { handlePreviewError } from '@/features/preview/handlePreviewError'
import { DefaultModelIcon } from '@/constants/common'
import { agentRunTypesMap } from './Logs'

const PreviewModalWrapper = styled(Modal)`
  .ant-modal-body {
    height: 90vh;
    padding: 0px !important;
  }
`

const ModalWrapper = styled.div<{ top: number; position?: string }>`
  position: ${({ position }) => position || 'absolute'};
  top: ${({ top }) => top}px;
  bottom: 8px;
  right: 8px;
  display: flex;
  flex-direction: column;
  width: 600px;
  border-radius: 8px;
  background-color: #fff;
  backdrop-filter: blur(16px);
  box-shadow: 0px 8px 24px 0px rgba(0, 0, 0, 0.2);
  z-index: 6;
  overflow: hidden;
  & {
    .run-btn {
      &:disabled {
        color: #f7f7ff;
        background: linear-gradient(
            0deg,
            rgba(255, 255, 255, 0.8),
            rgba(255, 255, 255, 0.8)
          ),
          #7b61ff;
      }
    }
  }
`

export interface LogModalProps {
  onClose?: () => void
  agentId?: string
  conversationId?: string
  appId?: string
  recordId?: string
  data?: PaginatedMessageHistory
  position?: 'absolute' | 'fixed'
  robotAvatarInfo?: MessageListProps['botAvatarInfo']
  mode?: 'onlyContent'
  modalClassName?: string
  selectedLog?: AgentLogItem
  title?: string
  subAgentRecordId?: string
}

export function LogModal({
  onClose,
  robotAvatarInfo,
  agentId,
  conversationId,
  recordId,
  appId,
  position = 'absolute',
  mode,
  modalClassName = '',
  selectedLog,
  title,
  subAgentRecordId,
}: LogModalProps) {
  const [taskOpen, { setTrue: openTaskModal, setFalse: closeTaskModal }] =
    useBoolean(false)
  const [taskDetailId, setTaskDetailId] = useState<string>()

  const [task, setTask] = useState<any>({})

  const [logModalVisible, { toggle: toggleLogModalVisible }] = useBoolean(false)

  const [hasMore, setHasMore] = useState(false)
  const [messageList, setMessageList] = useState<MessageItem[]>([])

  const { data: modelList } = useRequest(
    () => getLLMModelList('textGenerate'),
    {
      cacheKey: 'MODEL_LIST',
      cacheTime: 1000 * 60 * 60 * 1,
    },
  )

  const params = useRef<AgentLogDetailParams & ChatRecordsParams>({
    agent_id: agentId || '',
    conversation_id: conversationId || '',
    page_no: 1,
    page_size: 10,
    application_id: appId,
    record_id: recordId || '',
    limit: 5,
  })

  const [dialogConfig, setDialogConfig] = useState({
    open: false,
    url: '',
    type: '',
  })

  useKeyPress('esc', () => {
    setDialogConfig({ ...dialogConfig, open: false })
  })

  const {
    data,
    loading,
    runAsync: loadList,
  } = useRequest(
    async (params: AgentLogDetailParams & ChatRecordsParams) => {
      if (params.record_id) {
        const records = await getPrevChatRecordsById({
          record_id: params.record_id || '',
          limit: params.limit,
        })
        return (records || []).reverse()
      }
      {
        const res = await fetchAgentLogDetail({
          ...params,
          record_id: subAgentRecordId || '',
        })
        setHasMore(res && res?.total_pages > res?.page_no)
        return res?.data_list || []
      }
    },
    {
      manual: true,
    },
  )

  const handleTaskModalOpen = useMemoizedFn(async (task: Task) => {
    if (!conversationId) return
    setTaskDetailId(task.id)
    openTaskModal()
  })

  const messageListScrollBarRef = useRef<Scrollbar>(null)
  const getTransformMessageList = useMemoizedFn((data: any[]) => {
    const list: MessageItem[] = []
    data?.forEach(message => {
      if (
        message.user_content ||
        message.quotes?.length ||
        message.files?.length
      ) {
        list.push(transformUserMessage(message))
      }
      list.push(transformAssistantMessage(message))
    })
    return list
  })

  useEffect(() => {
    const init = async () => {
      const originList = await loadList(params.current)
      const list = getTransformMessageList(originList)
      setMessageList(list)
      setTimeout(() => {
        messageListScrollBarRef.current?.scrollTo(
          0,
          messageListScrollBarRef.current.limit.y,
          0,
        )
      }, 30)
    }
    init()
  }, [recordId])

  const onLoadMore = useMemoizedFn(async () => {
    if (hasMore && !loading) {
      params.current.page_no += 1
      const originList = await loadList(params.current)
      const list = getTransformMessageList(originList)
      setMessageList(prevState => {
        return [...list, ...prevState]
      })
    }
  })

  const onMessageToolClick: ChatEvents['onToolClick'] = tool => {
    toggleLogModalVisible()
    setTask({
      title: tool.metaData.title,
      status: tool.status === 'SUCCEEDED' ? 'success' : 'error',
      data: tool.raw,
    })
  }

  const handleFilePreview = useMemoizedFn<ChatEvents['onFilePreview']>(file => {
    setDialogConfig({ url: file.url, type: file.type, open: true })
  })

  const handleUrlClick = useMemoizedFn((url: string) => {
    if (url.includes('/api/v1/dataset/file/download')) {
      const encodedUrl = encodeURIComponent(url)
      window.open(`/file-preview?downloadLink=${encodedUrl}`, '_blank')
      return
    }
    window.open(url, '__blank')
  })

  const onPreviewError = useMemoizedFn(handlePreviewError)

  const { scrollRef } = useScrollBar()

  const messageListRenders = useMemo<MessageListProps['renders']>(() => {
    return {
      userMessage: withUserMessage({
        renders: {
          reEdit: false,
        },
      }),
      assistantMessage: withAssistantMessage({
        readonly: true,
        renders: {
          messageTime: false,
          suggestPlugin: withSuggestPlugin(true),
          modelComponent: data => (
            <div className='flex items-center gap-4px bg-#F2F3F7 px-6px py-3px rounded-8px'>
              <img
                src={
                  modelList?.find(item => item.model === data.model)?.feature
                    ?.icon || DefaultModelIcon
                }
                alt={data.model}
                className='w-14px h-14px'
              />
              <span className='text-12px c-#8D8D99'>{data.model}</span>
            </div>
          ),
          footer: memo(props => {
            const { message } = props
            return (
              <>
                <MessageError className='mb-12' message={message} />
                <div>
                  {`${message.irlTime ? `首字 ${message.irlTime} s ` : ''}${
                    message.durationTime
                      ? `耗时 ${message.durationTime || ''} s ·`
                      : ''
                  }  ${dayjs(message.create_time).format('YYYY-MM-DD HH:mm:ss')}`}
                </div>
              </>
            )
          }),
        },
      }),
    }
  }, [modelList])

  return (
    <ModalWrapper top={8} position={position} className={modalClassName}>
      {mode !== 'onlyContent' && (
        <div className='flex flex-items-center flex-justify-between h-56px shrink-0 flex-grow-0 p-l-20px p-r-16px border-b-1 border-b-#e1e1e5 border-b-op-60'>
          <div className='flex flex-items-center'>
            {selectedLog ? (
              <>
                <div className='text-16px c-#17171D font-500 mr-4px'>
                  {agentRunTypesMap[selectedLog.run_type]?.label}
                </div>
                <div className='m-r-12 text-16px font-500 c-#17171d c-op-60'>
                  {getLastCreateTime(selectedLog.latest_create_time)}
                </div>
                <div
                  className={classNames(
                    'flex-center w-min h-20px px-14px b-rd-4px bg-op-12 text-12px font-500 whitespace-nowrap mr-12px',
                    {
                      'bg-#2cb969 c-#2cb969':
                        selectedLog.run_status === 'SUCCEEDED',
                      'bg-#ff5219 c-#ff5219':
                        selectedLog.run_status === 'FAILED',
                    },
                  )}
                >
                  {selectedLog.run_status === 'SUCCEEDED'
                    ? localize('agent.log.status.success', '成功')
                    : localize('agent.log.status.failed', '失败')}
                </div>
              </>
            ) : (
              <span className='m-r-12 text-16px font-600 c-#17171d'>
                {title || localize('agent.log.debug.title', '运行调试')}
              </span>
            )}
          </div>
          <div
            className='w-24px h-24px b-rd-4px flex flex-items-center flex-justify-center cursor-pointer hover:bg-#626999 hover:bg-op-12'
            onClick={onClose}
          >
            <IconFont name='guanbi' className='text-16px' />
          </div>
        </div>
      )}
      <div className='h-full relative flex-1 of-hidden [&_.chat-message-scroll-content]:px-30px [&_.bty-chat-message-list-container]:h-full [&_.bty-markdown-a]:line-height-24px'>
        <ChatEventProvider
          onToolClick={onMessageToolClick}
          onFilePreview={handleFilePreview}
          onHrefClick={handleUrlClick}
          onTaskClick={handleTaskModalOpen}
        >
          <MessageList
            scrollBarRef={messageListScrollBarRef}
            data={messageList}
            botAvatarInfo={robotAvatarInfo}
            loadMore={onLoadMore}
            hasMore={hasMore}
            renders={messageListRenders}
          />
        </ChatEventProvider>
      </div>
      {logModalVisible && (
        <TaskLogModal
          onClose={toggleLogModalVisible}
          top={0}
          right={0}
          task={task}
          title={task?.title}
          status={task?.status}
        />
      )}
      <PreviewModalWrapper
        style={{
          height: '90vh',
        }}
        onCancel={() => setDialogConfig({ ...data, open: false })}
        className='overflow-hidden !w-[90vw] !max-w-[100vw] !h-[90vh] p-[0px] border-[0px]'
        open={dialogConfig.open}
        footer={null}
      >
        <div className='w-full h-full overflow-auto' ref={scrollRef}>
          <Preview
            url={dialogConfig.url!}
            mimeType={dialogConfig.type!}
            onError={onPreviewError}
            rendererOptions={{
              office: {
                provider: officePreviewProvider,
                onlyOfficeServerUrl,
              },
            }}
          />
        </div>
      </PreviewModalWrapper>

      {agentId && conversationId && taskDetailId && (
        <Modal
          centered={false}
          closable={false}
          footer={null}
          mask={false}
          wrapClassName='static!'
          className='absolute top-68px! bottom-8px right-8px p-0px [&_.ant-modal-content]:h-full z-10'
          width={600}
          styles={{
            body: { padding: 0, height: '100%' },
            content: {
              position: 'absolute',
              top: 0,
              bottom: 0,
              left: 0,
              right: 0,
            },
          }}
          open={taskOpen}
          destroyOnClose
        >
          <TaskLog
            agentId={agentId}
            taskId={taskDetailId}
            readonly
            logModalPosition={{ top: 68, right: 8, bottom: 8 }}
            conversationId={conversationId}
            onClose={closeTaskModal}
          />
        </Modal>
      )}
    </ModalWrapper>
  )
}
