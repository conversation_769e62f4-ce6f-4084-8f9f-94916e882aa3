import {
  useBoolean,
  useRequest,
  useUnmount,
  useUpdateEffect,
  useDebounceEffect,
  useAntdTable,
  useDebounceFn,
} from 'ahooks'
import type { ColumnsType } from 'antd/es/table'
import classNames from 'classnames'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import { useSearchParams } from 'react-router-dom'
import isBetween from 'dayjs/plugin/isBetween'
import {
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
  useCallback,
  useEffect,
} from 'react'
import styled from '@emotion/styled'
import { Pagination, Tabs, Form } from 'antd'
import type { TabsProps } from 'antd'
import { isEqual } from 'lodash-es'
import { AgentFeedbackStatusEn } from '@apis/agent/type'
import {
  exportAgentChatFeedBackList,
  fetchHandleFeedback,
  getAgentChatFeedBackList,
  fetchAgentLogs,
} from '@apis/agent'
import type {
  AgentChatFeedbackItem,
  AgentChatFeedbackParams,
  AgentLogItem,
  AgentRunStatus,
  AgentRunType,
} from '@apis/agent/type'
import { exportAgentLogs } from '@apis/chat-bot'
import { Input, Controller, DatePicker } from '@bty/components'
import { localize } from '@bty/localize'
import type { MessageListProps } from '@bty/chat/ui'
import { Button, IconFont, Select, Table, Segmented } from '@/components'
import {
  rangePresets,
  SearchSelectContainer,
  agentSearchOption,
} from '@/pages/application/components/appDetail/utils'
import { POINTS_CONSUME_NAME } from '@/constants/commercialization'

import { LimitedAlertContent } from '@/features/pay/LimitedAlert'
import { useVersion } from '@/hooks/useVersion'
import { disabledOneYearDate } from '@/utils/date'
import { useLogsExport } from '@/hooks/useLogsExport'
import { useFavoriteLogs } from '@/hooks/useFavoriteLogs'
import { RunTypeGroupSelect } from '@/features/analyze/components/RunTypeGroupSelect'
import { AgentRunTypeGroup } from '@/features/analyze/constants'
import { getLastCreateTime } from '../utils'
import { LogModal } from './LogModal'
import { FeedbackDetailModal } from './FeedbackDetailModal'
import { FeedbackStatusSelect } from './FeedbackStatusSelect'

dayjs.extend(isBetween)

const PAGE_SIZE = 10
const PAGINATION_HEIGHT = 69
const TABLE_HEADER_HEIGHT = 41
const SEARCH_DATE_FORMAT = 'YYYY-MM-DD HH:mm:ss'

const StyledTabs = styled(Tabs)`
  &.ant-tabs-top > .ant-tabs-nav::before {
    border: none !important;
  }
  &.ant-tabs .ant-tabs-tab {
    font-size: 16px;
    font-weight: 500;
    padding: 0px 0px 12px 0px;
  }
  &.ant-tabs .ant-tabs-ink-bar {
    border-top-left-radius: 200px;
    border-top-right-radius: 200px;
  }
`

export const agentRunTypesMap: Record<
  AgentRunType | string,
  { label: string }
> = {
  AGENT_TESTING: {
    label: localize('agent.run_type.testing', '调试'),
  },
  MULTIPLE_MODELS: {
    label: localize('agent.run_type.multiple_models', '多模型对比'),
  },
  BATCH_TEST: {
    label: '批量测试',
  },
  CHATBOT: {
    label: localize('agent.run_type.chatbot', 'chatBot'),
  },
  CHAT_CLIENT: {
    label: localize('agent.run_type.chat_client', 'ChatClient'),
  },
  CLIENT_SMART_TOOLS: {
    label: localize('agent.run_type.smart_tools', '客户端划词'),
  },
  AGENT: {
    label: 'agent',
  },
  AGENT_OAPI: {
    label: 'API',
  },
  COPILOT: {
    label: 'copilot',
  },
  DINGTALK_STREAM_ROBOT: {
    label: localize('agent.run_type.dingtalk_stream', '钉钉 stream 机器人'),
  },
  DINGTALK_ORGAPP_ROBOT: {
    label: localize('agent.run_type.dingtalk_robot', '钉钉机器人'),
  },
  WEIXIN_KF: {
    label: localize('agent.run_type.weixin_kf', '微信客服'),
  },
  WEIXIN_MP: {
    label: localize('agent.run_type.weixin_mp', '微信公众号'),
  },
  WEIXIN_WORK_APP: {
    label: localize('agent.run_type.weixin_work', '企业微信'),
  },
  FEISHU_APP_BOT: {
    label: localize('agent.run_type.feishu_bot', '飞书机器人'),
  },
  AI_RESEARCH: {
    label: 'AI-RESEARCH',
  },
  SUB_AGENT: {
    label: localize('agent.run_type.sub_agent', '子Agent'),
  },
}

const agentRunStatusOptions: {
  label: string
  value: AgentRunStatus | 'all'
}[] = [
  {
    value: 'all',
    label: localize('agent.status.all', '全部状态'),
  },
  {
    value: 'SUCCEEDED',
    label: localize('agent.status.success', '成功'),
  },
  {
    value: 'FAILED',
    label: localize('agent.status.failed', '失败'),
  },
]

type LogTabType = 'logs' | 'feedback'

export function AgentRunLogs(props: {
  agentId: string
  appId: string
  agentName: string
  avatarInfo: MessageListProps['botAvatarInfo']
}) {
  const { agentId, appId, avatarInfo, agentName } = props
  const [searchParams, setSearchParams] = useSearchParams()
  const ref = useRef<HTMLDivElement>(null)
  const observerRef = useRef<ResizeObserver | null>(null)
  const [tableBodyHeight, setTableBodyHeight] = useState(400)
  const [page, setPage] = useState(() => Number(searchParams.get('page')) || 1)
  const [recordId, setRecordId] = useState(() => searchParams.get('record_id'))

  const [pageSize, setPageSize] = useState(
    () => Number(searchParams.get('page_size')) || PAGE_SIZE,
  )
  const [content, setContent] = useState(
    () => searchParams.get('content') || '',
  )
  const [runStatus, setRunStatus] = useState<
    (typeof agentRunStatusOptions)[number]['value']
  >(() => (searchParams.get('run_status') as AgentRunStatus | 'all') || 'all')
  const [favoriteLabel, setFavoriteLabel] = useState<'DEFAULT' | 'FAVORITE'>(
    () =>
      (searchParams.get('favorite_label') as 'DEFAULT' | 'FAVORITE') ||
      'DEFAULT',
  )
  const [runType, setRunType] = useState<AgentRunType[] | ['all']>(() => {
    const runType = searchParams.get('run_types')
    if (runType) {
      return runType.split(',') as AgentRunType[]
    }
    return []
  })
  const [searchType, setSearchType] = useState(
    () => searchParams.get('search_type') || agentSearchOption[0].value,
  )
  const searchPlaceHolder = useMemo(() => {
    return agentSearchOption?.find(item => searchType === item.value)?.label
  }, [searchType])
  const [timeRange, setTimeRange] = useState<[Dayjs | null, Dayjs | null]>(
    () => {
      const startTime = searchParams.get('start_time')
      const endTime = searchParams.get('end_time')
      if (startTime && endTime) {
        return [dayjs(startTime).startOf('d'), dayjs(endTime).endOf('d')]
      }
      return [dayjs().add(-7, 'd').startOf('d'), dayjs().endOf('day')]
    },
  )

  const { isTeam } = useVersion()

  const { exportLogs, loading: exporting } = useLogsExport({
    exportApi: exportAgentLogs,
  })
  const [logModalVisible, { setTrue: openLogModal, setFalse: closeLogModal }] =
    useBoolean(false)
  const [selectedLog, setSelectedLog] = useState<AgentLogItem | null>(null)
  const [currConversationId, setCurrConversationId] = useState(
    () => searchParams.get('conversation_id') || '',
  )

  const {
    data: logsData,
    loading,
    refreshAsync: refreshLogs,
    runAsync: fetchLogs,
  } = useRequest(
    () =>
      fetchAgentLogs({
        agent_id: agentId,
        page_no: page,
        page_size: pageSize,
        search_type: searchType,
        content,
        run_status: runStatus !== 'all' ? runStatus : undefined,
        run_types: !(isEqual(runType, ['all']) || runType?.length === 0)
          ? (runType as AgentRunType[])
          : undefined,
        start_time: timeRange[0]?.startOf('day').format(SEARCH_DATE_FORMAT),
        end_time: timeRange[1]?.endOf('day').format(SEARCH_DATE_FORMAT),
        application_id: appId,
        favorite_label: favoriteLabel,
      }),
    {
      refreshDeps: [agentId, pageSize, searchType],
      ready: !!agentId,
    },
  )
  const logs = useMemo(() => {
    return logsData?.data || []
  }, [logsData])

  const { handleFavorite, handleUnFavorite } = useFavoriteLogs({
    refresh: refreshLogs,
    applicationType: 'AGENT',
  })

  const [userAvatarInfo, setUserAvatarInfo] = useState({
    color: 'rgba(20, 171, 241, 0.12)',
    icon: '',
  })

  const updateSearchParams = useCallback(
    (updates: Record<string, string | null>) => {
      const newSearchParams = new URLSearchParams(searchParams)
      Object.entries(updates).forEach(([key, value]) => {
        if (value === null) {
          newSearchParams.delete(key)
        } else {
          newSearchParams.set(key, value)
        }
      })
      setSearchParams(newSearchParams)
    },
    [searchParams, setSearchParams],
  )

  const openConversationModal = async (conversationId: string) => {
    setCurrConversationId(conversationId)
    const log = logs.find(v => v.conversation_id === conversationId)
    setUserAvatarInfo({
      ...userAvatarInfo,
      icon: log?.user_name?.substring(0, 1) || '',
    })
    setSelectedLog(log)

    // 更新 URL 参数
    updateSearchParams({
      conversation_id: conversationId,
    })

    if (!logModalVisible) {
      openLogModal()
    } else {
      closeLogModal()
      requestAnimationFrame(() => {
        openLogModal()
      })
    }
  }

  // 关闭详情面板时清除 URL 中的 conversation_id
  const handleCloseModal = () => {
    closeLogModal()
    setSelectedLog(null)
    setRecordId(null)
    updateSearchParams({
      conversation_id: null,
    })
  }

  // 初始化时如果 URL 中有 conversation_id，则打开对应的详情面板
  useEffect(() => {
    const conversationId = searchParams.get('conversation_id')
    if (conversationId && logs.length > 0) {
      openConversationModal(conversationId)
    }
  }, [logs])

  const handleExport = () => {
    exportLogs(
      {
        agent_id: agentId,
        search_type: searchType,
        content,
        run_types: !(isEqual(runType, ['all']) || runType?.length === 0)
          ? (runType as AgentRunType[])
          : undefined,
        run_status: runStatus !== 'all' ? runStatus : undefined,
        start_time: timeRange[0]?.startOf('day').format(SEARCH_DATE_FORMAT),
        end_time: timeRange[1]?.endOf('day').format(SEARCH_DATE_FORMAT),
      },
      `${agentName}-${localize('agent.operation.run_log', '运行日志')}-${dayjs().format('YYYYMMDDHHmm')}`,
    )
  }
  const handleSelectChange = (value: string) => {
    setSearchType(value)
  }

  const changeFavorite = (conversationId: string, userFavorite: boolean) => {
    if (userFavorite) {
      handleFavorite(conversationId)
    } else {
      handleUnFavorite(conversationId)
    }
  }

  const columns: ColumnsType<AgentLogItem> = [
    {
      title: '',
      width: 45,
      key: 'favorite',
      render: (_, { conversation_id, user_favorite }) => {
        return (
          <IconFont
            name='shouzang'
            className={classNames('transition-all', {
              'opacity-0 group-hover:opacity-100 c-#8D8D99 c-op-40':
                !user_favorite,
              'opacity-100 c-#FFC300': user_favorite,
            })}
            onClick={e => {
              e.stopPropagation()
              changeFavorite(conversation_id, !user_favorite)
              if (
                logModalVisible &&
                selectedLog?.conversation_id === conversation_id
              ) {
                setSelectedLog(prev => ({
                  ...prev,
                  user_favorite: !user_favorite,
                }))
              }
            }}
          />
        )
      },
    },
    {
      title: localize('agent.column.latest_time', '最新消息时间'),
      key: 'latest_create_time',
      width: 150,
      ellipsis: true,
      render: (_, { latest_create_time }) => {
        return getLastCreateTime(latest_create_time)
      },
    },
    {
      title: localize('agent.column.status', '状态'),
      key: 'run_status',
      render: (_, { run_status }) => {
        return (
          <div
            className={classNames(
              'flex-center w-min h-20px px-14px b-rd-4px bg-op-12 text-12px font-500 whitespace-nowrap',
              {
                'bg-#2cb969 c-#2cb969': run_status === 'SUCCEEDED',
                'bg-#ff5219 c-#ff5219': run_status === 'FAILED',
              },
            )}
          >
            {run_status === 'SUCCEEDED'
              ? localize('agent.status.success', '成功')
              : localize('agent.status.failed', '失败')}
          </div>
        )
      },
    },
    {
      title: localize('agent.column.title', '标题'),
      key: 'title',
      ellipsis: true,
      dataIndex: 'title',
      width: 220,
    },
    {
      title: localize('agent.column.message_count', '消息条数'),
      key: 'total_records',
      dataIndex: 'total_records',
      ellipsis: true,
    },
    {
      title: localize(
        'agent.column.points_consume',
        `${localize('agent.column.points_consume', '消耗')}${POINTS_CONSUME_NAME}`,
      ),
      key: 'total_upgrade_consume',
      dataIndex: 'total_upgrade_consume',
      ellipsis: true,
    },
    {
      title: localize('agent.column.user', '用户'),
      key: 'user_name',
      dataIndex: 'user_name',
      render: (value, record) => {
        if (record.run_type === 'WEIXIN_MP') {
          return `${localize('agent.user.fans', '粉丝')}${value.slice(-6)}`
        }
        return value
      },
    },
    {
      title: localize('agent.column.run_type', '运行方式'),
      key: 'run_type',
      ellipsis: true,
      render: (_, { run_type }) => agentRunTypesMap[run_type]?.label,
    },
    {
      title: localize('agent.column.client_type', '客户端类型'),
      key: 'client_type',
      dataIndex: 'client_type',
      ellipsis: true,
    },
    {
      title: localize('agent.column.average_irl_time', '平均首字响应'),
      key: 'average_irl_time',
      dataIndex: 'average_irl_time',
      ellipsis: true,
      render: value => {
        return `${value} s`
      },
    },
    {
      title: localize('agent.column.average_during_time', '平均时长'),
      key: 'average_during_time',
      dataIndex: 'average_during_time',
      ellipsis: true,
      render: value => {
        return `${value} s`
      },
    },
    {
      title: localize('agent.column.operation', '操作'),
      render: (_, { conversation_id }) => {
        return (
          <div
            className='c-#7b61ff cursor-pointer'
            onClick={e => {
              e.stopPropagation()
              openConversationModal(conversation_id)
            }}
          >
            {localize('agent.operation.view_detail', '查看详情')}
          </div>
        )
      },
    },
  ]

  useLayoutEffect(() => {
    const node = ref.current
    if (node) {
      observerRef.current = new ResizeObserver(entries => {
        for (const entry of entries) {
          if (entry.target === node) {
            const { height } = node.getBoundingClientRect()
            const paginationEle = node.querySelector('#agent_logs_pagination')
            const tableHeaderEle = node.querySelector('.ant-table-header')
            const pH = paginationEle
              ? paginationEle.getBoundingClientRect().height
              : PAGINATION_HEIGHT
            const hH = tableHeaderEle
              ? tableHeaderEle.getBoundingClientRect().height
              : TABLE_HEADER_HEIGHT
            setTableBodyHeight(height - pH - hH)
          }
        }
      })
      observerRef.current.observe(node)
    }
  }, [ref])

  useUnmount(() => {
    if (observerRef.current && ref.current) {
      observerRef.current.unobserve(ref.current)
    }
  })

  useUpdateEffect(() => {
    // 筛选项变化切换到第一页请求
    if (page !== 1) {
      setPage(1)
    } else {
      refreshLogs()
    }
  }, [timeRange, runStatus, runType, favoriteLabel])

  useUpdateEffect(() => {
    refreshLogs()
  }, [page])

  useDebounceEffect(
    () => {
      // 搜索内容变化时，只有当不是初始化加载时才重置页码
      if (content && page !== 1) {
        setPage(1)
      } else {
        fetchLogs()
      }
    },
    [content],
    {
      wait: 500,
    },
  )

  // 初始化时执行一次请求
  useEffect(() => {
    if (agentId) {
      fetchLogs()
    }
  }, [agentId])

  // 更新 URL 参数的函数

  // 修改各个状态时同步更新 URL
  useEffect(() => {
    updateSearchParams({
      page: page.toString(),
      page_size: pageSize.toString(),
      content,
      search_type: searchType,
      run_status: runStatus,
      favorite_label: favoriteLabel,
      run_types: runType.length > 0 ? runType.join(',') : null,
      start_time: timeRange[0]?.format('YYYY-MM-DD') || null,
      end_time: timeRange[1]?.format('YYYY-MM-DD') || null,
    })
  }, [
    page,
    pageSize,
    content,
    searchType,
    runStatus,
    favoriteLabel,
    runType,
    timeRange,
    updateSearchParams,
  ])

  return (
    <div className='flex flex-col h-full bg-#fff b-rd-12px'>
      <div className='flex-center-between gap-12px m-b-20px'>
        <SearchSelectContainer>
          <Select
            options={agentSearchOption}
            style={{ width: 72 }}
            value={searchType}
            onChange={handleSelectChange}
          />
          <Input
            overrides={{ root: 'w-328px rounded-l-0' }}
            placeholder={`${localize('agent.search.placeholder', '搜索')}${searchPlaceHolder}`}
            icon='search'
            value={content}
            onChange={e => {
              setContent(e.target.value)
            }}
          />
        </SearchSelectContainer>
        <div className='flex-center-between gap-12px'>
          <Segmented
            className='h-36px [&_.ant-segmented-item]:py-8! [&_.ant-segmented-item]:px-12!  [&_.ant-segmented-item-label]:text-14px/16px [&_.ant-segmented-item-label]:p-0!'
            size='small'
            value={favoriteLabel}
            onChange={v => setFavoriteLabel(v as 'DEFAULT' | 'FAVORITE')}
            options={[
              { label: localize('agent.filter.all', '全部'), value: 'DEFAULT' },
              {
                label: localize('agent.filter.favorite', '收藏'),
                value: 'FAVORITE',
              },
            ]}
          />
          <Select
            className='min-w-140px'
            size='middle'
            placeholder={localize('agent.filter.all_status', '全部状态')}
            options={agentRunStatusOptions}
            value={runStatus}
            onChange={v => setRunStatus(v)}
          />
          <RunTypeGroupSelect
            data={AgentRunTypeGroup}
            onTypeChange={v => setRunType(v as AgentRunType[])}
          />
          <DatePicker.Range
            overrides={{ root: 'h-36px' }}
            disabledDate={isTeam ? disabledOneYearDate : undefined}
            showTime={false}
            // 因为 agent_log 的接口限制，这里去掉过去一年的选项
            presets={rangePresets.slice(0, 4)}
            format='YYYY-MM-DD'
            size='large'
            value={timeRange}
            allowClear={false}
            placement='bottomRight'
            onChange={dates => {
              dates
                ? setTimeRange([dates[0], dates[1]])
                : setTimeRange([null, null])
            }}
          />
          <Button
            loading={exporting}
            onClick={handleExport}
            disabled={favoriteLabel === 'FAVORITE'}
          >
            {localize('agent.operation.export', '导出')}
          </Button>
        </div>
      </div>
      <div className='flex-1 overflow-hidden' ref={ref}>
        <Table
          rowKey='conversation_id'
          columns={columns}
          dataSource={logs}
          loading={loading}
          scroll={{
            y: `${tableBodyHeight}px`,
          }}
          rowClassName={record =>
            classNames('cursor-pointer group', {
              'bg-#7B61FF bg-op-08 !important':
                record.conversation_id === currConversationId,
            })
          }
          onRow={record => ({
            onClick: () => {
              openConversationModal(record.conversation_id)
            },
          })}
          pagination={false}
        />
        <div
          className='flex flex-1 pt-20 items-center justify-between border-t border-solid border-font_1 border-op-16 '
          id='agent_logs_pagination'
        >
          <span>
            <LimitedAlertContent
              prefix={localize(
                'agent.alert.team_limit',
                '团队版仅可查看近1年的日志',
              )}
              shouldCheckTeamVersion
            />
          </span>
          <Pagination
            {...{
              current: page,
              total: logsData?.total || 0,
              pageSize,
              showSizeChanger: logsData?.total > 10,
              onChange: (page, size) => {
                setPage(page)
                setPageSize(size)
              },
            }}
          />
        </div>
      </div>
      {logModalVisible && (
        <LogModal
          agentId={agentId}
          appId={appId}
          conversationId={currConversationId}
          recordId={recordId}
          onClose={handleCloseModal}
          selectedLog={selectedLog}
          robotAvatarInfo={avatarInfo}
        />
      )}
    </div>
  )
}

function getAgentChatFeedBackListFormatted(params: AgentChatFeedbackParams) {
  return getAgentChatFeedBackList(params).then(res => {
    return {
      list: res.data || [],
      total: res.total || 0,
    }
  })
}

const feedbackStatusOptions: {
  label: string
  value: AgentFeedbackStatusEn | 'all'
}[] = [
  {
    value: 'all',
    label: localize('agent.feedback.status.all', '全部状态'),
  },
  {
    value: AgentFeedbackStatusEn.Processed,
    label: localize('agent.feedback.status.processed', '已处理'),
  },
  {
    value: AgentFeedbackStatusEn.Pending,
    label: localize('agent.feedback.status.pending', '待处理'),
  },
  {
    value: AgentFeedbackStatusEn.Ignored,
    label: localize('agent.feedback.status.ignored', '不处理'),
  },
]

function Feedback({
  appId,
  agentName,
  avatarInfo,
}: {
  appId: string
  agentName: string
  avatarInfo: MessageListProps['botAvatarInfo']
}) {
  const ref = useRef<HTMLDivElement>(null)
  const paginationRef = useRef<HTMLDivElement>(null)
  const [tableBodyHeight, setTableBodyHeight] = useState(400)
  const [selectedFeedbackId, setSelectedFeedbackId] = useState<string | null>(
    null,
  )
  const [feedbackModalVisible, { toggle: toggleFeedbackModal }] =
    useBoolean(false)
  const [form] = Form.useForm()
  const {
    tableProps,
    search,
    refresh,
    params: tableParams,
  } = useAntdTable(
    (
      { current, pageSize },
      formData: {
        keyword?: string
        content?: string
        response?: string
        user_opinion?: string
        status: AgentFeedbackStatusEn | 'all' | undefined
        timeRange: [dayjs.Dayjs, dayjs.Dayjs]
      },
    ) => {
      const searchParams: {
        app_id: string
        page_no: number
        page_size: number
        status?: AgentFeedbackStatusEn
        start_time: string
        end_time: string
        content?: string
        response?: string
        user_opinion?: string
      } = {
        app_id: appId,
        page_no: current,
        page_size: pageSize,
        user_opinion: formData?.user_opinion,
        status: formData?.status === 'all' ? undefined : formData?.status,
        start_time: formData.timeRange[0]
          ?.startOf('day')
          .format(SEARCH_DATE_FORMAT),
        end_time: formData.timeRange[1]
          ?.endOf('day')
          .format(SEARCH_DATE_FORMAT),
      }

      return getAgentChatFeedBackListFormatted(searchParams)
    },
    {
      form,
      defaultType: 'simple',
      defaultParams: [
        { current: 1, pageSize: PAGE_SIZE },
        {
          status: 'all',
          timeRange: [dayjs().add(-7, 'd').startOf('d'), dayjs().endOf('day')],
        },
      ],
    },
  )

  const { submit } = search

  const { run: handleFeedback } = useRequest(fetchHandleFeedback, {
    manual: true,
    onSuccess: () => {
      refresh()
    },
  })

  const { exportLogs: exportFeedbackList, loading: exporting } = useLogsExport({
    exportApi: exportAgentChatFeedBackList,
  })

  const handleExport = () => {
    const searchValue = tableParams[1]!
    exportFeedbackList(
      {
        app_id: appId,
        status: searchValue?.status === 'all' ? undefined : searchValue?.status,
        start_time: searchValue?.timeRange[0]?.format(SEARCH_DATE_FORMAT),
        end_time: searchValue?.timeRange[1]?.format(SEARCH_DATE_FORMAT),
      },
      `${agentName}-${localize('agent.operation.feedback', '点踩反馈')}-${dayjs().format('YYYYMMDDHHmm')}`,
    )
  }

  const handleStatusChange = (
    status: string,
    record: AgentChatFeedbackItem,
  ) => {
    handleFeedback({
      feedback_id: record.feedback_id,
      status,
    })
  }

  // 定义反馈表格的列
  const columns: ColumnsType<AgentChatFeedbackItem> = [
    {
      title: localize('agent.column.feedback_content', '反馈意见内容'),
      dataIndex: 'user_opinion',
      ellipsis: true,
      width: '70%',
      render: user_opinion => {
        return user_opinion || '-'
      },
    },
    {
      title: localize('agent.column.feedback_user', '反馈人'),
      dataIndex: 'creator_name',
      width: 250,
    },
    {
      title: localize('agent.column.update_time', '更新时间'),
      dataIndex: 'updated_at',
      width: 180,
      render: updated_at => {
        return dayjs(updated_at).format('YYYY-MM-DD HH:mm:ss')
      },
    },
    {
      title: localize('agent.column.operation', '操作'),
      dataIndex: 'status',
      width: 100,
      render: (status, record) => {
        return (
          <div onClick={e => e.stopPropagation()}>
            <FeedbackStatusSelect
              value={status}
              onChange={(value: AgentFeedbackStatusEn) =>
                handleStatusChange(value, record)
              }
            />
          </div>
        )
      },
    },
  ]

  useLayoutEffect(() => {
    const node = ref.current
    if (!node) return

    const updateTableBodyHeight = () => {
      const { height } = node.getBoundingClientRect()
      const paginationEle = paginationRef.current
      const tableHeaderEle = node.querySelector('.ant-table-header')
      const paginationHeight =
        paginationEle?.getBoundingClientRect().height || PAGINATION_HEIGHT
      const tableHeaderHeight =
        tableHeaderEle?.getBoundingClientRect().height || TABLE_HEADER_HEIGHT
      setTableBodyHeight(height - paginationHeight - tableHeaderHeight)
    }

    const resizeObserver = new ResizeObserver(updateTableBodyHeight)
    resizeObserver.observe(node)

    updateTableBodyHeight()

    return () => resizeObserver.disconnect()
  }, [ref])

  const { run: inputSubmit } = useDebounceFn(submit, { wait: 500 })

  const openFeedbackModal = (feedbackId: string) => {
    setSelectedFeedbackId(feedbackId)
    if (!feedbackModalVisible) {
      toggleFeedbackModal()
    }
  }

  return (
    <div className='flex flex-col h-full'>
      <Form form={form} className='flex-center-between gap-12px'>
        <Controller.Input
          name='user_opinion'
          overrides={{ root: 'w-328px' }}
          placeholder={localize('agent.filter.search_feedback', '搜索反馈意见')}
          icon='search'
          onChange={inputSubmit}
        />
        <div className='flex-center-between gap-12px'>
          <Form.Item name='status'>
            <Select
              className='min-w-140px'
              size='middle'
              placeholder={localize('agent.filter.all_status', '全部状态')}
              options={feedbackStatusOptions}
              onChange={submit}
            />
          </Form.Item>
          <Form.Item name='timeRange'>
            <DatePicker.Range
              overrides={{ root: 'h-36px' }}
              showTime={false}
              presets={rangePresets}
              format='YYYY-MM-DD'
              onChange={submit}
            />
          </Form.Item>
          <Form.Item>
            <Button loading={exporting} onClick={handleExport}>
              {localize('agent.operation.export', '导出')}
            </Button>
          </Form.Item>
        </div>
      </Form>
      <div className='flex-1 overflow-hidden' ref={ref}>
        <Table
          rowKey='feedback_id'
          columns={columns}
          rowClassName='cursor-pointer'
          scroll={{
            y: `${tableBodyHeight}px`,
          }}
          onRow={record => ({
            onClick: () => {
              openFeedbackModal(record.feedback_id)
            },
          })}
          {...tableProps}
          pagination={false}
        />
        <div
          className='flex flex-1 pt-20 items-center justify-between border-t border-solid border-font_1 border-op-16'
          ref={paginationRef}
        >
          <span>{/* 如果需要，可以添加类似的提示信息 */}</span>
          <Pagination
            current={tableProps.pagination.current}
            total={tableProps.pagination.total || 0}
            pageSize={tableProps.pagination.pageSize}
            onChange={(page, pageSize) => {
              tableProps.onChange?.({
                current: page,
                pageSize,
              })
            }}
          />
        </div>
      </div>
      {feedbackModalVisible && selectedFeedbackId && (
        <FeedbackDetailModal
          key={selectedFeedbackId}
          avatarInfo={avatarInfo}
          onClose={() => {
            toggleFeedbackModal()
            setSelectedFeedbackId(null)
          }}
          feedbackId={selectedFeedbackId}
          refresh={refresh}
        />
      )}
    </div>
  )
}

export function Logs(props: {
  agentId: string
  appId: string
  agentName: string
  avatarInfo: MessageListProps['botAvatarInfo']
}) {
  const { agentId, appId, agentName, avatarInfo } = props
  const [activeTab, setActiveTab] = useState<LogTabType>('logs')

  const items: TabsProps['items'] = [
    {
      key: 'logs',
      label: localize('agent.tab.logs', '日志'),
    },
    {
      key: 'feedback',
      label: localize('agent.tab.feedback', '反馈'),
    },
  ]

  return (
    <div className='flex flex-col h-full p-20px bg-#fff b-rd-12px'>
      <StyledTabs
        activeKey={activeTab}
        items={items}
        onChange={(key: string) => setActiveTab(key as LogTabType)}
      />
      {activeTab === 'logs' && (
        <AgentRunLogs
          agentId={agentId}
          appId={appId}
          agentName={agentName}
          avatarInfo={avatarInfo}
        />
      )}
      {activeTab === 'feedback' && (
        <Feedback appId={appId} agentName={agentName} avatarInfo={avatarInfo} />
      )}
    </div>
  )
}
