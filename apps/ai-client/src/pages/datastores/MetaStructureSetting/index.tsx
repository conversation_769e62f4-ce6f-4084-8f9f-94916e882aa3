import { localize } from '@bty/localize'
import { Drawer } from '@bty/components'
import { Tooltip } from 'antd'
import { Button, IconFont } from '@/components'
import { SettingContent } from './SettingContent'

export interface MetaAttribute {
  id: string
  name: string
  type: string
  isNew?: boolean
  isEdit?: boolean
  isDefault?: boolean
}

export function MetaPanel({
  partition_id,
  disabled,
}: {
  partition_id: number
  disabled: boolean
}) {
  return (
    <Drawer destroyOnClose>
      <Drawer.Trigger slot>
        <Tooltip title={localize('datastores.preview_source', '元属性')}>
          <Button disabled={disabled} className='px-10px!' type='default'>
            <IconFont name='yuanzhuxing' className='text-16px' />
          </Button>
        </Tooltip>
      </Drawer.Trigger>
      <Drawer.Content width={600}>
        <div className='flex-0 flex items-center px-24px py-16px text-16px leading-[1.5] border-bottom border-solid border-line'>
          <Drawer.Close>
            <IconFont name='guanbi' />
          </Drawer.Close>
          <span className='leading-[1.5] text-font font-600 ml-8px'>
            元属性管理
          </span>
        </div>
        <SettingContent partitionId={partition_id} />
      </Drawer.Content>
    </Drawer>
  )
}
