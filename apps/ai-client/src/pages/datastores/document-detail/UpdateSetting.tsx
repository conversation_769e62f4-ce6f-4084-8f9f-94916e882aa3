import {
  DatePicker,
  InputNumber,
  Popover,
  Space,
  TimePicker,
  Tooltip,
} from 'antd'
import { memo, useState } from 'react'
import { localize } from '@bty/localize'
import dayjs from 'dayjs'
import { useMemoizedFn } from 'ahooks'
import { Button, Select, IconFont } from '@/components'
import {
  REGULAR_ENUM,
  REGULAR_TIME_OPTIONS,
} from '@/features/datastore/components/ThirdPartDocument/feishu'

enum REPEAT_ENUM {
  YES = 'YES',
  NO = 'NO',
}

const REPEAT_OPTIONS = [
  { value: REPEAT_ENUM.YES, label: '重复' },
  { value: REPEAT_ENUM.NO, label: '不重复' },
]

interface UpdateSettingConfigProps {
  onCancel: () => void
}

const UpdateSettingConfig = memo((props: UpdateSettingConfigProps) => {
  const { onCancel } = props

  const [type, setType] = useState(REGULAR_ENUM.NONE)
  const [reType, setReType] = useState(REPEAT_ENUM.YES)

  return (
    <div className='w-600px'>
      <div className='flex gap-8px items-center h-48px b-b-1px b-[#E1E1E5]/60 px-16px'>
        <span className='font-500 text-16px'>更新设置</span>
        <span className='flex-center gap-4px text-[#8D8D99] mr-auto text-12px/14px'>
          <IconFont name='xiaoshizhi' className='text-14px' />
          更新会覆盖当前文档内容
        </span>
        <span
          className='cursor m-[-4px] p-4px cursor-pointer hover:bg-bg_3/8 rd-4px'
          onClick={onCancel}
        >
          <IconFont name='guanbi' className='text-16px' />
        </span>
      </div>
      <div className='p-24px'>
        <div>
          <p className='mb-12px font-500 text-14px/16px'>设置更新频率</p>
          <Select
            className='w-full'
            value={type}
            options={REGULAR_TIME_OPTIONS}
            onChange={setType}
          />
        </div>

        {type === REGULAR_ENUM.CUSTOM && (
          <div className='flex mt-12px gap-12px'>
            <div className='flex-1'>
              <Select
                className='size-full'
                value={reType}
                options={REPEAT_OPTIONS}
                onChange={setReType}
              />
            </div>
            {reType === REPEAT_ENUM.YES && (
              <div className='flex-1'>
                <InputNumber
                  className='size-full [&_.ant-input-number-suffix]:mr-11px! [&_.ant-input-number-input]:text-center! bg-[#626999]/6 hover:bg-[#626999]/6 focus-within:bg-[#626999]/6 b-transparent'
                  prefix={<span className='text-[#8D8D99]'>每</span>}
                  suffix={<span className='text-[#8D8D99]'>天</span>}
                  min={1}
                  max={365}
                  controls={false}
                  precision={0}
                />
              </div>
            )}
            {reType === REPEAT_ENUM.NO && (
              <div className='flex-1'>
                <DatePicker
                  className='size-full bg-[#626999]/6 focus-within:bg-[#626999]/6 hover:bg-[#626999]/6 b-transparent'
                  allowClear={false}
                  defaultValue={dayjs()}
                />
              </div>
            )}
            <div className='flex-1'>
              <TimePicker
                className='size-full bg-[#626999]/6 focus-within:bg-[#626999]/6 hover:bg-[#626999]/6 b-transparent'
                allowClear={false}
                defaultValue={dayjs('12:08', 'HH:mm')}
                format={'HH:mm'}
              />
            </div>
          </div>
        )}

        <div className='flex gap-12px justify-end mt-24px'>
          <Button onClick={onCancel}>取消</Button>
          <Button type='primary'>完成</Button>
        </div>
      </div>
    </div>
  )
})

export const UpdateSetting = memo(() => {
  const [open, setOpen] = useState(false)
  const [tipOpen, setTipOpen] = useState(false)

  const handleOpen = useMemoizedFn((newOpen: boolean) => {
    setOpen(newOpen)
    setTipOpen(false)
  })

  const handleTipOpen = useMemoizedFn((newOpen: boolean) => {
    if (open) return
    setTipOpen(newOpen)
  })

  const handleCancel = useMemoizedFn(() => {
    setOpen(false)
  })

  return (
    <Space.Compact>
      <Button icon={<IconFont name='tongshi' className='text-16px' />}>
        {localize('datastores.update_content', '更新内容')}
      </Button>
      <Popover
        open={open}
        trigger='click'
        placement='bottomRight'
        arrow={false}
        content={<UpdateSettingConfig onCancel={handleCancel} />}
        rootClassName='[&_.ant-popover-inner]:p-0px!'
        onOpenChange={handleOpen}
      >
        <Tooltip
          trigger='hover'
          open={tipOpen}
          onOpenChange={handleTipOpen}
          title={localize('datastores.update_setting', '更新设置')}
        >
          <Button className='px-10px!'>
            <IconFont name='shezhi' className='text-16px' />
          </Button>
        </Tooltip>
      </Popover>
    </Space.Compact>
  )
})
