import { Tag, Tooltip, Space, Divider } from 'antd'
import { useNavigate } from 'react-router-dom'
import { clone } from 'lodash-es'
import { useRequest, useLockFn } from 'ahooks'
import { DocumentType, DocumentStatus } from '@apis/datastore/model'
import { updateDocument, exportFaqContent } from '@apis/datastore'
import type { DocumentItem } from '@apis/datastore/type'
import { localize } from '@bty/localize'
import { DocumentNameEdit } from '@/pages/datastores/components/DocumentNameEdit.tsx'
import { IconFont, Button, ConfirmModal } from '@/components'
import { useModal } from '@/hooks/useModal.tsx'
import { SearchableTagEditorModal } from '@/pages/datastores/components/SearchableTagEditorModal'
import { Routers } from '@/pages/datastores/document-detail/Layout.tsx'

import { TagWithHover } from '@/features/datastore/components/DocumentTagWithHover'

import { DocumentTagList } from '@/features/datastore/components/DocumentTagList'
import {
  DATASTORE_MAX_TAG_COUNT,
  fileTypeMimeMap,
} from '@/features/datastore/constant'
import { useTrack } from '@/features/track/Track'
import { Permissions } from '@/components/acl'
import theme from '@/constants/theme'
import { getDatastorePath } from '@/router/datastore'

interface HeaderProps {
  documentInfo: DocumentItem
  workspaceId: string
  dataset_id: number
  dataLoading: boolean
  saveLoading: boolean
  onTagsChange: () => void
  onRetry: () => void
  hasUnSaveData: boolean
  onSave: () => void
  toContent: () => void
  route: string
  handleGenerateAISummary: (id?: number) => void
  isEditing?: boolean
}

export default function Header(props: HeaderProps) {
  const {
    dataset_id,
    workspaceId,
    hasUnSaveData,
    saveLoading,
    dataLoading,
    onTagsChange,
    onRetry,
    route,
    toContent,
    handleGenerateAISummary,
    documentInfo,
    isEditing,
    onSave,
  } = props

  const {
    mimetype,
    file_id,
    file_name,
    file_type,
    tags,
    status,
    failed_reason = '',
  } = documentInfo

  const navigate = useNavigate()

  const [tagEditorModal] = useModal(SearchableTagEditorModal)

  const { run: updateDocumentApi } = useRequest(updateDocument, {
    manual: true,
  })
  const { runAsync: exportFaqTemplate } = useRequest(exportFaqContent, {
    manual: true,
  })
  const handleExportFaq = useLockFn(async () => {
    try {
      const fileName =
        file_name?.substring(0, file_name.lastIndexOf('.')) || file_name
      await exportFaqTemplate(file_id, fileName)
    } catch (error) {
      console.error(
        localize('datastores.export_qa_failed', '导出问答内容失败 :'),
        error,
      )
    }
  })
  const viewStatus = saveLoading ? DocumentStatus.Ing : status
  const { doTrack } = useTrack()

  const handleEditTag = () => {
    tagEditorModal.open({
      applicationType: 'DATASET',
      title: tags?.length
        ? localize('datastores.edit_tag', '编辑标签')
        : localize('datastores.add_tag', '添加标签'),
      defaultValue: tags?.map(item => ({
        label: item,
        color: theme.colors.primary,
        textColor: '#fff',
        id: item,
      })),
      documentId: file_id,
      datasetId: dataset_id,
      onFinish: onTagsChange,
    })
  }

  const [confirmModal] = useModal(ConfirmModal, {
    okButtonProps: {
      danger: false,
      loading: saveLoading,
    },
  })

  const handleBack = () => {
    const path = getDatastorePath.documents(workspaceId, String(dataset_id))
    navigate(`${path}${location.search}`)
  }

  const onBack = () => {
    if (isEditing && hasUnSaveData) {
      confirmModal.open({
        title: localize(
          'datastores.save_confirm',
          '修改的内容尚未保存，请确认是否保存？',
        ),
        okText: localize('datastores.save', '保存'),
        cancelText: localize('datastores.cancel_exit', '取消并退出'),
        onOk: async () => {
          await onSave()
          doTrack('datastore_doc_save', { type: 'back_save' })
          confirmModal.close()
          setTimeout(() => {
            handleBack()
          }, 200)
        },
        onCancel: () => {
          handleBack()
        },
      })
    } else {
      handleBack()
    }
  }

  return (
    <div className='pl-16 pr-24 bg-white py-17.5px flex-center-between flex-wrap gap-8 b-b b-line b-op-60'>
      <div className='flex items-center flex-1'>
        <span
          onClick={onBack}
          className='mr-12 w-32px h-32px hover:bg-bg_3 hover:bg-op-8 rounded-6px flex-center cursor-pointer'
        >
          <IconFont name='fanhui' className='text-16px' />
        </span>
        <Permissions
          className='pointer-events-none'
          resource={{
            type: 'DATASET',
            id: [dataset_id, documentInfo.partition_group_id],
          }}
          noRenderWhenReadonly={false}
        >
          <DocumentNameEdit
            mimetype={mimetype}
            file_name={file_name}
            file_id={file_id}
            file_type={file_type}
            onSave={updateDocumentApi}
          />
          <div className='ml-12 font-400 shrink-0'>
            <div className='flex items-center gap-8 w-100%'>
              <div className='flex flex-1 items-center overflow-hidden ml-8px'>
                {(tags?.length || 0) < DATASTORE_MAX_TAG_COUNT && (
                  <Tag
                    className='rounded-4px b-dashed bg-white hover:bg-bg_3 hover:bg-op-8 cursor-pointer ,'
                    onClick={() => handleEditTag()}
                  >
                    {localize('datastores.add_tag_btn', '添加标签')}
                  </Tag>
                )}
                <DocumentTagList
                  className='h-22px! !text-12px'
                  list={clone?.(tags || [])?.reverse() || []}
                  showMaxCount={
                    (tags?.length || 0) < DATASTORE_MAX_TAG_COUNT ? 4 : 6
                  }
                  onClick={() => {
                    handleEditTag()
                  }}
                />
              </div>
            </div>
          </div>
        </Permissions>
      </div>
      <Space size={12}>
        <TagWithHover
          status={viewStatus}
          className='rounded-4px font-500 text-12px min-w-52px mr-0 text-center ml-12 cursor-pointer relative'
          progress={documentInfo?.progress?.percentage}
          tooltip={
            ((viewStatus === DocumentStatus.Fail ||
              viewStatus === DocumentStatus.Warning) &&
              failed_reason) ||
            ''
          }
          onClick={() => {
            if (viewStatus === DocumentStatus.Fail) {
              onRetry()
            }
          }}
        />
        {viewStatus === DocumentStatus.Warning && (
          <Tooltip title='一键AI处理所有异常段落'>
            {!isEditing && (
              <div className='w-24px h-24px m-l-[-8px] m-r-[-8px] cursor-pointer hover:bg-[rgba(98,105,153,0.08)] rounded-4px flex justify-center items-center text-16px/16px'>
                <IconFont
                  name='AIcengjiangchuli'
                  onClick={async () => {
                    await handleGenerateAISummary()
                  }}
                />
              </div>
            )}
          </Tooltip>
        )}
        {(file_type === DocumentType.FILE ||
          status === DocumentStatus.Done) && (
          <Divider type='vertical' className='mx-8' />
        )}

        {/* <UpdateSetting /> */}

        {mimetype !== fileTypeMimeMap.msg && (
          <Tooltip title={localize('datastores.preview_source', '元属性')}>
            <Button
              className='px-10px!'
              onClick={() => {
                const path = getDatastorePath.documentDetail(
                  workspaceId,
                  String(dataset_id),
                  String(file_id),
                  route === Routers.Meta ? Routers.Content : Routers.Meta,
                )
                navigate(`${path}${location.search}`)
              }}
            >
              <IconFont name='yuanzhuxing' className='text-16px' />
            </Button>
          </Tooltip>
        )}

        {(file_type === DocumentType.FILE ||
          file_type === DocumentType.VIDEO) &&
          mimetype !== fileTypeMimeMap.msg && (
            <Tooltip
              title={localize('datastores.preview_source', '预览源文件')}
            >
              <Button
                className='px-10px!'
                onClick={() => {
                  const path = getDatastorePath.documentDetail(
                    workspaceId,
                    String(dataset_id),
                    String(file_id),
                    route === Routers.SourcePreview
                      ? Routers.Content
                      : Routers.SourcePreview,
                  )
                  navigate(`${path}${location.search}`)
                }}
              >
                <IconFont name='yulanyuanwenjian1x' className='text-16px' />
              </Button>
            </Tooltip>
          )}

        {(file_type === DocumentType.FEISHU ||
          file_type === DocumentType.DING) && (
          <Tooltip
            title={localize('datastores.preview_source_link', '查看源文档')}
          >
            <Button
              className='px-10px!'
              onClick={() => {
                window.open(documentInfo.file_url, '_blank')
              }}
            >
              <IconFont name='yulanyuanwenjian1x' className='text-16px' />
            </Button>
          </Tooltip>
        )}

        <Permissions
          resource={{
            type: 'DATASET',
            id: [documentInfo.partition_id, documentInfo.partition_group_id],
          }}
        >
          {(status === DocumentStatus.Done ||
            status === DocumentStatus.Warning) &&
            DocumentType.QA !== file_type &&
            DocumentType.VIDEO !== file_type && (
              <Tooltip title={localize('datastores.resplit', '重新分段')}>
                <Button
                  className='px-10px!'
                  onClick={() => {
                    if (route === Routers.Split) {
                      toContent()
                    } else {
                      const path = getDatastorePath.documentDetail(
                        workspaceId,
                        String(dataset_id),
                        String(file_id),
                        Routers.Split,
                      )
                      navigate(`${path}${location.search}`)
                    }
                  }}
                  disabled={dataLoading || props.isEditing}
                >
                  <IconFont name='tongxinfenduan' className='text-16px' />
                </Button>
              </Tooltip>
            )}
        </Permissions>
        {status === DocumentStatus.Done && file_type === DocumentType.QA && (
          <Tooltip title={localize('datastores.export_qa', '导出问答')}>
            <Button className='px-10px!' onClick={handleExportFaq}>
              <IconFont name='daochu-1' className='text-16px' />
            </Button>
          </Tooltip>
        )}
        {(status === DocumentStatus.Done ||
          status === DocumentStatus.Warning ||
          status === DocumentStatus.AIProcess) && (
          <Tooltip
            placement='bottom'
            title={
              hasUnSaveData
                ? '请先保存'
                : localize('datastores.hit_test', '命中测试')
            }
          >
            <Button
              className='px-10px!'
              disabled={hasUnSaveData || props.isEditing}
              onClick={() => {
                if (route === Routers.HitTest) {
                  toContent()
                } else {
                  const path = getDatastorePath.documentDetail(
                    workspaceId,
                    String(dataset_id),
                    String(file_id),
                    Routers.HitTest,
                  )
                  navigate(`${path}${location.search}`)
                }
              }}
            >
              <IconFont name='mingzhong' className='text-16px' />
            </Button>
          </Tooltip>
        )}
      </Space>
    </div>
  )
}
