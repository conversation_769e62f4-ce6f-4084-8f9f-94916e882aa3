import { useState } from 'react'
import classNames from 'classnames'
import { message } from 'antd'
import { DocumentType } from '@apis/datastore/model'
import type { DocumentUpdateRequest } from '@apis/datastore/type'
import { localize } from '@bty/localize'
import { Input } from '@bty/components'
import { IconFont } from '@/components'
import {
  getFileDisplayName,
  getFileIconByFileTypeOrMimeType,
} from '@/features/datastore/utils'
import feishuTip from '@/assets/dataset/feishu-tip.svg'

interface DocumentNameEditProps {
  className?: string
  innerClassName?: string
  file_id: number
  file_name: string
  mimetype: string
  file_type: DocumentType
  inputClassName?: string
  onSave: (params: DocumentUpdateRequest) => void
}

export function DocumentNameEdit(props: DocumentNameEditProps) {
  const {
    className,
    file_name,
    file_id,
    mimetype,
    file_type,
    onSave,
    innerClassName,
    inputClassName,
  } = props
  const [isEdit, setIsEdit] = useState(false)
  const [value, setValue] = useState(file_name)
  const [loading, setLoading] = useState(false)

  const onConfirm = async () => {
    if (value.trim()) {
      try {
        setLoading(true)
        await onSave({
          file_id,
          file_name: value,
        })
        setIsEdit(false)
        message.success(localize('datastores.save_success', '保存成功'))
      } finally {
        setLoading(false)
      }
    }
  }

  if (isEdit) {
    return (
      <Input
        overrides={{
          root: classNames('flex-1 max-w-600px h-24px', inputClassName, {
            '!border-error': !value.trim(),
          }),
        }}
        size='small'
        loading={loading}
        autoFocus
        value={value}
        maxLength={255}
        showCount
        onBlur={onConfirm}
        onPressEnter={onConfirm}
        placeholder={localize('datastores.input_title', '请输入标题')}
        onChange={e => setValue(e.target.value)}
      />
    )
  }

  return (
    <div
      className={classNames(
        'flex items-center py-6 rounded-4px transition-all ease-in-out hover:bg-bg_3 hover:bg-op-12 hover:px-12 group/name',
        className,
      )}
      title={value}
      onClick={() => setIsEdit(true)}
    >
      <img
        className='w-20px h-24px shrink-0 mr-12'
        src={getFileIconByFileTypeOrMimeType(mimetype, file_type)}
        alt=''
      />
      <span
        className={classNames(
          'cursor-pointer text-20px font-600 truncate max-w-40vw',
          innerClassName,
        )}
      >
        {getFileDisplayName(value, file_type)}
      </span>
      {file_type === DocumentType.FEISHU && (
        <img className='size-16px ml-8px shrink-0' src={feishuTip} alt='' />
      )}
      <IconFont
        name='bianji'
        className='c-bg_3 !w-0 transition-all ease-in-out op-0 h-fit shrink-0 c-op-60 text-14px relative mt-2 group-hover/name:opacity-100 group-hover/name:!w-16px group-hover/name:ml-8'
      />
    </div>
  )
}
