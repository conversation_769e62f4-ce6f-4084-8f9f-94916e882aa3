import type { InviteMembersRequestBody } from '@apis/authority/type.ts'
import {
  EDITOR_FIELD,
  READ_ONLY_FIELD,
  RoleCode,
} from '@apis/authority/type.ts'
import type { AppResourceAclType } from '@apis/acl/type.ts'

interface GeneralInviteParamsPayloads {
  appResourceAcl: AppResourceAclType
  roleCode: RoleCode // 邀请用户的角色
  authAppSelectEnable: boolean // 是否支持授权应用选择
  selectedAgentApps?: string[] // 手动选择的agent授权应用
  selectedFlowApps?: string[] // 手动选择的flow授权应用
  selectedDatabaseApps?: string[] // 手动选择的database授权应用
  selectedDatasetApps?: string[] // 手动选择的dataset授权应用
  isAuthAllAgentAndFlow?: boolean // 是否授权了全部的agent和flow
  isAuthAllDatabaseAndDataset?: boolean // 是否授权了全部的database和dataset
  isAgent?: boolean
  applicationId?: string // 当前所在的应用的应用id
}

type Permission = Omit<
  InviteMembersRequestBody,
  'phones' | 'role' | 'workspaceId' | 'url'
>

export function generalInviteParams(
  payload: GeneralInviteParamsPayloads,
): Permission {
  const {
    appResourceAcl,
    roleCode,
    authAppSelectEnable,
    isAgent = false,
    applicationId,
    selectedAgentApps = [],
    selectedFlowApps = [],
    selectedDatabaseApps,
    selectedDatasetApps,
    isAuthAllAgentAndFlow = false,
    isAuthAllDatabaseAndDataset = false,
  } = payload

  const currentUserRoleCode = appResourceAcl.role_code

  const isDeveloper = roleCode === RoleCode.DEVELOPER

  if (currentUserRoleCode === RoleCode.ADMINISTRATOR) {
    if (authAppSelectEnable) {
      if (isAuthAllAgentAndFlow || isAuthAllDatabaseAndDataset) {
        const is_auth_app_by_type = [
          {
            app_type: 'AGENT',
            permission_points: [isDeveloper ? EDITOR_FIELD : READ_ONLY_FIELD],
          },
          {
            app_type: 'AI',
            permission_points: [isDeveloper ? EDITOR_FIELD : READ_ONLY_FIELD],
          },
        ]
        if (isAuthAllDatabaseAndDataset) {
          is_auth_app_by_type.push(
            {
              app_type: 'DATABASE',
              permission_points: [isDeveloper ? EDITOR_FIELD : READ_ONLY_FIELD],
            },
            {
              app_type: 'DATASET',
              permission_points: [isDeveloper ? EDITOR_FIELD : READ_ONLY_FIELD],
            },
          )
        }
        return {
          is_auth_all_app: isAuthAllAgentAndFlow && isAuthAllDatabaseAndDataset,
          is_auth_app_by_type,
        }
      }
      return {
        is_auth_all_app: false,
        agent_permission: selectedAgentApps.map(id => ({
          app_id: id,
          app_group_id: '',
          permission_points: [READ_ONLY_FIELD],
        })),
        flow_permission: selectedFlowApps.map(id => ({
          app_id: id,
          app_group_id: '',
          permission_points: [READ_ONLY_FIELD],
        })),
        db_permission: selectedDatabaseApps?.map(id => ({
          app_id: id,
          app_group_id: '',
          permission_points: [READ_ONLY_FIELD],
        })),
        dataset_permission: selectedDatasetApps?.map(id => ({
          app_id: id,
          app_group_id: '',
          permission_points: [READ_ONLY_FIELD],
        })),
      }
    } else if (applicationId) {
      // 仅授权当前所在的app页面的app
      const key = isAgent ? 'agent_permission' : 'flow_permission'
      return {
        is_auth_all_app: false,
        [key]: [
          {
            app_id: applicationId,
            permission_points: [isDeveloper ? EDITOR_FIELD : READ_ONLY_FIELD],
          },
        ],
      }
    }
  } else if (currentUserRoleCode === RoleCode.DEVELOPER) {
    const currentUserPermissions = {
      is_auth_all_app: appResourceAcl.is_auth_all_app,
      is_auth_app_by_type: appResourceAcl.is_auth_app_by_type,
      group_permission: [
        ...(appResourceAcl.AGENT.groups_permission ?? []),
        ...(appResourceAcl.AI.groups_permission ?? []),
        ...(appResourceAcl.DATASET.groups_permission ?? []),
        ...(appResourceAcl.DATABASE.groups_permission ?? []),
      ],
      agent_permission: appResourceAcl.AGENT.app_list,
      flow_permission: appResourceAcl.AI.app_list,
      dataset_permission: appResourceAcl.DATASET.app_list,
      db_permission: appResourceAcl.DATABASE.app_list,
    }
    if (roleCode === RoleCode.DEVELOPER) {
      if (applicationId) {
        // 仅授权当前所在的app页面的app
        const key = isAgent ? 'agent_permission' : 'flow_permission'
        return {
          is_auth_all_app: false,
          [key]: [
            {
              app_id: applicationId,
              permission_points: [isDeveloper ? EDITOR_FIELD : READ_ONLY_FIELD],
            },
          ],
        }
      }
      // 授予与当前角色相同的权限
      return currentUserPermissions
    } else if (roleCode === RoleCode.VIEWER) {
      if (authAppSelectEnable) {
        /**
         * 这里不用考虑复制当前用户权限并转为只读的情况，也不用考虑全选应用的情况，
         * 所有的应用权限的过滤都会在应用选择的组件中处理完成
         * 如果在这里处理，要考虑高级组权限（权限，类型权限，组权限）是否包含当前应用权限，而判断依赖的数据不全面
         * 简单期间，这里只处理外部传入的应用权限的授权
         */
        return {
          is_auth_all_app: false,
          agent_permission: selectedAgentApps.map(id => ({
            app_id: id,
            app_group_id: '',
            permission_points: [READ_ONLY_FIELD],
          })),
          flow_permission: selectedFlowApps.map(id => ({
            app_id: id,
            app_group_id: '',
            permission_points: [READ_ONLY_FIELD],
          })),
        }
      } else if (applicationId) {
        // 授予当前应用的使用权限
        const key = isAgent ? 'agent_permission' : 'flow_permission'
        return {
          is_auth_all_app: false,
          [key]: [
            {
              app_id: applicationId,
              permission_points: [READ_ONLY_FIELD],
            },
          ],
        }
      }
    }
  }
  return {}
}

export function calcHasAllAppConfigAuth(payload: {
  currentUserRole: RoleCode
  appResourceAcl: AppResourceAclType
}) {
  const { currentUserRole, appResourceAcl } = payload
  if (currentUserRole === RoleCode.ADMINISTRATOR) return true
  if (currentUserRole === RoleCode.DEVELOPER) {
    if (appResourceAcl?.is_auth_all_app) return true
    const hasAllAgentEditorAuth =
      appResourceAcl?.is_auth_app_by_type?.findIndex(
        item =>
          item.app_type === 'AGENT' &&
          item.permission_points.includes(EDITOR_FIELD),
      ) !== -1
    const hasAllFlowEditorAuth =
      appResourceAcl?.is_auth_app_by_type?.findIndex(
        item =>
          item.app_type === 'AI' &&
          item.permission_points.includes(EDITOR_FIELD),
      ) !== -1
    if (hasAllAgentEditorAuth && hasAllFlowEditorAuth) return true
  }
  return false
}
