import { memo, useMemo } from 'react'
import { cn } from '@bty/util'
import { useRequest } from 'ahooks'
import { getAuthConfig } from '@apis/datastore'
import { FeiShu } from './config/feishu'

enum DataStoreEnum {
  FEISHU = 'feishu',
  WECHAT = 'wechat',
  DING = 'ding',
  YUQUE = 'yuque',
  NOTION = 'notion',
}

interface DataStoreCardProps {
  type: DataStoreEnum
  active?: boolean
  disable?: boolean
  config?: boolean
  title: string
  desc: string
}

const LOGO_IMAGE = {
  [DataStoreEnum.FEISHU]:
    'https://resource-bty.oss-cn-hangzhou.aliyuncs.com/betteryeah/logo/feishu.png',
  [DataStoreEnum.WECHAT]:
    'https://resource-bty.oss-cn-hangzhou.aliyuncs.com/betteryeah/logo/wechat.png',
  [DataStoreEnum.DING]:
    'https://resource-bty.oss-cn-hangzhou.aliyuncs.com/betteryeah/logo/ding.png',
  [DataStoreEnum.YUQUE]:
    'https://resource-bty.oss-cn-hangzhou.aliyuncs.com/betteryeah/logo/yuque.png',
  [DataStoreEnum.NOTION]:
    'https://resource-bty.oss-cn-hangzhou.aliyuncs.com/betteryeah/logo/notion.png',
}

const DataStoreCard = memo((props: DataStoreCardProps) => {
  const { type, title, desc, active, disable, config } = props

  return (
    <div
      className={cn(
        'h-72px rd-8px b-1px b-solid b-#E1E1E5/40 p-16px flex gap-8px items-center bg-#fff cursor-pointer mb-12px relative of-hidden',
        {
          'opacity-60': disable,
          'bg-#7B61FF/8! b-#7B61FF!': active,
        },
      )}
    >
      <div>
        <img className='size-40px' src={LOGO_IMAGE[type]} alt='' />
      </div>
      <div className='flex-1'>
        <div className='text-14px/16px font-500 mb-8px flex items-center'>
          {title}
          {config && (
            <span className='text-#00B078 py-2px px-4px rd-4px text-10px/12px bg-#EBF9F5 ml-8px'>
              已接入
            </span>
          )}
        </div>
        <div className='text-12px text-#8D8D99'>{desc}</div>
      </div>
      {disable && (
        <div className='bg-#ECEDF3 rounded-bl-8px absolute right-0px top-0px text-12px px-6px py-4px'>
          即将推出
        </div>
      )}
    </div>
  )
})

export const DataSource = memo(() => {
  const { loading, data = [], refresh } = useRequest(getAuthConfig)

  const feishuConfig = useMemo(
    () => data.find(each => each.provider_type === 'feishu'),
    [data],
  )

  return (
    <div className='adapt:px-64 size-full pb-24px'>
      <div className='bg-#fff rd-12px of-hidden size-full flex'>
        <div className='p-24px w-360px bg-#FCFCFD b-r-solid b-r-1px b-r-#E1E1E5/60'>
          <p className='text-16px/24px font-500 mb-12px'>关联数据源</p>
          <DataStoreCard
            type={DataStoreEnum.FEISHU}
            title='飞书'
            desc='关联飞书知识库、文档，获取数据'
            active
            config={!!feishuConfig}
          />
          <DataStoreCard
            type={DataStoreEnum.WECHAT}
            title='企业微信'
            desc='关联企业微信知识库、文档，获取数据'
            disable
          />
          <DataStoreCard
            type={DataStoreEnum.DING}
            title='钉钉'
            desc='关联钉钉知识库、文档，获取数据'
            disable
          />
          <DataStoreCard
            type={DataStoreEnum.YUQUE}
            title='语雀'
            desc='关联语雀知识库、文档，获取数据'
            disable
          />
          <DataStoreCard
            type={DataStoreEnum.NOTION}
            title='Notion'
            desc='关联Notion知识库、文档，获取数据'
            disable
          />
        </div>
        <div className='flex-1'>
          <FeiShu loading={loading} config={feishuConfig} onUpdate={refresh} />
        </div>
      </div>
    </div>
  )
})
