import type { ReactNode } from 'react'
import { memo } from 'react'
import type { HelpProps } from './help'
import { Help } from './help'

interface StepProps {
  title: ReactNode
  content: ReactNode
  helpList: HelpProps['list']
}

export const Step = memo((props: StepProps) => {
  const { title, content, helpList } = props

  return (
    <div className='flex size-full of-hidden'>
      <div className='p-24px flex-1 b-r-1px b-r-solid b-r-#E1E1E5/60'>
        <h3 className='text-16px/16px font-500 mb-24px'>{title}</h3>

        {content}
      </div>
      <div className='flex-1'>
        <Help list={helpList} />
      </div>
    </div>
  )
})
