import { IconFont } from '@bty/components'
import type { ReactNode } from 'react'
import { memo, useMemo } from 'react'
import { Image } from 'antd'

export interface HelpProps {
  list: {
    title: ReactNode
    image: string
    imageWidth?: string
  }[]
}

export const Help = memo((props: HelpProps) => {
  const { list } = props

  const imageList = useMemo(() => list.map(e => e.image), [list])

  return (
    <div className='size-full of-auto p-24px'>
      <h3 className='text-16px/16px font-500 flex items-center mb-20px'>
        <IconFont name='youhuajianyi'></IconFont>
        <span className='ml-4px'>帮助说明</span>
      </h3>
      {list.map((each, index) => {
        return (
          <div className='mb-24px' key={index}>
            <div className='mb-8px'>{each.title}</div>
            <Image.PreviewGroup items={imageList}>
              <Image
                rootClassName='[&_.ant-image-mask]:opacity-0! [&_.ant-image-mask]:cursor-zoom-in!'
                src={each.image}
                style={{ width: each.imageWidth }}
                alt=''
              />
            </Image.PreviewGroup>
          </div>
        )
      })}
    </div>
  )
})
