import { getFeiShuCallbackUrl } from '@apis/datastore'
import { Button, IconFont, Input } from '@bty/components'
import { useMemoizedFn } from 'ahooks'
import type { StepProps } from 'antd'
import { Checkbox, Form, message } from 'antd'
import copy from 'copy-to-clipboard'

interface StepConfigProps {
  onPrev: (now: number) => void
  onNext: (now: number) => void
}

export function Step1Config(props: StepConfigProps) {
  const { onNext } = props

  const form = Form.useFormInstance()
  const appId = Form.useWatch('appId', form)
  const appSecret = Form.useWatch('appSecret', form)

  const handleNext = useMemoizedFn(async () => {
    await form.validateFields()
    onNext(0)
  })

  return (
    <div>
      <Form.Item
        name='appId'
        label={
          <span className='text-14px/16px font-500'>
            App ID <span className='text-#FF5219'>*</span>
          </span>
        }
        required
        rules={[{ required: true, message: '请输入 App ID' }]}
      >
        <Input placeholder='在「凭证与基础信息」中获取'></Input>
      </Form.Item>

      <Form.Item
        name='appSecret'
        label={
          <span className='text-14px/16px font-500'>
            App Secret <span className='text-#FF5219'>*</span>
          </span>
        }
        required
        rules={[{ required: true, message: '请输入 App Secret' }]}
      >
        <Input placeholder='在「凭证与基础信息」中获取'></Input>
      </Form.Item>

      <div className='flex gap-12px mt-40px'>
        <Button
          className='flex-1'
          type='primary'
          disabled={!appId || !appSecret}
          onClick={handleNext}
        >
          下一步
        </Button>
      </div>
    </div>
  )
}

export const step1Help = [
  {
    title: (
      <p className='text-14px/22px font-500'>
        1.登录「飞书开放平台-开发者后台」创建一个应用
      </p>
    ),
    image:
      'https://resource-bty.oss-cn-hangzhou.aliyuncs.com/betteryeah/setting/bind-feishu-step1-1.png',
  },
  {
    title: (
      <p className='text-14px/22px font-500'>
        2.打开「凭证与基础信息」分别获取App ID 和App Secret
      </p>
    ),
    image:
      'https://resource-bty.oss-cn-hangzhou.aliyuncs.com/betteryeah/setting/bind-feishu-step1-2.png',
  },
]

export function Step2Config(props: StepConfigProps) {
  const { onPrev, onNext } = props

  const form = Form.useFormInstance()
  const confirmUrl = Form.useWatch('confirmUrl', form)

  const handlePrev = useMemoizedFn(async () => {
    onPrev(1)
  })

  const handleNext = useMemoizedFn(async () => {
    onNext(1)
  })

  const handleCopy = useMemoizedFn(() => {
    copy(getFeiShuCallbackUrl())
    message.success('已复制')
  })

  return (
    <div>
      <div className='pb-8px text-14px/16px font-500'>
        重定向URL <span className='text-#FF5219'>*</span>
      </div>
      <Input
        readOnly
        value={getFeiShuCallbackUrl()}
        suffix={
          <span
            className='size-24px text-16px flex-center cursor-pointer hover:bg-bg_3/8 rd-4px mr-[-6px]'
            onClick={handleCopy}
          >
            <IconFont name='copy' />
          </span>
        }
      />

      <div className='p-12px bg-#626999/6 rd-8px mt-24px flex items-start'>
        <Form.Item noStyle name='confirmUrl' valuePropName='checked'>
          <Checkbox className='line-height-16px' />
        </Form.Item>
        <div className='ml-8px'>
          <div className='text-14px/16px mb-8px'>重定向URL已配置</div>
          <div className='text-12px text-#979797'>
            未配置重定向URL无法同步飞书文档内容
          </div>
        </div>
      </div>

      <div className='flex gap-12px mt-40px'>
        <Button className='w-100px' onClick={handlePrev}>
          上一步
        </Button>
        <Button
          className='flex-1'
          type='primary'
          disabled={!confirmUrl}
          onClick={handleNext}
        >
          下一步
        </Button>
      </div>
    </div>
  )
}

export const step2Help = [
  {
    title: (
      <p className='text-14px/22px font-500'>
        1.复制重定向URL，填写到「安全设置-重定向URL」中
      </p>
    ),
    image:
      'https://resource-bty.oss-cn-hangzhou.aliyuncs.com/betteryeah/setting/bind-feishu-step2-1.png',
  },
]

interface AuthProps {
  children: string
}

function Auth(props: AuthProps) {
  const { children } = props

  const handleCopy = useMemoizedFn(() => {
    copy(children)
    message.success('复制成功')
  })

  return (
    <span className='text-#979797 leading-inherit'>
      <span
        className='leading-inherit border-b-1 border-dashed border-font_1 cursor-pointer hover:opacity-60'
        onClick={handleCopy}
      >
        {children}
      </span>
      ；
    </span>
  )
}

export function Step3Config(props: StepConfigProps) {
  const { onPrev, onNext } = props

  const form = Form.useFormInstance()
  const confirmAuth = Form.useWatch('confirmAuth', form)

  const handlePrev = useMemoizedFn(async () => {
    onPrev(2)
  })

  const handleNext = useMemoizedFn(async () => {
    onNext(2)
  })

  return (
    <div>
      <div className='pb-8px text-14px/16px font-500'>开通权限</div>

      <div className='p-12px bg-#626999/6 rd-8px flex items-start'>
        <Form.Item noStyle name='confirmAuth' valuePropName='checked'>
          <Checkbox className='line-height-16px' />
        </Form.Item>

        <div className='ml-8px'>
          <div className='text-14px/16px'>已确认开通以下权限</div>
          <p className='text-12px/20px mt-8px'>应用身份权限10个：</p>
          <div className='flex flex-col items-start text-12px/20px'>
            <Auth>通过手机号或邮箱获取用户 ID</Auth>
            <Auth>查看云文档内容</Auth>
            <Auth>创建及编辑新版文档</Auth>
            <Auth>查看新版文档</Auth>
            <Auth>查看、评论、编辑和管理云空间中所有文件</Auth>
            <Auth>获取云空间文件夹下的云文档清单</Auth>
            <Auth>查看、编辑和管理知识库</Auth>
            <Auth>查看知识库</Auth>
            <Auth>查看企业的完整域名</Auth>
            <Auth>获取企业信息</Auth>
          </div>
          <p className='text-12px/20px mt-8px'>用户身份权限1个：</p>
          <div className='flex flex-col items-start text-12px/20px'>
            <Auth>离线访问已授权数据</Auth>
          </div>
        </div>
      </div>

      <div className='flex gap-12px mt-40px'>
        <Button className='w-100px' onClick={handlePrev}>
          上一步
        </Button>
        <Button
          className='flex-1'
          type='primary'
          disabled={!confirmAuth}
          onClick={handleNext}
        >
          下一步
        </Button>
      </div>
    </div>
  )
}

export const step3Help = [
  {
    title: (
      <>
        <p className='text-14px/22px font-500'>
          1. 进入「权限管理」，依次搜索并开通以下「应用权限」：
        </p>
        <p className='text-14px/22px'>
          <span>（1）应用身份权限：</span>
          <Auth>通过手机号或邮箱获取用户 ID</Auth>
          <Auth>查看云文档内容</Auth>
          <Auth>创建及编辑新版文档</Auth>
          <Auth>查看新版文档</Auth>
          <Auth>查看、评论、编辑和管理云空间中所有文件</Auth>
          <Auth>获取云空间文件夹下的云文档清单</Auth>
          <Auth>查看、编辑和管理知识库</Auth>
          <Auth>查看知识库</Auth>
          <Auth>查看企业的完整域名</Auth>
          <Auth>获取企业信息</Auth>
        </p>
        <p className='text-14px/22px'>
          <span>（2）用户身份权限：</span>
          <Auth>离线访问已授权数据</Auth>
        </p>
      </>
    ),
    image:
      'https://resource-bty.oss-cn-hangzhou.aliyuncs.com/betteryeah/setting/bind-feishu-step3-1.png',
  },
]

export function Step4Config(props: StepConfigProps) {
  const { onPrev, onNext } = props

  const form = Form.useFormInstance()
  const confirmPub = Form.useWatch('confirmPub', form)

  const handlePrev = useMemoizedFn(async () => {
    onPrev(3)
  })

  const handleNext = useMemoizedFn(async () => {
    onNext(3)
  })

  return (
    <div>
      <div className='p-12px bg-#626999/6 rd-8px flex items-start'>
        <Form.Item noStyle name='confirmPub' valuePropName='checked'>
          <Checkbox className='line-height-16px' />
        </Form.Item>

        <div className='ml-8px'>
          <div className='text-14px/16px mb-8px'>飞书应用已发布</div>
          <div className='text-12px/20px text-#979797'>
            未发布的应用将无法正常使用
          </div>
        </div>
      </div>

      <div className='flex gap-12px mt-40px'>
        <Button className='w-100px' onClick={handlePrev}>
          上一步
        </Button>
        <Button
          className='flex-1'
          type='primary'
          disabled={!confirmPub}
          onClick={handleNext}
        >
          完成
        </Button>
      </div>
    </div>
  )
}

export const step4Help = [
  {
    title: (
      <p className='text-14px/22px font-500'>
        1.在应用详情「版本管理与发布」创建版本并发布
      </p>
    ),
    image:
      'https://resource-bty.oss-cn-hangzhou.aliyuncs.com/betteryeah/setting/bind-feishu-step4-1.png',
  },
  {
    title: (
      <p className='text-14px/22px font-500'>2.发布后的应用状态显示为已启用</p>
    ),
    image:
      'https://resource-bty.oss-cn-hangzhou.aliyuncs.com/betteryeah/setting/bind-feishu-step4-2.png',
  },
  {
    title: (
      <p className='text-14px/22px font-500'>
        3.在知识库中登录此应用下的账号，可同步相应账号的文档
      </p>
    ),
    image:
      'https://resource-bty.oss-cn-hangzhou.aliyuncs.com/betteryeah/setting/bind-feishu-step4-3.png',
    imageWidth: '60%',
  },
]

export function getSteps(): StepProps[] {
  return [
    {
      status: 'process',
      disabled: true,
      title: <span className='text-14px font-500'>填写应用信息</span>,
    },
    {
      status: 'wait',
      disabled: true,
      title: <span className='text-14px font-500'>配置安全设置</span>,
    },
    {
      status: 'wait',
      disabled: true,
      title: <span className='text-14px font-500'>应用权限设置</span>,
    },
    {
      status: 'wait',
      disabled: true,
      title: <span className='text-14px font-500'>发布飞书应用</span>,
    },
  ]
}
