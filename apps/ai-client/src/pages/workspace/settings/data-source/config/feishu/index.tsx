import { Dropdown, Form, message, Modal, Popconfirm, Spin, Steps } from 'antd'
import { memo, useEffect, useMemo, useRef, useState } from 'react'
import { useMemoizedFn } from 'ahooks'
import { deleteFeiShuConfig, saveFeiShuConfig } from '@apis/datastore'
import { Button, IconFont } from '@bty/components'
import dayjs from 'dayjs'
import { Step } from '../step'
import {
  Step1Config,
  step1Help,
  Step2Config,
  step2Help,
  Step3Config,
  step3Help,
  Step4Config,
  step4Help,
  getSteps,
} from './const'

const feishuLink = (
  <a
    href='https://open.feishu.cn/app'
    target='_blank'
    className='text-#7B61FF mx-4px'
    rel='noreferrer'
  >
    飞书开放平台
  </a>
)
interface FeiShuBindProps {
  initConfig?: {
    client_id: string
    update_time: string
    update_user: string
  }
  onBack: () => void
  onSuccess: () => void
}

export const FeiShuBind = memo((props: FeiShuBindProps) => {
  const { initConfig, onSuccess, onBack } = props

  const [step, setStep] = useState(getSteps)
  const [current, setCurrent] = useState(0)
  const [form] = Form.useForm()
  const configRef = useRef<any>({})

  const onConfirm = useMemoizedFn(async () => {
    await saveFeiShuConfig({
      client_id: configRef.current.appId,
      client_secret: configRef.current.appSecret,
    })
    message.success('配置完成')
    onSuccess()
  })

  const onPrev = useMemoizedFn((step: number) => {
    setCurrent(step - 1)
  })

  const onStepNext = useMemoizedFn(async (step: number) => {
    if (step === 3) {
      return onConfirm()
    }
    const values = form.getFieldsValue()
    configRef.current = {
      ...configRef.current,
      ...values,
    }
    setCurrent(step + 1)
    setStep(prev => {
      prev[step].status = 'finish'
      prev[step + 1].status = 'process'
      return prev
    })
  })

  useEffect(() => {
    if (initConfig) {
      form.setFieldsValue({
        appId: initConfig.client_id,
      })
    } else {
      form.resetFields()
    }
  }, [initConfig])

  return (
    <div className='size-full flex flex-col'>
      <h2 className='px-24px pt-21px pb-9px h-66px text-20px/30px font-500 flex-center'>
        <span className='flex-1'>关联飞书数据源</span>
        {initConfig && (
          <Popconfirm
            icon={false}
            placement='top'
            style={{
              width: '200px',
            }}
            title={
              <p className='w-200px pb-12px font-bold lh-20px'>
                退出配置后，已填写的内容不会被保存，是否确认退出？
              </p>
            }
            cancelButtonProps={{ size: 'middle' }}
            okText='退出'
            okButtonProps={{ danger: true, size: 'middle' }}
            onConfirm={onBack}
          >
            <Button>退出重置</Button>
          </Popconfirm>
        )}
      </h2>
      <Steps
        className='b-b-1px b-b-solid b-b-#E1E1E5/60 [&_.ant-steps-icon]:font-500 [&_.ant-steps-item-icon]:size-22px [&_.ant-steps-item-icon]:flex-center [&_.ant-steps-item-container]:flex-center!'
        type='navigation'
        current={current}
        items={step}
      />
      <Form
        form={form}
        requiredMark={false}
        className='flex-1 of-hidden'
        layout='vertical'
      >
        {current === 0 && (
          <Step
            title={<>登录{feishuLink}获取以下参数</>}
            content={<Step1Config onPrev={onPrev} onNext={onStepNext} />}
            helpList={step1Help}
          />
        )}
        {current === 1 && (
          <Step
            title={<>在{feishuLink}配置「安全设置」的重定向URL</>}
            content={<Step2Config onPrev={onPrev} onNext={onStepNext} />}
            helpList={step2Help}
          />
        )}
        {current === 2 && (
          <Step
            title={<>在{feishuLink}应用中配置「应用权限」</>}
            content={<Step3Config onPrev={onPrev} onNext={onStepNext} />}
            helpList={step3Help}
          />
        )}
        {current === 3 && (
          <Step
            title={<>在{feishuLink}版本管理中发布当前应用</>}
            content={<Step4Config onPrev={onPrev} onNext={onStepNext} />}
            helpList={step4Help}
          />
        )}
      </Form>
    </div>
  )
})

interface FeiShuConfigProps {
  config: {
    client_id: string
    update_time: string
    update_user: string
  }
  onReBind: () => void
  onUnBind: () => void
}

export const FeiShuConfig = memo((props: FeiShuConfigProps) => {
  const { config, onReBind, onUnBind } = props

  const menu = useMemo(() => {
    return [
      {
        key: 'rebind',
        label: (
          <div className='w-96px h-32px px-12px flex items-center gap-4px'>
            重新配置
          </div>
        ),
      },
      {
        key: 'delete',
        label: (
          <div className='w-96px h-32px px-12px flex items-center gap-4px text-error'>
            解除绑定
          </div>
        ),
      },
    ]
  }, [])

  const handleMenu = useMemoizedFn((info: any) => {
    if (info.key === 'rebind') {
      onReBind()
    }
    if (info.key === 'delete') {
      Modal.confirm({
        width: 480,
        icon: null,
        centered: true,
        maskClosable: true,
        okText: '解绑',
        okButtonProps: { danger: true },
        content: (
          <div className='text-14px/22px font-500'>
            是否确认解除绑定飞书数据源？
          </div>
        ),
        onOk: async () => {
          await deleteFeiShuConfig()
          message.success('解绑成功')
          onUnBind()
        },
      })
    }
  })

  return (
    <div className='size-full flex flex-col'>
      <h2 className='px-24px pt-21px pb-9px h-66px text-20px/30px font-500 flex-center'>
        <span className='flex-1'>关联飞书数据源</span>
      </h2>

      <div className='px-24px'>
        <div className='h-80px b-1px b-solid b-#E1E1E5/60 rd-8px flex items-center gap-16px px-16px mb-24px'>
          <IconFont className='text-22px' name='pay-success' />

          <div className='flex-1'>
            <div className='text-16px/24px font-500 mb-8px'>
              已接入飞书数据源
            </div>
            <div className='text-12px/16px text-#8D8D99'>
              接入时间：{dayjs(config.update_time).format('YYYY-MM-DD HH:mm')}
              <span className='mx-4px'>｜</span>
              最后操作人：{config.update_user}
            </div>
          </div>

          <Dropdown
            rootClassName='[&_.ant-dropdown-menu]:p-4px!'
            placement='bottomRight'
            menu={{ items: menu, onClick: handleMenu }}
          >
            <div className='size-24px flex-center cursor-pointer hover:bg-bg_3/8 rd-4px'>
              <IconFont className='text-16px' name='gengduo' />
            </div>
          </Dropdown>
        </div>

        <div className='text-14px font-500 mb-16px'>
          在知识库中登录此应用下的账号，可同步相应账号的文档
        </div>

        <img
          className='w-300px'
          src='https://resource-bty.oss-cn-hangzhou.aliyuncs.com/betteryeah/setting/bind-feishu-step4-3.png'
          alt=''
        />
      </div>
    </div>
  )
})

interface FeiShuProps {
  loading?: boolean
  config: {
    client_id: string
    update_time: string
    update_user: string
  }
  onUpdate: () => void
}

export const FeiShu = memo((props: FeiShuProps) => {
  const { loading, config, onUpdate } = props
  const [reBind, setReBind] = useState(false)

  const handleReBind = useMemoizedFn(() => {
    setReBind(true)
  })

  const handleUnBind = useMemoizedFn(() => {
    onUpdate()
  })

  const handleBack = useMemoizedFn(() => {
    setReBind(false)
  })

  const handleSuccess = useMemoizedFn(() => {
    onUpdate()
    setReBind(false)
  })

  if (loading) {
    return <Spin className='size-full flex-center' />
  }

  return config && !reBind ? (
    <FeiShuConfig
      config={config}
      onReBind={handleReBind}
      onUnBind={handleUnBind}
    />
  ) : (
    <FeiShuBind
      initConfig={reBind ? config : undefined}
      onBack={handleBack}
      onSuccess={handleSuccess}
    />
  )
})
