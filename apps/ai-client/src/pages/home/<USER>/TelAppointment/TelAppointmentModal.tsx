import { useState } from 'react'
import { Form, message } from 'antd'
import NiceModal, { useModal } from '@ebay/nice-modal-react'
import { createWebsiteReservation } from '@apis/track'
import { Modal } from '@/components'

import { TelAppointmentContent } from './TelAppointmentContent'

export interface submitValueType {
  phone: string
  company: string
  user_name: string
  desc: string
}
interface TelAppointmentModelProps {
  source: string
}

function TelAppointmentModel(props: TelAppointmentModelProps) {
  const { source } = props
  const [submitted, setSubmitted] = useState(false)
  const [form] = Form.useForm()
  const modal = useModal()

  const handleSubmit = async (values: submitValueType) => {
    try {
      const response = await createWebsiteReservation({
        ...values,
        source,
      })
      if (response?.id) {
        setSubmitted(true)
        form.resetFields()
      }
    } catch (error: any) {
      message.error(error.message || '提交失败')
    }
  }

  const handleClose = () => {
    modal.hide()
    form.resetFields()
  }

  const handleCloseSuccess = () => {
    modal.hide()
    setSubmitted(false)
  }
  return (
    <Modal
      open={modal.visible}
      onCancel={handleClose}
      footer={null}
      width={960}
      styles={{
        content: {
          backgroundImage:
            'url(https://resource.bantouyan.com/battleyeah-ai/ai-website/assets/bg-form-img.png)',
          backgroundSize: '100% 100%',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        },
      }}
    >
      <TelAppointmentContent
        submitted={submitted}
        form={form}
        handleCloseSuccess={handleCloseSuccess}
        handleSubmit={handleSubmit}
        source={source}
      />
    </Modal>
  )
}

export default NiceModal.create(TelAppointmentModel)
