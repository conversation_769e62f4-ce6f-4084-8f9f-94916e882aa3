import { Button } from 'antd'

const sucessSubmissionIcon =
  'https://resource.bantouyan.com/battleyeah-ai/ai-website/assets/success_submission.png'

export function SuccessPage({ onClose }: { onClose: () => void }) {
  return (
    <div className='flex flex-col items-center justify-center pt-94px'>
      <img
        src={sucessSubmissionIcon}
        alt='提交成功'
        className='w-140px h-140px'
      />
      <p className='text-14px/22px pb-106px mx-auto text-center'>
        您的预约演示申请已提交，
        <br />
        我们将在2个工作日内与您联系！
      </p>
      <div className='flex justify-end w-full'>
        <Button
          onClick={onClose}
          className='px-16px py-8px text-14px font-500 rounded-8px mb-24px mr-32px'
        >
          我知道了
        </Button>
      </div>
    </div>
  )
}
