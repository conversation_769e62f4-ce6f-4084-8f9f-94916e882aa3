import { Form, Input } from 'antd'
import type { FormInstance } from 'antd/es/form'
import { Button } from '@/components'
import { useUserStore } from '@/store'
import { Track } from '@/features/track'
import { SuccessPage } from './SuccessPage'
import type { submitValueType } from './TelAppointmentModal'

const formDescription = [
  {
    title: '前沿AI技术分享',
    description: '探索AI大模型、知识库及AI Agent的强大功能',
  },
  {
    title: '头部行业案例解析',
    description: '标杆客户的最佳实践',
  },
  {
    title: '企业版产品试用',
    description: '全面体验我们的企业级产品套件',
  },
]

const CustomerServiceQRcode =
  'https://resource.bantouyan.com/battleyeah-ai/ai-website/assets/pricing/customer-service-qrcode.png'

interface TelAppointmentContentProps {
  submitted: boolean
  form: FormInstance
  handleCloseSuccess: () => void
  handleSubmit: (values: submitValueType) => void
  source: string
}

const eventMap: Record<string, string> = {
  client_explore_banner: 'client_explore_banner',
  client_help_center: 'help_solution_submit',
}

export function TelAppointmentContent(props: TelAppointmentContentProps) {
  const { submitted, form, source, handleCloseSuccess, handleSubmit } = props
  const { user } = useUserStore()

  const trackEvent = eventMap[source] || 'help_solution_submit'

  return (
    <div className='flex'>
      <div className='flex-1 pt-48px pl-38px'>
        <div className='text-28px/28px c-#17171D font-bold pb-32px'>
          获取企业级专属AI解决方案
        </div>
        <div className='flex flex-col gap-y-24px'>
          {formDescription?.map((item, index) => (
            <div key={index} className='flex gap-x-8px'>
              <img
                src='https://resource.bantouyan.com/battleyeah-ai/ai-website/assets/star2.png'
                alt='star'
                className='w-16px h-24px'
              />
              <div>
                <span className='c-#17171D text-16px/24px font-500'>
                  {item.title}
                </span>
                <p className='c-#17171D7A text-14px/22px mt-2px'>
                  {item.description}
                </p>
              </div>
            </div>
          ))}
          <div className='mt-46px'>
            <div className='text-14px/16px font-500'>联系我们</div>
            <img
              alt='微信扫码加入'
              className='object-contain w-64px h-64px mt-12px'
              src={CustomerServiceQRcode}
            />
            <div className='color-[rgba(23,23,29,0.48)] text-12px/12px mt-8px w-64px text-center'>
              微信扫一扫
            </div>
          </div>
        </div>
      </div>
      <div className='w-416px min-h-520px pt-48px'>
        {submitted ? (
          <SuccessPage onClose={handleCloseSuccess} />
        ) : (
          <Form
            form={form}
            onFinish={handleSubmit}
            className='px-40px pb-40px'
            layout='vertical'
            initialValues={{
              phone: user?.phone,
            }}
          >
            <Form.Item
              name='company'
              label='公司'
              rules={[{ required: true, message: '请输入公司名称' }]}
            >
              <Input
                placeholder='填写您的公司名称'
                maxLength={50}
                className='border-transparent !bg-[rgba(98,105,153,0.06)] hover:border-[#7B61FF] focus:border-[#7B61FF]'
              />
            </Form.Item>

            <Form.Item name='user_name' label='姓名'>
              <Input
                placeholder='填写您的姓名或称呼'
                maxLength={30}
                className='border-transparent !bg-[rgba(98,105,153,0.06)] hover:border-[#7B61FF] focus:border-[#7B61FF]'
              />
            </Form.Item>

            <Form.Item
              name='phone'
              label='电话'
              rules={[
                { required: true, message: '请输入电话号码' },
                {
                  pattern: /^1[3-9]\d{9}$|^\d{3}-\d{8}$|^\d{4}-\d{7}$/,
                  message: '请输入有效的电话号码或座机号码',
                },
              ]}
            >
              <Input
                placeholder='填写您的手机号码'
                maxLength={15}
                className='border-transparent !bg-[rgba(98,105,153,0.06)] hover:border-[#7B61FF] focus:border-[#7B61FF]'
              />
            </Form.Item>

            <Form.Item
              name='desc'
              label='需求描述'
              rules={[{ required: true, message: '需求描述不能为空' }]}
            >
              <Input.TextArea
                placeholder='描述您的需求'
                maxLength={256}
                className='!bg-[rgba(98,105,153,0.06)] text-14px/16px py-10px px-12px rounded-8px w-full h-72px border border-transparent focus:border-#7B61FF focus:outline-none hover:border-#7B61FF'
              />
            </Form.Item>

            <Track event={trackEvent}>
              <Button
                type='primary'
                htmlType='submit'
                className='w-full py-10px text-center bg-#7B61FF c-#FFFFFF text-14px/16px font-500 rounded-8px shadow-[0px_8px_24px_0px_rgba(0,0,0,0.1)] hover:bg-#7B61FFCC'
              >
                获取解决方案
              </Button>
            </Track>
          </Form>
        )}
      </div>
    </div>
  )
}
