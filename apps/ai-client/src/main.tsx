import ReactDOM from 'react-dom/client'

import { ErrorBoundary, mapToMonitorEnvironment, monitor } from '@bty/monitor'
import weekday from 'dayjs/plugin/weekday'
import localeData from 'dayjs/plugin/localeData'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import App from '@/app'
import 'react-resizable/css/styles.css'

// import 'virtual:uno.css'
import './styles/index.css'
import './styles/markdown.css'
import { DataTrack } from './features/track'
import { trackList } from './track.config'
import { tokenStorage, useIdStorage } from './utils/storage'
import { injectHttp } from './http-inject'
import { isPrivateVersion } from './constants/common'

dayjs.extend(weekday)
dayjs.extend(localeData)
dayjs.locale('zh-cn')

if (!__IS_LOCAL__ && !isPrivateVersion) {
  DataTrack.init({ trackList })
  monitor.init({
    env: mapToMonitorEnvironment(__DEFINE_ENV__),
    version: __DEFINE_RELEASE_VERSION__,
    user: {
      name: useIdStorage.get() ?? tokenStorage.get() ?? undefined,
    },
  })
  useIdStorage.listen(useId => monitor.setUser(useId))
}

injectHttp()

const urlSearch = new URLSearchParams(location.search)

if (urlSearch.get('from') === 'feishu_auth') {
  window.close()
}

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <ErrorBoundary>
    <App />
  </ErrorBoundary>,
)
