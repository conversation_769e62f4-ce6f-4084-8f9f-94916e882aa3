import { localize } from '@bty/localize'
import { useMemoizedFn, useRequest } from 'ahooks'
import React, { memo, useEffect, useMemo, useState } from 'react'
import type { MessageInputProps } from '@bty/chat/ui'
import {
  shortcutsFormat,
  useShortcutsForm,
  FileIconButton,
  MessageInput,
} from '@bty/chat/ui'
import type { SelectProps } from 'antd'
import { Empty, Form, Spin, Switch } from 'antd'
import type { AgentConfigShortcut } from '@bty/global-types/shortcuts'
import type { TableProps } from 'antd/lib'
import NiceModal, { useModal } from '@ebay/nice-modal-react'
import { uniq } from 'lodash-es'
import { getAgentShortcutsList } from '@apis/agent'
import type { MessageFile } from '@bty/global-types/message'
import { Controller } from '@bty/components'
import { AppLogo, Button, Modal, Select, Table } from '@/components'
import {
  DEFAULT_SHORTCUTS,
  DEMO_SHORTCUT,
  PROMPT_SHORTCUT,
  WEBPAGE_SHORTCUT,
  TYPE_OPTIONS,
} from '../constant/shortcut'
import { useWorkspaceStore } from '@/store'
import { EnvTag } from '@/components/global-env/EnvTag'
import { ENV_DESC } from '@/components/global-env/const'

export interface ShortcutsModalProps {
  flowId: string
  versionId: string
  initialData?: AgentConfigShortcut
  onSave?: (value?: Record<string, any>) => void
  toAddFlow?: () => void
  toAddPlugin?: () => void
}

function previewFileUpload(params: { file: File }) {
  const { file } = params
  return Promise.resolve({
    url: '',
    type: file.type,
    name: file.name,
    size: file.size,
  } as MessageFile)
}

type ShortcutType = 'flows' | 'utility' | 'prompt' | 'webpage'

interface SettingTableProps {
  columns?: TableProps['columns']
  dataSource?: any[]
  value?: string[]
  onChange?: (value: string[]) => void
}
function SettingTable({ dataSource, value = [], onChange }: SettingTableProps) {
  const handleChange = useMemoizedFn((key: string, checked: boolean) => {
    const newValue = checked
      ? value.filter(v => v !== key)
      : uniq([...value, key])

    onChange?.(newValue)
  })

  const columns: TableProps['columns'] = useMemo(() => {
    return [
      {
        title: localize('parameter', '参数'),
        dataIndex: 'label',
        key: 'label',
      },
      {
        title: localize('type', '类型'),
        dataIndex: 'type',
        key: 'type',
      },
      {
        title: localize('default_value', '默认值'),
        dataIndex: 'default',
        key: 'default',
      },
      {
        title: localize('required', '必填'),
        dataIndex: 'required',
        key: 'required',
        render: (value: boolean) =>
          value ? localize('required', '必填') : '/',
      },
      {
        title: localize('display', '是否显示'),
        render: (_, record) => {
          const needShow = record.required && !record.default
          return (
            <Switch
              defaultChecked={!value?.includes?.(record.variableName)}
              size='small'
              disabled={needShow}
              onChange={checked => {
                handleChange(record.variableName, checked)
              }}
            />
          )
        },
      },
    ]
  }, [value, handleChange])

  return (
    <Table
      rowClassName='[&>.ant-table-cell]:!border-b-0'
      size='small'
      columns={columns}
      dataSource={dataSource}
      pagination={false}
    />
  )
}

function ToolsSelectLabel({
  name,
  icon,
  color,
  env,
  envActive,
}: {
  name: string
  icon: string
  color: string
  env?: string
  envActive?: boolean
}) {
  return (
    <div className='flex items-center h-[100%] w-full'>
      <AppLogo
        size={16}
        fillSize={12}
        value={icon}
        color={color}
        style={{ borderRadius: 4 }}
        type='emoji'
        imgStyle={{ borderRadius: 4 }}
      />
      <div className='ml-[6px] text-truncate' title={name}>
        {name}
      </div>
      <EnvTag
        className='ml-[6px] font-400 text-12px'
        env={env}
        active={envActive}
      />
    </div>
  )
}

interface FunctionIdOptions {
  label: React.ReactNode
  value: string
  disabled: boolean
}

function buildFunctionIdOptions(
  data: Record<string, any>,
  type: string,
): FunctionIdOptions[] {
  return (data?.[type] || []).map((j: Record<string, any>) => {
    return {
      label: (
        <ToolsSelectLabel
          name={j.display_name || j.name}
          icon={j.icon || j.metadata?.icon || ''}
          color={j.color || j.metadata?.color || ''}
          env={j.env}
          envActive={j.active_multi_env}
        />
      ),
      value: j.function_id,
      disabled: !j.is_enable,
    }
  })
}

function buildHiddenParametersDataSource(
  data: Record<string, any>,
  type: ShortcutType,
  functionId: string,
) {
  const dataSource =
    shortcutsFormat({
      shortcuts: [
        {
          function_id: functionId,
          type,
          short_name: '',
          hidden_parameters: [],
        },
      ],
      config: {
        ...(data || {}),
      } as any,
    })?.[0]?.formConfig || []
  return dataSource
}
export const ShortcutsModal = NiceModal.create(
  ({
    flowId,
    versionId,
    initialData,
    onSave,
    toAddFlow,
    toAddPlugin,
  }: ShortcutsModalProps) => {
    const [form] = Form.useForm()
    const currentWorkspaceId = useWorkspaceStore(
      state => state.currentWorkspaceId,
    )

    const modal = useModal()

    const [values] = useState<AgentConfigShortcut>(
      initialData || DEFAULT_SHORTCUTS,
    )

    const { data, loading } = useRequest(getAgentShortcutsList, {
      defaultParams: [flowId, versionId, currentWorkspaceId],
      refreshDeps: [flowId, versionId],
    })

    const type: ShortcutType = Form.useWatch('type', form)
    const functionId = Form.useWatch('function_id', form)
    const shortName = Form.useWatch('short_name', form)
    Form.useWatch('hidden_parameters', form)

    const flowOptions = useMemo(() => {
      return data ? buildFunctionIdOptions(data, 'flows') : []
    }, [data])

    const pluginOptions = useMemo(() => {
      return data ? buildFunctionIdOptions(data, 'utility') : []
    }, [data])

    const dataSources = useMemo(() => {
      return buildHiddenParametersDataSource(data || [], type, functionId)
    }, [type, functionId, data])

    const shortcuts = useMemo(() => {
      const formValues: AgentConfigShortcut = form.getFieldsValue()
      const { short_name } = formValues
      if (formValues.type === 'prompt') {
        return [{ ...PROMPT_SHORTCUT, title: short_name, meta: formValues }]
      }
      if (formValues.type === 'webpage') {
        return [
          {
            ...WEBPAGE_SHORTCUT,
            title: short_name,
            meta: formValues,
            webpage: formValues?.webpage,
          },
        ]
      }

      return formValues?.function_id
        ? shortcutsFormat({
            shortcuts: [formValues],
            config: {
              ...(data || {}),
            } as any,
          })
        : [DEMO_SHORTCUT]
    }, [data, form, functionId, shortName])

    const shortcutFormState = useShortcutsForm(shortcuts, true)

    useEffect(() => {
      if (!shortcuts?.[0]?.formConfig?.length) {
        shortcutFormState.hideShortcutsForm()
      }
    }, [shortcuts])

    useEffect(() => {
      if (!functionId) {
        shortcutFormState.hideShortcutsForm()
      }
    }, [functionId])

    const handleSave = async () => {
      try {
        const values = await form.validateFields()
        onSave?.(values)
      } catch (error) {
        console.warn(error)
      }
    }

    const handleToFlow = useMemoizedFn(() => {
      modal.hide()
      toAddFlow?.()
    })

    const handleToPlugin = useMemoizedFn(() => {
      modal.hide()
      toAddPlugin?.()
    })

    const handleFlowOptionChange: SelectProps['onChange'] = useMemoizedFn(
      value => {
        const flow = (data?.flows || []).find(v => v.function_id === value)
        form.setFieldsValue({
          short_name:
            (flow?.name || '') + (flow?.env ? ` ${ENV_DESC[flow.env]}` : ''),
        })
      },
    )

    const handlePluginOptionChange: SelectProps['onChange'] = useMemoizedFn(
      value => {
        form.setFieldsValue({
          short_name:
            (data?.utility || []).find(v => v.function_id === value)
              ?.display_name || '',
        })
      },
    )

    const messageInputRenders = useMemo<MessageInputProps['renders']>(() => {
      return {
        shortcutTips: false,
        modelSelect: false,
        fileUpload: memo(() => {
          return <FileIconButton disabled />
        }),
      }
    }, [])

    return (
      <Modal
        width={1120}
        open={modal.visible}
        title={localize('create_shortcut', '创建快捷入口')}
        footer={null}
        onCancel={modal.remove}
        styles={{
          body: {
            padding: 0,
          },
        }}
      >
        <Spin spinning={loading}>
          <div className='flex'>
            <div className='w-62% flex flex-col'>
              <div className='h-480px px-24px py-16px overflow-auto'>
                <Form form={form} initialValues={values}>
                  <div className='mb-24px'>
                    <div className='mb-8px'>
                      <span>{localize('execution_content', '执行内容')}</span>
                      <span className='text-#FF512B ml-4px'>*</span>
                    </div>
                    <div className='w-full'>
                      <Form.Item name='type' required noStyle>
                        <Select
                          className='w-full h-36px mb-12px'
                          onChange={() =>
                            form.setFieldsValue({ function_id: '' })
                          }
                          options={TYPE_OPTIONS}
                        />
                      </Form.Item>
                      {type === 'flows' && (
                        <Form.Item
                          className='w-full h-36px [&_.ant-form-item-explain-error]:m-0 [&_.ant-form-item-explain]:mt-4px'
                          name='function_id'
                          required
                          rules={[
                            {
                              required: true,
                              message: localize(
                                'please_select_workflow',
                                '请选择工作流',
                              ),
                            },
                          ]}
                        >
                          <Select
                            className='flex-1 h-36px'
                            placeholder={localize(
                              'please_select_workflow',
                              '请选择工作流',
                            )}
                            options={flowOptions}
                            onChange={handleFlowOptionChange}
                            notFoundContent={
                              <Empty
                                className='ant-empty-small'
                                image={Empty.PRESENTED_IMAGE_SIMPLE}
                                description={
                                  <span>
                                    {localize(
                                      'please_add_workflow_in_agent',
                                      '请先在Agent中添加工作流，',
                                    )}
                                    <span
                                      className='text-primary cursor-pointer'
                                      onClick={handleToFlow}
                                    >
                                      {localize('add', '去添加')}
                                    </span>
                                  </span>
                                }
                              />
                            }
                          />
                        </Form.Item>
                      )}
                      {type === 'utility' && (
                        <Form.Item
                          className='w-full h-36px [&_.ant-form-item-explain-error]:m-0 [&_.ant-form-item-explain]:mt-4px'
                          name='function_id'
                          required
                          rules={[
                            {
                              required: true,
                              message: localize(
                                'please_select_plugin',
                                '请选择插件',
                              ),
                            },
                          ]}
                        >
                          <Select
                            className='flex-1 h-36px'
                            placeholder={localize(
                              'please_select_plugin',
                              '请选择插件',
                            )}
                            options={pluginOptions}
                            onChange={handlePluginOptionChange}
                            notFoundContent={
                              <Empty
                                className='ant-empty-small'
                                image={Empty.PRESENTED_IMAGE_SIMPLE}
                                description={
                                  <span>
                                    {localize(
                                      'please_add_plugin_in_agent',
                                      '请先在Agent中添加插件，',
                                    )}
                                    <span
                                      className='text-primary cursor-pointer'
                                      onClick={handleToPlugin}
                                    >
                                      {localize('add', '去添加')}
                                    </span>
                                  </span>
                                }
                              />
                            }
                          />
                        </Form.Item>
                      )}
                      {type === 'prompt' && (
                        <Controller.Textarea
                          name='prompt'
                          rules={[
                            {
                              required: true,
                              message: localize(
                                'please_fill_in_instruction',
                                '请填写指令',
                              ),
                            },
                          ]}
                          overrides={{ textarea: '!h-60px' }}
                          placeholder={localize(
                            'input_your_instruction',
                            '输入你的指令，例如：搜索总结科技新闻',
                          )}
                        />
                      )}
                      {type === 'webpage' && (
                        <Controller.Overrides>
                          <Controller.Input
                            name='webpage'
                            validateTrigger={['onBlur']}
                            rules={[
                              {
                                required: true,
                                message: localize(
                                  'please_fill_in_weburl',
                                  '请输入链接',
                                ),
                              },
                              {
                                pattern:
                                  /^[a-zA-Z][a-zA-Z0-9+.-]*:\/\/[^\s<>'"]*$/,
                                message: localize(
                                  'invalid_url_format',
                                  '请输入完整的链接，如：https://www.betteryeah.com',
                                ),
                              },
                            ]}
                            placeholder={localize(
                              'input_your_weburl',
                              '请输入链接',
                            )}
                          />
                        </Controller.Overrides>
                      )}
                      <div className='mb-24px'>
                        <div className='mb-8px'>
                          <span>
                            {localize('shortcut_button_name', '快捷按钮名称')}
                          </span>
                          <span className='text-#FF512B ml-4px'>*</span>
                        </div>
                        <Controller.Input
                          name='short_name'
                          rules={[
                            {
                              required: true,
                              message: localize(
                                'please_enter_shortcut_button_name',
                                '请输入快捷按钮名称',
                              ),
                            },
                          ]}
                          placeholder={localize(
                            'please_enter_shortcut_button_name',
                            '请输入快捷按钮名称',
                          )}
                        />
                      </div>
                    </div>
                    {['flows', 'utility'].includes(type) && (
                      <div className='mb-24px'>
                        <div className='mb-8px'>
                          <span>{localize('input_settings', '输入设置')}</span>
                        </div>
                        <div>
                          <Form.Item
                            className='h-36px [&_.ant-form-item-explain-error]:m-0 [&_.ant-form-item-explain]:mt-4px'
                            name='hidden_parameters'
                            required
                          >
                            <SettingTable
                              columns={[]}
                              dataSource={dataSources}
                            />
                          </Form.Item>
                        </div>
                      </div>
                    )}
                  </div>
                </Form>
              </div>
              <div className='h-60px flex  items-center justify-end px-24px b-t-line b-t-1 b-t-op-30'>
                <Button onClick={modal.remove}>
                  {localize('cancel', '取消')}
                </Button>
                <Button type='primary' className='ml-12px' onClick={handleSave}>
                  {localize('save', '保存')}
                </Button>
              </div>
            </div>
            <div className='flex-1 bg-line bg-op-30 p-16px flex flex-col  relative'>
              <div className='absolute top-0 left-0 right-0 bottom-0 z-10 w-full h-full'></div>
              <div className='text-14px'>{localize('preview', '效果预览')}</div>
              <div className='grid place-items-center h-full z-1'>
                <MessageInput
                  disabled
                  className='w-full'
                  shortcuts={shortcuts}
                  shortcutFormState={shortcutFormState}
                  autofocus={false}
                  renders={messageInputRenders}
                  shortcutPreview={true}
                  onFileUpload={previewFileUpload}
                />
              </div>
            </div>
          </div>
        </Spin>
      </Modal>
    )
  },
)
