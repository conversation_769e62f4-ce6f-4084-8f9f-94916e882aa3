import { localize } from '@bty/localize'
import type { FC } from 'react'
import classNames from 'classnames'
import { DocumentType } from '@apis/datastore/model'
import { SUPPORTED_FILE_TYPE_TEXT } from '@/features/datastore/constant'

interface DocumentTypeSelectProps {
  withTemplate?: boolean
  onSelect: (type: DocumentType) => void
}
export const DocumentTypeSelect: FC<DocumentTypeSelectProps> = props => {
  const { withTemplate = false, onSelect } = props

  return (
    <div>
      {withTemplate && (
        <div
          className='p-16 bg-white border border-line border-opacity-80 hover:bg-bg_3 hover:bg-opacity-8 active:border-primary rounded-8px cursor-pointer'
          onClick={() => {
            onSelect(DocumentType.TEMPLATE_FILE)
          }}
        >
          <p className='font-500 text-14px line-height-16px'>
            {localize('example_document', '示例文档')}
          </p>
          {/* <p className="text-12px mt-8 text-font_1 line-height-1em"></p> */}
        </div>
      )}
      <div
        className={classNames(
          'p-16 bg-white border border-line border-opacity-80 hover:bg-bg_3 hover:bg-opacity-8 active:border-primary rounded-8px cursor-pointer',
          {
            'mt-16': withTemplate,
          },
        )}
        onClick={() => {
          onSelect(DocumentType.FILE)
        }}
      >
        <p className='font-500 text-14px line-height-16px relative flex items-center justify-start'>
          <span>{localize('document', '文档')}</span>
        </p>
        <p className='text-12px mt-8 text-font_1 line-height-22px'>
          {localize('supported_format', '支持格式：')}
          {SUPPORTED_FILE_TYPE_TEXT}
        </p>
      </div>
      <div
        className='p-16 bg-white border border-line border-opacity-80 hover:bg-bg_3 hover:bg-opacity-8 active:border-primary rounded-8px mt-16 cursor-pointer'
        onClick={() => {
          onSelect(DocumentType.TEXT)
        }}
      >
        <p className='font-500 text-14px line-height-16px'>
          {localize('manual_input', '手动输入')}
        </p>
        <p className='text-12px mt-8 text-font_1 line-height-1em'>
          {localize('manual_input_text', '手动输入一些文本信息')}
        </p>
      </div>
      <div
        className='p-16 bg-white border border-line border-opacity-80 hover:bg-bg_3 hover:bg-opacity-8 active:border-primary rounded-8px mt-16 cursor-pointer'
        onClick={() => {
          onSelect(DocumentType.FEISHU)
        }}
      >
        <p className='font-500 text-14px line-height-16px'>
          {localize('fei_shu_input', '飞书')}
        </p>
        <p className='text-12px mt-8 text-font_1 line-height-1em'>
          {localize(
            'fei_shu_input_text',
            '导入飞书文档，仅支持文档，暂不支持表格等其他格式',
          )}
        </p>
      </div>
      <div className='p-16 bg-white border border-line border-opacity-80 rounded-8px mt-16 cursor-not-allowed opacity-60 relative of-hidden'>
        <p className='font-500 text-14px line-height-16px'>
          {localize('ding_input', '钉钉')}
        </p>
        <p className='text-12px mt-8 text-font_1 line-height-1em'>
          {localize('ding_input_text', '导入钉钉文档')}
        </p>
        <div className='bg-#ECEDF3 rounded-bl-8px absolute right-0px top-0px text-12px px-6px py-4px'>
          即将推出
        </div>
      </div>
      <div
        className='p-16 bg-white border border-line border-opacity-80 hover:bg-bg_3 hover:bg-opacity-8 active:border-primary rounded-8px mt-16 cursor-pointer'
        onClick={() => {
          onSelect(DocumentType.WEBPAGE)
        }}
      >
        <p className='font-500 text-14px line-height-16px flex items-center'>
          {localize('webpage', '网页')}
          {/* mufong */}
          {/* <span
            className='w-50px text-10px/10px px-4px py-3px ml-8px rd-10px font-italic text-#fff'
            style={{
              background: 'linear-gradient(270deg, #B92EFF 0%, #FF67FC 100%)',
            }}
          >
            {localize('webpage_engin_update', '引擎升级')}
          </span> */}
        </p>
        <p className='text-12px mt-8 text-font_1 line-height-1em'>
          {localize('input_webpage_url', '输入网页URL地址，获取网页数据')}
        </p>
      </div>
      <div
        className={classNames(
          'mt-16 p-16 bg-white border border-line border-opacity-80 hover:bg-bg_3 hover:bg-opacity-8 active:border-primary rounded-8px cursor-pointer',
        )}
        onClick={() => {
          onSelect(DocumentType.VIDEO)
        }}
      >
        <p className='font-500 text-14px line-height-16px relative flex items-center justify-start'>
          <span>{localize('video', '视频')}</span>
        </p>
        <p className='text-12px mt-8 text-font_1 line-height-22px'>
          {localize('parse_video_content', '根据人声声轨解析视频内容')}
        </p>
      </div>
    </div>
  )
}
