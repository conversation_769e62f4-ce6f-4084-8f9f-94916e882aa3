.third-part-document {
  .third-part-document-regular.ant-select {
    background: none;
    height: 24px !important;
    width: auto !important;

    &.ant-select-open .ant-select-selector {
      background-color: rgb(98 105 153 / 0.08)
    }

    .ant-select-selector {
      height: 24px !important;
      background-color: transparent;
      border-radius: 6px;
      padding: 0 4px;

      &:hover {
        background-color: rgb(98 105 153 / 0.08)
      }
    }

    .ant-select-arrow {
      right: 4px;
    }

    .ant-select-selection-item {
      padding-right: 12px;
    }
  }

  .third-part-document-tab.ant-tabs {
    flex: 1;

    .ant-tabs-nav {
      margin: 0px;
      height: 100%;

      &::before {
        display: none;
      }
    }

    .ant-tabs-ink-bar {
      border-top-left-radius: 2px;
      border-top-right-radius: 2px;
    }
  }

  .third-part-document-tree.ant-tree {
    width: 100%;
    height: 100%;

    .ant-tree-treenode {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0 4px;
      border-radius: 4px;
      height: 32px;
      margin-bottom: 4px;

      &:hover {
        background-color: rgb(98 105 153 / 0.08)
      }
    }

    .ant-tree-indent-unit {
      width: 24px;
    }

    .ant-tree-switcher {
      width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      align-self: center;
      border-radius: 4px;
      margin-right: 8px;
    }

    .ant-tree-checkbox {
      margin-top: 8px;

      .ant-tree-checkbox-inner {
        border-radius: 4px;
      }
    }

    .ant-tree-node-content-wrapper {
      display: flex;
      align-items: center;
      padding: 0;

      &:hover {
        background-color: transparent;
      }
    }
  }
}