import './index.css'

import { memo, useEffect, useState } from 'react'
import type { TabsProps, TreeProps } from 'antd'
import { message, Popconfirm, Spin, Tabs, Tooltip, Tree } from 'antd'
import { DocumentType } from '@apis/datastore/model'
import {
  getFeiShuDocumentList,
  getFeiShuInfo,
  getFeiShuKnowledge,
  getFeiShuMyDocumentList,
  getFeiShuMyDocumentToken,
  unbindFeiShuAccount,
} from '@apis/datastore'
import { useMemoizedFn } from 'ahooks'
import { IconFont } from '@/components'
import { getFileIconByFileType } from '../../utils'
import { updateTreeData } from './util'
import { ThirdPartDocumentAuth } from './auth'

export enum REGULAR_ENUM {
  NONE = 'NONE',
  WORKDAY = 'WORKDAY',
  DAY = 'DAY',
  WEEK = 'WEEK',
  MONTH = 'MONTH',
  CUSTOM = 'CUSTOM',
}

export enum SPACE_ENUM {
  MY_SPACE = 'MY_SPACE',
  KNOWLEDGE = 'KNOWLEDGE',
}

export const REGULAR_TIME_OPTIONS = [
  { value: REGULAR_ENUM.NONE, label: '不更新' },
  { value: REGULAR_ENUM.WORKDAY, label: '每个工作日' },
  { value: REGULAR_ENUM.DAY, label: '每天' },
  { value: REGULAR_ENUM.WEEK, label: '每周' },
  { value: REGULAR_ENUM.MONTH, label: '每月' },
  { value: REGULAR_ENUM.CUSTOM, label: '自定义' },
]

export const REGULAR_TIME_LOOP_OPTIONS = REGULAR_TIME_OPTIONS.filter(
  e => e.value !== REGULAR_ENUM.CUSTOM,
)

const DOCUMENT_TYPE: TabsProps['items'] = [
  {
    key: SPACE_ENUM.MY_SPACE,
    label: '我的空间',
  },
  {
    key: SPACE_ENUM.KNOWLEDGE,
    label: '知识库',
  },
]

interface FeiShuDocumentProps {
  onChange?: (value: {
    list: { id: string; title: string }[]
    regular: REGULAR_ENUM
  }) => void
}

function findInTree(tree: any[], id: string) {
  for (const each of tree) {
    if (each.key === id) return each
    if (each.children) {
      const find = findInTree(each.children, id)
      if (find) return find
    }
  }

  return null
}

export const FeiShuDocument = memo((props: FeiShuDocumentProps) => {
  const { onChange } = props

  const [loading, setLoading] = useState(true)
  const [activeType, setActiveType] = useState(SPACE_ENUM.MY_SPACE)
  const [updateId, setUpdateId] = useState('init')
  const [account, setAccount] = useState<any>()
  // const [reType, setReType] = useState(REGULAR_ENUM.NONE)
  const [knowledgeTree, setKnowledgeTree] = useState([])
  const [knowledgeSKeys, setKnowledgeSKeys] = useState<string[]>([])
  const [myTree, setMyTree] = useState([])
  const [mySKeys, setMySKeys] = useState<string[]>([])

  useEffect(() => {
    const ids = knowledgeSKeys
      .map(each => {
        const node = findInTree(knowledgeTree, each)
        return {
          id: node.fileToken ?? '',
          type: node.fileType,
          title: node.fileTitle,
          node_token: node.key,
          from_field: 'wiki',
        }
      })
      .filter(each => !!each.id)

    const myIds = mySKeys
      .map(each => {
        const node = findInTree(myTree, each)
        return {
          id: node.fileToken ?? '',
          type: node.fileType,
          title: node.fileTitle,
          from_field: 'my-space',
        }
      })
      .filter(each => !!each.id)
    onChange?.({ list: [...ids, ...myIds], regular: REGULAR_ENUM.NONE })
  }, [knowledgeSKeys, mySKeys])

  const initAccount = useMemoizedFn(async (showError = false) => {
    const res = await getFeiShuInfo()
    if (res.is_bound === false) {
      setAccount(undefined)
      if (showError) {
        message.success('已刷新')
      }
      return
    }
    setAccount(res)
  })

  const onRefresh = useMemoizedFn(async () => {
    initAccount(true)
    setKnowledgeSKeys([])
  })

  const onKnowledgeCheck: TreeProps['onCheck'] = useMemoizedFn(
    (checkedKeys: any) => {
      setKnowledgeSKeys(checkedKeys.checked)
    },
  )

  const initKnowledgeTree = useMemoizedFn(async () => {
    setLoading(true)
    try {
      const res = await getFeiShuKnowledge()
      setKnowledgeTree(
        res.items.map(each => {
          return {
            key: each.space_id,
            spaceId: each.space_id,
            type: 'space',
            title: (
              <div className='flex-center'>
                <IconFont name='wenjianga' className='text-16px mr-6px' />
                {each.name}
              </div>
            ),
            checkable: false,
            isLeaf: false,
          }
        }),
      )
    } catch {
      message.error('获取知识库列表失败')
    } finally {
      setLoading(false)
    }
  })

  const onLoadKnowledgeData = useMemoizedFn(
    async ({ key, spaceId, type, children }: any) => {
      if (children) {
        return
      }
      const subDocument = await getFeiShuDocumentList(
        spaceId,
        type === 'node' ? key : '',
      )
      const childTree = subDocument.items.map(each => {
        const disabled = !['doc', 'docx', 'folder'].includes(each.obj_type)
        let content = (
          <div className='flex-center'>
            <img
              className='size-16px mr-6px'
              src={getFileIconByFileType(each.obj_type)}
              alt=''
            />
            {each.title}
          </div>
        )

        if (disabled) {
          content = <Tooltip title='文件类型不支持'>{content}</Tooltip>
        }

        return {
          key: each.node_token,
          spaceId: each.space_id,
          type: 'node',
          fileToken: each.obj_token,
          fileType: each.obj_type,
          fileTitle: each.title,
          title: content,
          disabled,
          isLeaf: !each.has_child,
        }
      })
      setKnowledgeTree(origin => {
        return updateTreeData(origin, key, childTree)
      })
    },
  )

  const initMyDocument = useMemoizedFn(async () => {
    setLoading(true)
    try {
      const res = await getFeiShuMyDocumentToken()
      const list = await getFeiShuMyDocumentList(res.token)

      setMyTree(
        list.files.map(each => {
          const disabled = !['doc', 'docx', 'folder'].includes(each.type)
          let content = (
            <div className='flex-center'>
              {each.type === 'folder' ? (
                <IconFont name='wenjianga' className='text-16px mr-6px' />
              ) : (
                <img
                  className='size-16px mr-6px'
                  src={getFileIconByFileType(each.type)}
                  alt=''
                />
              )}
              {each.name}
            </div>
          )

          if (disabled) {
            content = <Tooltip title='文件类型不支持'>{content}</Tooltip>
          }

          return {
            key: each.token,
            type: 'my',
            fileToken: each.token,
            fileType: each.type,
            fileTitle: each.name,
            title: content,
            disabled,
            checkable: each.type !== 'folder',
            isLeaf: each.type !== 'folder',
          }
        }),
      )
    } catch {
      message.error('获取我的空间文件失败')
    } finally {
      setLoading(false)
    }
  })

  const onLoadMyDocumentData = useMemoizedFn(async ({ key, children }: any) => {
    if (children) {
      return
    }

    const subDocument = await getFeiShuMyDocumentList(key)
    const childTree = subDocument.files.map(each => {
      const disabled = !['doc', 'docx', 'folder'].includes(each.type)
      let content = (
        <div className='flex-center'>
          {each.type === 'folder' ? (
            <IconFont name='wenjianga' className='text-16px mr-6px' />
          ) : (
            <img
              className='size-16px mr-6px'
              src={getFileIconByFileType(each.type)}
              alt=''
            />
          )}
          {each.name}
        </div>
      )

      if (disabled) {
        content = <Tooltip title='文件类型不支持'>{content}</Tooltip>
      }

      return {
        key: each.token,
        type: 'my',
        fileType: each.type,
        fileTitle: each.name,
        title: content,
        disabled,
        checkable: each.type !== 'folder',
        isLeaf: each.type !== 'folder',
      }
    })

    setMyTree(origin => {
      return updateTreeData(origin, key, childTree)
    })
  })

  const onMyDocumentCheck: TreeProps['onCheck'] = useMemoizedFn(
    (checkedKeys: any) => {
      setMySKeys(checkedKeys.checked)
    },
  )

  const initTree = useMemoizedFn(async () => {
    setUpdateId(Date.now().toString())
    setKnowledgeSKeys([])
    setKnowledgeTree([])
    await initKnowledgeTree()
    await initMyDocument()
  })

  const onUnbind = useMemoizedFn(async () => {
    await unbindFeiShuAccount()
    setAccount(undefined)
    setKnowledgeSKeys([])
  })

  useEffect(() => {
    initAccount()
  }, [])

  useEffect(() => {
    if (!account) return
    initTree()
  }, [account])

  if (!account) {
    return (
      <ThirdPartDocumentAuth type={DocumentType.FEISHU} onRefresh={onRefresh} />
    )
  }

  return (
    <div className='mt-[-16px] third-part-document'>
      <div className='flex mb-12px'>
        <div className='flex h-24px items-center mr-auto'>
          <span className='c-[#8D8D99]'>账号</span>
          <span className='mx-4px'>{account?.provider_user_info?.name}</span>
          <Popconfirm
            icon={false}
            placement='bottom'
            style={{
              width: '200px',
            }}
            title={
              <p className='w-200px pb-12px font-bold lh-20px'>
                是否确认解绑账号？
              </p>
            }
            cancelButtonProps={{ size: 'middle' }}
            okText='解绑'
            okButtonProps={{ danger: true, size: 'middle' }}
            onConfirm={onUnbind}
          >
            <Tooltip title='解绑账号'>
              <span className='c-[#8D8D99] size-24px flex-center cursor-pointer hover:bg-bg_3/8 rd-4px'>
                <IconFont name='yongqi' />
              </span>
            </Tooltip>
          </Popconfirm>
        </div>
        {/* <div className='flex h-24px items-center'>
          <span className='c-[#8D8D99]'>更新设置</span>
          <Tooltip title='选择更新方式，更新会覆盖当前文档内容'>
            <IconFont
              name='botxiangqing'
              className='c-[#8D8D99]/40 mx-4px cursor-pointer'
            />
          </Tooltip>
          <Select
            variant='borderless'
            className='third-part-document-regular'
            popupMatchSelectWidth={140}
            value={reType}
            options={REGULAR_TIME_LOOP_OPTIONS}
            onChange={setReType}
          />
        </div> */}
      </div>

      <div className='h-400px rd-8px b-1px b-[rgba(225,225,229,0.8)] flex flex-col items-center justify-center of-hidden'>
        <div className='w-full h-36px pl-16px pr-6px bg-[#626999]/4 flex items-center'>
          <Tabs
            className='third-part-document-tab'
            items={DOCUMENT_TYPE}
            activeKey={activeType}
            onChange={setActiveType as any}
          />
          <span
            className='c-[#8D8D99] size-24px flex-center cursor-pointer hover:bg-bg_3/8 rd-4px'
            onClick={initTree}
          >
            <IconFont name='tongshi' className='c-[#8D8D99]/60' />
          </span>
        </div>
        <div className='flex-1 w-full p-8px of-auto'>
          {loading && <Spin className='size-full flex-center' />}
          {activeType === SPACE_ENUM.MY_SPACE && !loading && (
            <Tree
              key={updateId}
              className='third-part-document-tree'
              checkable
              blockNode
              checkStrictly
              selectable={false}
              checkedKeys={mySKeys}
              onCheck={onMyDocumentCheck}
              treeData={myTree}
              loadData={onLoadMyDocumentData}
            />
          )}
          {activeType === SPACE_ENUM.KNOWLEDGE && !loading && (
            <Tree
              key={updateId}
              className='third-part-document-tree'
              checkable
              blockNode
              checkStrictly
              selectable={false}
              checkedKeys={knowledgeSKeys}
              onCheck={onKnowledgeCheck}
              treeData={knowledgeTree}
              loadData={onLoadKnowledgeData}
            />
          )}
        </div>
        <div className='h-36px w-full b-t-1px b-[rgba(225,225,229,0.8)] flex-center px-12px'>
          <span className='ml-auto c-[#8D8D99] text-12px'>
            已选{' '}
            <span className='text-#17171d'>
              {knowledgeSKeys.length + mySKeys.length}
            </span>
            /20 个文档
          </span>
        </div>
      </div>
    </div>
  )
})
