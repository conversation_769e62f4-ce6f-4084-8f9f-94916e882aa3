import './index.css'

import { memo, useEffect, useState } from 'react'
import { DocumentType } from '@apis/datastore/model'
import { bindFeiShuAccount } from '@apis/datastore'
import { useMemoizedFn } from 'ahooks'
import feiShuIcon from '@/assets/dataset/feishu-link.png'
import dingIcon from '@/assets/dataset/dingding-link.png'
import { useWorkspaceStore } from '@/store/workspace'

interface ThirdPartDocumentAuthProps {
  type: DocumentType.FEISHU | DocumentType.DING
  onRefresh?: () => void
}

const ICON_MAP = {
  [DocumentType.FEISHU]: feiShuIcon,
  [DocumentType.DING]: dingIcon,
}

const NAME_MAP = {
  [DocumentType.FEISHU]: '飞书',
  [DocumentType.DING]: '钉钉',
}

export const ThirdPartDocumentAuth = memo(
  (props: ThirdPartDocumentAuthProps) => {
    const { type, onRefresh } = props

    const currentWorkspaceId = useWorkspaceStore(s => s.currentWorkspaceId)
    const [authUrl, setAuthUrl] = useState('')

    const binding = useMemoizedFn(async () => {
      if (type === DocumentType.FEISHU) {
        const info = await bindFeiShuAccount()
        setAuthUrl(info.authorize_url)
      }
    })

    useEffect(() => {
      binding()
    }, [])

    return (
      <div className='rd-8px b-1px b-[rgba(225,225,229,0.8)] p-46px flex flex-col items-center justify-center gap-24px mt-[-16px]'>
        <div className='flex-center flex-col gap-12px'>
          <div className='size-54px'>
            <img className='size-full' src={ICON_MAP[type]} alt='' />
          </div>
          <h3 className='font-500'>{NAME_MAP[type]}未绑定</h3>
        </div>

        <div>
          <span>请先配置</span>
          <a
            target='_blank'
            href={`/workspace/${currentWorkspaceId}/settings/dataSource`}
            className='c-[#7B61FF] mx-4px cursor-pointer'
            rel='noreferrer'
          >
            飞书数据源
          </a>
          {authUrl && <span>，再</span>}
          {authUrl && (
            <a
              target='_blank'
              href={authUrl}
              className='c-[#7B61FF] mx-4px cursor-pointer'
              rel='noreferrer'
            >
              绑定账号
            </a>
          )}
        </div>

        <div className='c-[#8D8D99]'>
          <span>如果已经绑定，请</span>
          <span
            className='c-[#7B61FF] mx-4px cursor-pointer'
            onClick={onRefresh}
          >
            刷新
          </span>
          <span>开始导入文档</span>
        </div>
      </div>
    )
  },
)
