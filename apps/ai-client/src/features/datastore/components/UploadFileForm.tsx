import { localize } from '@bty/localize'
import { Form, message } from 'antd'
import type { UploadFileStatus } from 'antd/es/upload/interface'
import { useMemoizedFn, useRequest } from 'ahooks'
import { getQATemplate } from '@apis/datastore'
import { DocumentParseType, DocumentType } from '@apis/datastore/model'
import { Controller } from '@bty/components'
import { PartitionCategoryType } from '@apis/datastore/type'
import { ErrorBoundaryUnknown } from '@bty/monitor'
import { IconFont, FormItem, Button } from '@/components'
import { getFileSuffixByMime } from '@/features/datastore/utils'
import { DocumentParseSelect } from '../components/DocumentParseSelect.tsx'
import type { ParseOption } from '../components/DocumentParseSelect.tsx'
import { DocumentUpload, defaultNormaleFileType } from './DocumentUpload.tsx'
import { DocumentSegmentMode } from './DocumentSegmentMode.tsx'
import { FeiShuDocument } from './ThirdPartDocument/feishu.tsx'

const multiModalParseOptions: ParseOption[] = [
  {
    value: DocumentParseType.AI_PARSING,
    label: localize('ai_parse', 'AI解析'),
    description: localize(
      'automatically_generate_image_using_ai',
      '通过AI填写图片说明，适用于使用图片中物体、风格、风景等描述作为图片说明的情况',
    ),
  },
  {
    value: DocumentParseType.OCR_PARSING,
    label: localize('ocr_parse', 'OCR解析'),
    description: localize(
      'automatically_generate_image_using_ocr',
      '通过OCR识别图片文字，填写图片说明，适用于直接使用图片中的文字作为图片说明的情况',
    ),
  },
]

// const WEB_FETCH_OPTION = [
//   {
//     value: WebpageParseType.TURBO,
//     label: localize('web_fetch_turbo', '涡轮模式'),
//     desc: localize('web_fetch_turbo_desc', '公众号、B站等推荐'),
//   },
//   {
//     value: WebpageParseType.NORMAL,
//     label: localize('web_fetch_normal', '普通模式'),
//     desc: localize('web_fetch_normal_desc', '展示原文，不做处理'),
//   },
// ]

export const parseTypeSupportMaps = {
  [DocumentParseType.GENERAL_PARSE]: defaultNormaleFileType,
  [DocumentParseType.VISUAL_PARSE]: [
    '.doc',
    '.docx',
    '.pdf',
    '.ppt',
    '.pptx',
    '.png',
    '.jpg',
    '.jpeg',
  ],
  [DocumentParseType.AI_PARSING]: ['.jpg', '.jpeg', '.png', '.bmp', '.tiff'],
  [DocumentParseType.OCR_PARSING]: ['.jpg', '.jpeg', '.png', '.bmp', '.tiff'],
}

interface Props {
  documentType?: DocumentType
  hashPath: string
  datastoreType?: PartitionCategoryType
  partitionModelMaxLength?: number
  getFileStatus?: (e?: UploadFileStatus) => void
}
export const MAX_FILE_COUNT = 50
export function UploadFileForm(props: Props) {
  const {
    documentType,
    hashPath,
    datastoreType,
    partitionModelMaxLength,
    getFileStatus,
  } = props
  const { run: getQAOssUrl, loading: getQaLoading } = useRequest(
    getQATemplate,
    {
      manual: true,
    },
  )
  const form = Form.useFormInstance()
  const parseType =
    Form.useWatch('parseType') || DocumentParseType.GENERAL_PARSE

  const supportFileType = parseTypeSupportMaps[parseType]
  const fileListCount = Form.useWatch('fileList')?.length ?? 0

  const handleBeforeChange = useMemoizedFn((value: DocumentParseType) => {
    if (value !== DocumentParseType.VISUAL_PARSE) {
      return Promise.resolve()
    }
    const fileList = form.getFieldValue('fileList') || []
    for (const file of fileList) {
      if (file.file_status === 'uploading') {
        message.warning(
          localize('file_uploading', '文件正在上传中，请上传成功后操作'),
        )
        throw new Error(
          localize('file_uploading', '文件正在上传中，请上传成功后操作'),
        )
      }
    }
    const supportTypeList = parseTypeSupportMaps[value]
    const unsupportedFileList = fileList
      .filter(file => {
        const suffix = `.${getFileSuffixByMime(file.mimetype)}`
        // 检查文件扩展名是否在支持的类型列表中
        return !supportTypeList.includes(suffix)
      })
      .map(file => file.file_name)
    if (unsupportedFileList?.length) {
      message.error(
        <div className='text-14px/20px' style={{ whiteSpace: 'pre-line' }}>
          <p className='mb-8px'>
            {localize(
              'the_following_file_formats_are_not_supported_and_cannot_switch_the_parsing_method',
              '以下文件格式不支持解析 无法切换解析方式:',
            )}
          </p>
          {unsupportedFileList.map((item, index) => {
            return (
              <p key={index} className='text-14px/20px'>
                {item}
              </p>
            )
          })}
        </div>,
      )
      throw new Error(
        localize(
          'the_current_file_format_is_not_supported_and_cannot_switch_the_parsing_method',
          '当前存在不支持解析的文件格式',
        ),
      )
    }
    return Promise.resolve()
  })

  // const renderWithDesc = useMemoizedFn((option: any) => {
  //   return (
  //     <p className='flex items-center'>
  //       {option.label}
  //       <small className='text-#8D8D99 ml-4px'>{option.data.desc}</small>
  //     </p>
  //   )
  // })

  switch (documentType) {
    case DocumentType.FILE:
      return (
        <ErrorBoundaryUnknown
          message={localize(
            'UploadFileForm.FILE.unknown_error',
            'UploadFileForm.FILE 发生未知错误',
          )}
        >
          <div>
            <Controller
              overrides={{
                root: 'mb-[16px] [&_.ant-select]:w-full',
                label: 'font-normal',
              }}
              label={localize('parsing_method', '解析方式')}
              name='parseType'
              vertical={false}
            >
              <DocumentParseSelect handleBeforeChange={handleBeforeChange} />
            </Controller>
            {datastoreType === PartitionCategoryType.Document && (
              <DocumentSegmentMode maxLength={partitionModelMaxLength ?? 500} />
            )}
            <FormItem
              name='fileList'
              className='mb-0'
              rules={[
                {
                  validator(_, value) {
                    if (!value || value.length === 0) {
                      return Promise.reject(
                        new Error(
                          localize(
                            'please_upload_at_least_one_file',
                            '至少上传一个文件',
                          ),
                        ),
                      )
                    }
                    for (const file of value) {
                      if (file.file_status === 'uploading') {
                        return Promise.reject(
                          new Error(
                            localize(
                              'the_file_is_being_uploaded_please_submit_after_uploading',
                              '文件正在上传中，上传完成后提交',
                            ),
                          ),
                        )
                      }
                    }
                    return Promise.resolve()
                  },
                },
              ]}
            >
              <DocumentUpload
                namespace='partition'
                business='dataset'
                hashPath={hashPath}
                maxCount={MAX_FILE_COUNT}
                disabled={fileListCount === MAX_FILE_COUNT}
                placeholder={`${localize(
                  'supported_formats',
                  '支持格式：',
                )}${supportFileType.join(', ').replaceAll('.', '')} \n${localize(
                  'maximum_support_file_count',
                  '（最多支持',
                )}${MAX_FILE_COUNT}${localize(
                  'file_count',
                  '份，建议每份不超过10W字）',
                )}`}
                fileAccept={supportFileType}
                getFileStatus={getFileStatus}
              />
            </FormItem>
          </div>
        </ErrorBoundaryUnknown>
      )
    case DocumentType.TEXT:
      return (
        <>
          <Controller.Overrides font={400}>
            <Controller.Input
              name='textTitle'
              label={
                <>
                  {localize('title', '标题')}
                  <span className='text-font_1'>
                    {localize('optional', '（选填）')}
                  </span>
                </>
              }
              maxLength={20}
              showCount
            />
          </Controller.Overrides>
          <Controller.Overrides font={400} mb={0}>
            <Controller.Textarea
              name='textContent'
              label={localize('text_information', '文本信息')}
              rules={[
                {
                  required: true,
                  message: localize(
                    'text_information_cannot_be_empty',
                    '文本信息不能为空',
                  ),
                },
              ]}
              rows={3}
              maxLength={1000}
              showCount
            />
          </Controller.Overrides>
        </>
      )
    case DocumentType.WEBPAGE:
      return (
        <div className='mt--10px'>
          {/* mufong */}
          {/* <FormItem
            name='webParseType'
            label='选择抓取模式'
            initialValue={WebpageParseType.TURBO}
          >
            <Select options={WEB_FETCH_OPTION} optionRender={renderWithDesc} />
          </FormItem> */}
          <Form.List name='webUrl' initialValue={['']}>
            {(fields, { add, remove }) => {
              return (
                <>
                  <div className='flex justify-between items-center mb-8px'>
                    <div className='whitespace-nowrap'>
                      <p className='inline color-#17171D'>
                        {localize('input_webpage_url', '输入网页URL地址')}
                      </p>
                      <p className='inline color-#FF5219'> *</p>
                    </div>
                    <span className='size-24px flex-center cursor-pointer hover:bg-bg_3/8 rd-4px'>
                      <IconFont name='add' onClick={() => add('')} />
                    </span>
                  </div>
                  {fields.map((field, index) => (
                    <Controller.Overrides key={field.key} mb='8px'>
                      <Controller.Input
                        name={field.name}
                        rules={[
                          {
                            required: true,
                            message: localize(
                              'please_enter_the_url_address',
                              '请输入URL地址',
                            ),
                          },
                          {
                            type: 'url',
                            message: localize(
                              'the_webpage_url_format_is_incorrect_please_enter_the_correct_webpage_url_address',
                              '网页URL地址格式错误，请输入正确的网址URL地址',
                            ),
                          },
                        ]}
                        suffix={
                          fields.length > 1 ? (
                            <IconFont
                              name='shanshu'
                              className='cursor-pointer'
                              onClick={() => remove(index)}
                            />
                          ) : null
                        }
                      />
                    </Controller.Overrides>
                  ))}
                </>
              )
            }}
          </Form.List>
        </div>
      )
    case DocumentType.QA:
      return (
        <div>
          <Button
            size='large'
            style={{ height: '36px' }}
            loading={getQaLoading}
            className='mb-16'
            onClick={() => {
              getQAOssUrl(localize('qa_template', 'QA问答模版'))
            }}
            icon={<IconFont name='xiazai' className='text-16px' />}
          >
            {localize('download_template', '下载模板')}
          </Button>
          <FormItem
            name='fileList'
            className='mb-0'
            rules={[
              {
                validator(_, value) {
                  if (!value || value.length === 0) {
                    return Promise.reject(
                      new Error(
                        localize(
                          'please_upload_at_least_one_file',
                          '至少上传一个文件',
                        ),
                      ),
                    )
                  }
                  for (const file of value) {
                    if (file.file_status === 'uploading') {
                      return Promise.reject(
                        new Error(
                          localize(
                            'the_file_is_being_uploaded_please_submit_after_uploading',
                            '文件正在上传中，上传完成后提交',
                          ),
                        ),
                      )
                    }
                  }
                  return Promise.resolve()
                },
              },
            ]}
          >
            <DocumentUpload
              namespace='partition'
              hashPath={hashPath}
              business='dataset'
              placeholder={localize(
                'please_use_the_template_to_upload_otherwise_it_will_be_parsed_failed',
                '请使用模板上传，否则会解析失败',
              )}
              fileAccept={['.xlsx']}
            />
          </FormItem>
        </div>
      )
    case DocumentType.VIDEO:
      return (
        <div>
          <FormItem
            name='fileList'
            className='mb-0'
            rules={[
              {
                validator(_, value) {
                  if (!value || value.length === 0) {
                    return Promise.reject(
                      new Error(
                        localize(
                          'please_upload_at_least_one_file',
                          '至少上传一个文件',
                        ),
                      ),
                    )
                  }
                  for (const file of value) {
                    if (file.file_status === 'uploading') {
                      return Promise.reject(
                        new Error(
                          localize(
                            'the_file_is_being_uploaded_please_submit_after_uploading',
                            '文件正在上传中，上传完成后提交',
                          ),
                        ),
                      )
                    }
                  }
                  return Promise.resolve()
                },
              },
            ]}
          >
            <DocumentUpload
              namespace='partition'
              hashPath={hashPath}
              business='dataset'
              maxCount={5}
              placeholder={`${localize(
                'supported_formats',
                '支持格式：',
              )}${supportFileType.join(', ').replaceAll('.', '')} \n${localize(
                'maximum_support_file_count',
                '（最多支持',
              )}${MAX_FILE_COUNT}${localize(
                'file_count',
                '份，建议每份不超过10W字）',
              )}`}
              fileAccept={[
                '.mp4',
                '.mov',
                '.wmv',
                '.flv',
                '.avi',
                '.webm',
                '.mkv',
              ]}
            />
          </FormItem>
        </div>
      )
    case DocumentType.FEISHU:
      return (
        <FormItem name='feiShuConfig' className='mb-0'>
          <FeiShuDocument />
        </FormItem>
      )

    default:
      return null
  }
}

export function UploadQAFileForm(props: Props) {
  const { hashPath } = props
  const { run: getQAOssUrl, loading: getQaLoading } = useRequest(
    getQATemplate,
    {
      manual: true,
    },
  )
  const fileListCount = Form.useWatch('fileList')?.length ?? 0
  return (
    <div>
      <div className='flex items-center mb-16px justify-between'>
        <div className='text-12px/16px c-#8D8D99 flex-center'>
          <span className='text-16px/16px font-500 c-#17171D'>
            {localize('upload_file', '上传文件')}
          </span>{' '}
          <span>
            {localize('file_count', '（已添加')}
            {fileListCount}/{MAX_FILE_COUNT}）
          </span>
        </div>
        <Button
          size='large'
          style={{ height: '36px' }}
          loading={getQaLoading}
          onClick={() => {
            getQAOssUrl(localize('qa_template', 'QA问答模版'))
          }}
          icon={<IconFont name='xiazai' className='text-16px' />}
        >
          {localize('download_template', '下载模板')}
        </Button>
      </div>
      <FormItem
        name='fileList'
        className='mb-0'
        rules={[
          {
            validator(_, value) {
              if (!value || value.length === 0) {
                return Promise.reject(
                  new Error(
                    localize(
                      'please_upload_at_least_one_file',
                      '至少上传一个文件',
                    ),
                  ),
                )
              }
              for (const file of value) {
                if (file.file_status === 'uploading') {
                  return Promise.reject(
                    new Error(
                      localize(
                        'the_file_is_being_uploaded_please_submit_after_uploading',
                        '文件正在上传中，上传完成后提交',
                      ),
                    ),
                  )
                }
              }
              return Promise.resolve()
            },
          },
        ]}
      >
        <DocumentUpload
          namespace='partition'
          hashPath={hashPath}
          business='dataset'
          maxCount={MAX_FILE_COUNT}
          disabled={fileListCount === MAX_FILE_COUNT}
          placeholder={localize(
            'upload_placeholder',
            `支持xlsx；推荐使用【QA问答模板】上传，否则会解析失败\n（最多支持${MAX_FILE_COUNT}份，建议每份不超过10W字）`,
            MAX_FILE_COUNT,
          )}
          fileAccept={['.xlsx']}
        />
      </FormItem>
    </div>
  )
}

export function UploadMultiModalForm(props: Props) {
  const { hashPath, getFileStatus } = props

  const form = Form.useFormInstance()

  const parseType = Form.useWatch('parseType') || DocumentParseType.AI_PARSING

  const supportFileType = parseTypeSupportMaps[parseType]

  const fileListCount = Form.useWatch('fileList')?.length ?? 0

  const handleBeforeChange = (value: DocumentParseType) => {
    if (value !== DocumentParseType.VISUAL_PARSE) {
      return Promise.resolve()
    }
    const fileList = form.getFieldValue('fileList') || []
    for (const file of fileList) {
      if (file.file_status === 'uploading') {
        message.warning(
          localize('file_uploading', '文件正在上传中，请上传成功后操作'),
        )
        throw new Error(
          localize('file_uploading', '文件正在上传中，请上传成功后操作'),
        )
      }
    }
    const supportTypeList = parseTypeSupportMaps[value]
    const unsupportedFileList = fileList
      .filter(file => {
        const suffix = `.${getFileSuffixByMime(file.mimetype)}`
        // 检查文件扩展名是否在支持的类型列表中
        return !supportTypeList.includes(suffix)
      })
      .map(file => file.file_name)
    if (unsupportedFileList?.length) {
      message.error(
        <div className='text-14px/20px' style={{ whiteSpace: 'pre-line' }}>
          <p className='mb-8px'>
            {localize(
              'the_following_file_formats_are_not_supported_and_cannot_switch_the_parsing_method',
              '以下文件格式不支持解析 无法切换解析方式:',
            )}
          </p>
          {unsupportedFileList.map((item, index) => {
            return (
              <p key={index} className='text-14px/20px'>
                {item}
              </p>
            )
          })}
        </div>,
      )
      throw new Error(
        localize(
          'the_current_file_format_is_not_supported_and_cannot_switch_the_parsing_method',
          '当前存在不支持解析的文件格式',
        ),
      )
    }
    return Promise.resolve()
  }

  return (
    <div>
      <Controller
        overrides={{
          root: 'mb-[16px] [&_.ant-select]:w-full',
          label: 'font-normal',
        }}
        label={localize('parsing_method', '解析方式')}
        name='parseType'
        vertical={true}
      >
        <DocumentParseSelect
          parseOptions={multiModalParseOptions}
          handleBeforeChange={handleBeforeChange}
        />
      </Controller>
      <FormItem
        name='fileList'
        className='mb-0'
        rules={[
          {
            validator(_, value) {
              if (!value || value.length === 0) {
                return Promise.reject(
                  new Error(
                    localize(
                      'please_upload_at_least_one_file',
                      '至少上传一个文件',
                    ),
                  ),
                )
              }
              for (const file of value) {
                if (file.file_status === 'uploading') {
                  return Promise.reject(
                    new Error(
                      localize(
                        'the_file_is_being_uploaded_please_submit_after_uploading',
                        '文件正在上传中，上传完成后提交',
                      ),
                    ),
                  )
                }
              }
              return Promise.resolve()
            },
          },
        ]}
      >
        <DocumentUpload
          namespace='partition'
          hashPath={hashPath}
          business='dataset'
          maxCount={MAX_FILE_COUNT}
          disabled={fileListCount === MAX_FILE_COUNT}
          fileAccept={supportFileType}
          placeholder={`${localize(
            'supported_formats',
            '支持格式：',
          )}${supportFileType.join(', ').replaceAll('.', '')} \n${localize(
            'maximum_support_file_count',
            '（最多支持',
          )}${MAX_FILE_COUNT}${localize('file_count', '张，建议每张最大5MB）')}`}
          getFileStatus={getFileStatus}
          maxFileSize={5}
        />
      </FormItem>
    </div>
  )
}
