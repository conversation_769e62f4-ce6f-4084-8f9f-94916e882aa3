import { localize } from '@bty/localize'
import type { ColumnsType } from 'antd/lib/table'
import {
  useRequest,
  useDebounceFn,
  useMount,
  useLockFn,
  useDebounceEffect,
  useUpdateEffect,
} from 'ahooks'
import dayjs from 'dayjs'
import React, {
  useState,
  type Key,
  useMemo,
  useEffect,
  useCallback,
  useImperativeHandle,
  useRef,
  useContext,
} from 'react'
import copy from 'copy-to-clipboard'

import classNames from 'classnames'
import styled from '@emotion/styled'
import { useLocation, useNavigate, useOutletContext } from 'react-router-dom'
import type { TableProps } from 'antd'
import { Tag, message, Tooltip, Divider, Pagination, Switch } from 'antd'
import { clone, find, isNil } from 'lodash-es'
import { isString } from 'markdown-it/lib/common/utils'
import {
  getDocumentList,
  getMultiModalList,
  deleteDocuments,
  fileRetry,
  getDatasetTags,
  getDataStoreList,
  copyDocumentsToDataset,
  generateAISummaryByChunkId,
  downloadFailReasonFile,
  updateDocument,
  deleteFailedDocumentsByPartitionId,
  exportQADocuments,
  reProcessFile,
  getBatchMultiModalFileProgress,
} from '@apis/datastore'
import { DocumentStatus, DocumentType } from '@apis/datastore/model'
import type {
  DataStoreItem,
  DocumentItem,
  IDocumentListReq,
  MultiModalItem,
} from '@apis/datastore/type'
import { PartitionCategoryType } from '@apis/datastore/type'
import download from 'downloadjs'
import { ExpiredStatus } from '@bty/global-types/knowledge'
import {
  Button,
  ConfirmModal,
  IconFont,
  Table,
  usePageModal,
} from '@/components'
import useUrlState from '@/hooks/useUrlState'
import { useDataStoreCreate } from '@/features/datastore/hooks/useDataStoreCreate.ts'
import { useModal } from '@/hooks/useModal.tsx'
import { CreateDataStoreDocumentsModal } from '@/features/datastore/CreateDataStoreModal/DocumentsModal'
import { CopyDocumentsModal } from '@/features/datastore/CopyDocumentsModal'

import { SearchableTagEditorModal } from '@/pages/datastores/components/SearchableTagEditorModal'
import { useVersionStore, useWorkspaceStore, useUserStore } from '@/store'
import feishuTip from '@/assets/dataset/feishu-tip.svg'

import { Track } from '../track'
import { DocumentTagList } from '@/features/datastore/components/DocumentTagList'
import { LimitedAccess } from '../pay/LimitedAccess'
import { isReadonly, isReadwrite, usePermissions } from '@/components/acl'
import theme from '@/constants/theme'
import { getDatastorePath } from '@/router/datastore'
import { DataStoreContext } from '@/pages/datastores/provider/DataStoreProvider.tsx'
import {
  formatFileSize,
  formatSize,
  getFileDisplayName,
  getFileIconByFileTypeOrMimeType,
} from './utils'
import { useFileProcessWatcher } from './utils/useFIleProcessWatcher'

import { DATASTORE_MAX_TAG_COUNT, DocumentStatusMap } from './constant'
import { StatusTag } from './components/StatusTag'
import { DocumentsSearch } from './DocumentsSearch'
import { MultimodalDatastore } from './components/MultimodalDatastore'
import { DocumentsExpired } from './DocumentsExpired'

interface ProgressResponse {
  [key: string]: {
    status: DocumentStatus
    progress: {
      current: number
      total: number
      status: string
      percentage: number
    }
    enable?: boolean
    data?: MultiModalItem[]
    contents?: MultiModalItem[]
  }
}

const StyledTable = styled(Table)<{ isEmpty?: boolean }>`
  .ant-table-column-sorters {
    justify-content: flex-start !important;
  }

  .ant-table-column-title {
    flex: initial;
  }
  .ant-table-cell-fix-right {
    background: white !important;
  }
  .ant-table-sticky-scroll-bar {
    width: 0px;
    height: 0px;
  }

  .ant-table-body {
    overflow: auto !important;
  }

  .ant-table-sticky-scroll {
    height: 0px !important;
  }

  ${({ isEmpty }) => {
    if (isEmpty) {
      return `.ant-table-body{
          overflow: hidden !important;
        }`
    } else {
      return ''
    }
  }}
`

const StyledPagination = styled(Pagination)`
  .ant-select-selector {
    box-shadow: none !important;
  }
  .ant-pagination-options-quick-jumper {
    input {
      box-shadow: none !important;
    }
  }
  .ant-pagination-item-active {
    box-shadow: none !important;
  }
`

export const DocumentTypeMap: Record<DocumentType, string> = {
  [DocumentType.FILE]: localize('file', '文件'),
  [DocumentType.TEXT]: localize('text', '文本'),
  [DocumentType.WEBPAGE]: localize('webpage', '网页'),
  [DocumentType.TEMPLATE_FILE]: localize('template_file', '模版文件'),
  [DocumentType.QA]: localize('qa', '问答'),
  [DocumentType.VIDEO]: localize('video', '视频'),
  [DocumentType.FEISHU]: localize('feishu', '飞书'),
  [DocumentType.DING]: localize('ding', '钉钉'),
}

type Columns =
  | 'file_name'
  | 'file_size'
  | 'extracted_text_length'
  | 'update_time'
  | 'status'
  | 'tags'
  | 'segment_count'
  | 'enable'
  | 'update_user'
interface DocumentsProps {
  hideHeader?: boolean
  openDocumentInNewWindow?: boolean
  datastoreHashPath: string
  id: number
  datastoreName?: string
  datastoreType?: PartitionCategoryType
  className?: string
  fields?: Columns[]
  dateFormat?: string
  widthMap?: { [key in Columns]?: number }
  showExtraSearch?: boolean
  onRowClick?: (file_id: number) => void
  onShowDetail?: (file_id: number) => boolean | undefined
  datastoreStatus: DataStoreItem['status']
  handleRecoverDataStore: () => void
}

type OnTableChange = NonNullable<TableProps<Columns>['onChange']>

type GetSingle<T> = T extends (infer U)[] ? U : never
type Sorts = GetSingle<Parameters<OnTableChange>[2]>

const LinkText = styled.a`
  &:hover {
    .link {
      opacity: 1;
    }
  }
`

function SortIcon(props: { sortOrder: 'asc' | 'desc' }) {
  const { sortOrder } = props
  return (
    <div className='ml-8'>
      <IconFont
        name='asc'
        className={classNames('text-6px text-font_1', {
          'text-opacity-40': sortOrder !== 'asc',
        })}
      />
      <IconFont
        name='desc'
        className={classNames('text-6px text-font_1', {
          'text-opacity-40': sortOrder !== 'desc',
        })}
      />
    </div>
  )
}

export interface DocumentsInstance {
  create: (afterCreate?: VoidFunction) => void
  onSearch: (params: { file_name: string; [key: string]: any }) => void
}

const DEFAULT_PAGE_OPTIONS = {
  page: 1,
  page_size: 20,
  file_name: '',
  tags: [],
  file_status: -1,
  sort_order: '',
  sort_by: '',
}

function Documents(
  props: DocumentsProps,
  ref: React.ForwardedRef<DocumentsInstance>,
) {
  const {
    hideHeader,
    id,
    openDocumentInNewWindow,
    fields,
    widthMap,
    dateFormat,
    datastoreHashPath,
    showExtraSearch = false,
    onRowClick,
    onShowDetail,
    datastoreName,
    datastoreType,
    handleRecoverDataStore,
  } = props

  const outletProps = useOutletContext<{ readwrite?: boolean }>()

  const permission = usePermissions({
    type: 'DATASET',
    id,
  })

  const readwrite = outletProps?.readwrite || isReadwrite(permission)
  const navigate = useNavigate()
  const [retryFileId, setRetryFileId] = useState(-1)
  const location = useLocation()
  const { dataStoreInfo } = useContext(DataStoreContext)
  const isMultiModalRepo = useMemo(
    () => datastoreType === PartitionCategoryType.Multimodal,
    [datastoreType],
  )
  const { onCreate: onDocumentCreateApi, loading: documentCreateLoading } =
    useDataStoreCreate()
  const [createDataStoreDocumentsModal] = useModal(
    CreateDataStoreDocumentsModal,
    {
      okButtonProps: { loading: documentCreateLoading },
      uploadFileHashPath: datastoreHashPath,
      datastoreType,
      partitionModelMaxLength: dataStoreInfo?.partition_model_max_chunk_length,
    },
  )

  const isMultiRepo = datastoreType === PartitionCategoryType.Multimodal

  const wrapperRef = useRef(null)

  const [tagOptions, setTagOptions] = useState<any>([])

  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([])

  const selectOptions = useMemo(() => {
    const statusItems = Object.entries(DocumentStatusMap)
      .map(([key, { text }]) => ({
        label: text,
        value: Number(key),
      }))
      .filter(
        item =>
          item.value !== DocumentStatus.Ing &&
          (!isMultiRepo || item.value !== DocumentStatus.AIProcess),
      )

    return [
      {
        label: localize('all_status', '全部状态'),
        value: -1,
      },
      ...statusItems,
    ]
  }, [isMultiRepo])

  const checkSearchParamsSafe = (
    params: Omit<IDocumentListReq, 'partitionId'>,
  ) => {
    if (Number(params.page) < 0) {
      params.page = DEFAULT_PAGE_OPTIONS.page
    }
    if (
      params.page_size &&
      (Number(params.page_size < 0) || Number(params.page_size > 100))
    ) {
      params.page_size = DEFAULT_PAGE_OPTIONS.page_size
    }
    if (!isNil(params?.file_status) && Number(params?.file_status) !== -1) {
      const items = selectOptions.filter(item => {
        return item.value === Number(params.file_status)
      })
      if (!items.length) {
        params.file_status = DEFAULT_PAGE_OPTIONS.file_status
      }
      if (params.sort_by && params.sort_by !== 'update_time') {
        params.sort_by = ''
        params.sort_order = ''
      }
      if (
        params.sort_order &&
        params.sort_order !== 'asc' &&
        params.sort_order !== 'desc'
      ) {
        params.sort_order = ''
      }
    }
    if (params.sort_by && params.sort_by !== 'update_time') {
      params.sort_by = ''
      params.sort_order = ''
    }
    if (
      params.sort_order &&
      params.sort_order !== 'asc' &&
      params.sort_order !== 'desc'
    ) {
      params.sort_order = ''
    }
    return params
  }

  const [_pageOptions, setPageOptions] = useUrlState<
    Omit<IDocumentListReq, 'partitionId'>
  >(DEFAULT_PAGE_OPTIONS!, {
    navigateMode: 'replace',
  })

  const pageOptions = useMemo(() => {
    return checkSearchParamsSafe(_pageOptions)
  }, [_pageOptions])
  // 搜索
  const [searchInfo, setSearchInfo] = useState<{
    file_name: string
    file_status: number
    tags: string[]
  }>({
    file_name: pageOptions.file_name!,
    file_status: Number(pageOptions.file_status!),
    tags: (isString(pageOptions.tags!)
      ? [pageOptions.tags!]
      : pageOptions.tags)!,
  })

  const [failDocumentsCount, setFailDocumentsCount] = useState<number>(0)

  const getDatasetTasAndTransform = async (id: number) => {
    const res = await getDatasetTags(id)
    return res?.map(item => ({ label: item.tag, value: item.tag }))
  }

  const { runAsync: refreshDataSetTags } = useRequest(
    getDatasetTasAndTransform,
    {
      defaultParams: [props.id],
      onSuccess: res => {
        setTagOptions(res)
      },
    },
  )

  const { data: datasetList } = useRequest(getDataStoreList)

  const currentWorkspaceId = useWorkspaceStore(
    state => state.currentWorkspaceId,
  )

  const user = useUserStore(state => state.user)

  const { fetchVersionByWorkspaceId } = useVersionStore()

  const fileWatcher = useFileProcessWatcher()

  const [retryLoadingMap, setRetryLoadingMap] = useState<
    Record<number, boolean>
  >({})
  const { runAsync: retryFile } = useRequest(fileRetry, { manual: true })
  const [loading, setLoading] = useState<boolean>(false)

  const [multiModalList, setMultiModalList] = useState<DocumentItem[]>([])

  const { runAsync: fetchAnalyse } = useRequest(reProcessFile, { manual: true })

  const [multiModalTotal, setMultiModalTotal] = useState(0)

  const handleFailedFile = async (file_id: number) => {
    await fetchAnalyse({ file_id })
    setMultiModalList(preList =>
      preList?.map(item => {
        if (file_id === item.file_id) {
          return {
            ...item,
            status: DocumentStatus.Ing,
            progress: {
              current: 0,
              percentage: 0,
              total: 100,
            },
          }
        }
        return item
      }),
    )
    fileWatcher.add([file_id])
  }

  const {
    data: currentMultiModalData,
    loading: multiModalLoading,
    run: runMultiModalListApi,
  } = useRequest(params => getMultiModalList(params), {
    manual: true,
    onSuccess: res => {
      const data = res.rows ?? []
      setMultiModalTotal(res?.totals)
      setMultiModalList(prevList => {
        const map = new Map<string, DocumentItem>()
        prevList.forEach((item: DocumentItem) =>
          map.set(String(item.file_id), item),
        )
        data.forEach((item: DocumentItem) =>
          map.set(String(item.file_id), item),
        )
        return Array.from(map.values())
      })
      fileWatcher.add(
        data
          ?.filter(item =>
            [
              DocumentStatus.Wait,
              DocumentStatus.Ing,
              DocumentStatus.AIProcess,
            ]?.includes(item.status),
          )
          .map(item => item.file_id),
      )
      setFailDocumentsCount(res.failed_totals)
    },
    onFinally: () => {
      setLoading(false)
    },
  })

  const {
    run: fetchBatchProgressingFilesPoll,
    cancel: cancelBatchProgressingFilesPoll,
  } = useRequest(
    () =>
      getBatchMultiModalFileProgress({
        file_ids: fileWatcher?.fileProgressingList || [],
      }),
    {
      pollingInterval: 3000,
      pollingWhenHidden: false,
      manual: true,
      onSuccess: (res: ProgressResponse) => {
        // 更新已有文档状态
        setMultiModalList(prevList => {
          const newList = prevList?.map(item => {
            const fileProgress = res[item.file_id]

            if (!fileProgress) return item

            return {
              ...item,
              status: fileProgress.status,
              progress: fileProgress.progress,
              enable: fileProgress?.enable,
              update_user: user?.username,
              ...(fileProgress.data && { contents: fileProgress?.data }),
            }
          })
          if (searchInfo?.file_status === DocumentStatus.Fail) {
            return newList?.filter(
              item => item.status === searchInfo?.file_status,
            )
          }
          const processingFileIds = newList
            .filter(item =>
              [
                DocumentStatus.Wait,
                DocumentStatus.Ing,
                DocumentStatus.AIProcess,
              ].includes(item.status),
            )
            .map(item => item?.file_id)

          fileWatcher.clean()
          if (processingFileIds.length > 0) {
            fileWatcher.add(processingFileIds)
          }
          return newList
        })
      },
    },
  )

  const fetchMultiModalList = async (
    params?: Omit<IDocumentListReq, 'partitionId'>,
  ) => {
    const newParams = {
      page_size: params?.page_size,
      tags: params?.tags,
      ...(params?.file_name && { file_name: params?.file_name }),
      ...(params?.file_status !== -1 && { file_status: params?.file_status }),
      ...(params?.cursor && { cursor: params?.cursor }),
    }
    setLoading(true)
    await runMultiModalListApi({
      ...newParams,
      partitionId: id,
      pagination: true,
      page_size: 20,
    })
  }

  const fetchOptionMultiModalList = async (
    params?: Omit<IDocumentListReq, 'partitionId'>,
  ) => {
    setMultiModalList([])
    fetchMultiModalList(params)
  }

  const {
    data: getDocumentsData,
    run: runDocumentsApi,
    refreshAsync: refreshDocumentsApi,
    cancel: cancelPolling,
  } = useRequest(params => getDocumentList(params, true), {
    pollingInterval: 3000,
    manual: true,
    onSuccess: res => {
      const data = res.rows
      fileWatcher.add(
        data
          .filter(
            item =>
              item.status === DocumentStatus.Wait ||
              item.status === DocumentStatus.Ing ||
              item.status === DocumentStatus.AIProcess,
          )
          .map(item => item.file_id),
      )

      const failFileIds = data
        .filter(item => item.status === DocumentStatus.Fail)
        .map(item => item.file_id)
      setFailDocumentsCount(res.failed_totals)

      fileWatcher.process(failFileIds, fileIds => {
        fileIds.forEach(id => {
          const [item] = data.filter(item => item.file_id === id)
          if (item) {
            message.error(item.failed_reason)
          }
        })
      })
    },
    onFinally: () => {
      setLoading(false)
    },
  })
  const [documents, setDocuments] = useState<DocumentItem[]>([])

  const fetchDocumentsList = async (
    params?: Omit<IDocumentListReq, 'partitionId'>,
  ) => {
    fileWatcher.clean()
    setLoading(true)
    const baseReq = checkSearchParamsSafe({
      ...pageOptions,
      page: 1,
      ...params,
    })
    await runDocumentsApi({
      partitionId: id,
      ...baseReq,
      tags: isString(baseReq.tags) ? [baseReq.tags] : baseReq.tags,
      file_status:
        Number(baseReq.file_status) === -1 ? undefined : baseReq.file_status,
    })
    setPageOptions(baseReq)
  }

  useMount(() => {
    if (datastoreType === PartitionCategoryType.Multimodal) {
      fetchMultiModalList()
    } else {
      fetchDocumentsList(pageOptions)
    }
  })

  const addProgressFile = (fileIds: number[]) => {
    setMultiModalList(preList =>
      preList?.map(item => {
        if (fileIds?.includes(item.file_id)) {
          return {
            ...item,
            status: DocumentStatus.Ing,
            progress: {
              current: 0,
              percentage: 0,
              total: 100,
            },
          }
        }
        return item
      }),
    )
    fileWatcher.add(fileIds)
  }

  const [tagEditorModal] = useModal(SearchableTagEditorModal)

  const currentDocumentsInfo = useMemo(() => {
    return find(datasetList?.partitions!, o => {
      return o.partition_id === id
    })
  }, [datasetList, id])

  const pageModal = usePageModal()

  const toDocumentDetail = (file_id: number, openModal = false) => {
    if (!openModal && onShowDetail && onShowDetail(file_id)) {
      return
    }

    const path = getDatastorePath.documentDetail(
      currentWorkspaceId,
      String(id),
      String(file_id),
      'content',
    )

    const url = `${path}${
      openDocumentInNewWindow ? '?independent=1' : location.search
    }`

    if (openDocumentInNewWindow) {
      pageModal.show({ url })
    } else {
      navigate(url)
    }
  }

  const handleEditTag = (row: DocumentItem) => {
    tagEditorModal.open({
      applicationType: 'DATASET',
      title: row.tags?.length
        ? localize('edit_tags', '编辑标签')
        : localize('add_tags', '添加标签'),
      defaultValue: row.tags?.map(item => ({
        label: item,
        color: theme.colors.primary,
        textColor: '#fff',
        id: item,
      })),
      documentId: row.file_id,
      datasetId: id,
      onFinish: async value => {
        datastoreType === PartitionCategoryType.Multimodal
          ? setMultiModalList(list =>
              list?.map(item => ({
                ...item,
                ...(item?.file_id === row.file_id && {
                  tags: value?.map(item => item.label),
                }),
              })),
            )
          : await refreshDocumentsApi()
        refreshDataSetTags(props.id)
      },
    })
  }

  const { run: onFileRetry } = useDebounceFn(
    async (file_id: number, type: 'fail' | 'ai_process' = 'fail') => {
      fileWatcher.add([file_id])
      if (!retryLoadingMap[file_id]) {
        setRetryLoadingMap({
          ...retryLoadingMap,
          [file_id]: true,
        })
        if (type === 'fail') {
          await retryFile(file_id).finally(() => {
            setRetryLoadingMap({
              ...retryLoadingMap,
              [file_id]: false,
            })
          })
        }
        if (type === 'ai_process') {
          await generateAISummaryByChunkId?.({
            file_id,
          }).finally(() => {
            setRetryLoadingMap({
              ...retryLoadingMap,
              [file_id]: false,
            })
          })
        }

        refreshDocumentsApi()
      }
    },
    {
      leading: true,
      trailing: false,
      wait: 500,
    },
  )

  const checkHasIngDocument = (documents: any[]) => {
    return documents.some(
      item =>
        item.status === DocumentStatus.Ing ||
        item.status === DocumentStatus.Wait ||
        item.status === DocumentStatus.AIProcess,
    )
  }

  const refreshDocuments = async () => {
    fetchDocumentsList({ page: 1 })
  }

  const { run: toggleDocumentEnableStatus } = useRequest(updateDocument, {
    manual: true,
    debounceWait: 100,
    onSuccess: (_, [params]) => {
      if (datastoreType === PartitionCategoryType.Multimodal) {
        setMultiModalList(prevDocuments =>
          prevDocuments.map(doc =>
            doc.file_id === params.file_id
              ? { ...doc, enable: params.enable! }
              : doc,
          ),
        )
      } else {
        setDocuments(prevDocuments =>
          prevDocuments.map(doc =>
            doc.file_id === params.file_id
              ? { ...doc, enable: params.enable! }
              : doc,
          ),
        )
      }
      message.success(localize('update_success', '更新成功'))
    },
    onError: () => {
      message.success(localize('update_failed', '更新失败，请联系开发人员'))
    },
  })

  const renderPopContent = (record: DocumentItem) => {
    switch (record.status) {
      case DocumentStatus.Fail:
        return {
          title: (
            <div className='c-#17171D text-12px/16px flex items-center'>
              <p className='text-16px/16px mr-6px'>
                <IconFont name='cuowurizhi'></IconFont>
              </p>
              <span>
                {localize('document_processing_failed', '文档处理失败')}
              </span>
            </div>
          ),
          content: (
            <div className='ml-22px w-250px flex flex-col'>
              <div className='flex  flex-col line-height-18px'>
                <span className='mr-8px text-#17171d text-12px/18px'>
                  {record.failed_reason}
                </span>
                <div className='mt-8px flex justify-end'>
                  <Button
                    className='mr-8px !px-8px !py-0px !h-24px !text-12px/12px'
                    size='small'
                    onClick={async () => {
                      await copy(record.file_name)
                      message.success(localize('copy_success', '已复制'))
                    }}
                  >
                    {localize('copy_file_name', '复制文件名称')}
                  </Button>
                </div>
              </div>
            </div>
          ),
        }
      case DocumentStatus.Warning:
        return {
          title: (
            <div className='c-#17171D text-12px/16px flex items-center'>
              <p className='text-16px/16px mr-6px'>
                <IconFont name='jingzhi'></IconFont>
              </p>
              <span>{localize('paragraph_abnormal', '段落异常')}</span>
            </div>
          ),
          content: (
            <div className='flex items-center line-height-18px'>
              <span className='mr-8px text-#17171d text-12px'>
                {record.failed_reason}
              </span>
            </div>
          ),
        }
      case DocumentStatus.AIProcess:
        return {
          title: null,
          content: (
            <div className='flex items-center line-height-18px'>
              <span className='mr-8px text-#17171d text-12px'>
                {localize('paragraph_ai_processing', '段落AI处理中')}
                {!!record?.extra_info?.process_content_count && (
                  <>
                    , {localize('processing', '正在进行')}
                    <span className='mx-4px c-#7B61FF'>
                      {record?.extra_info?.process_content_count}
                    </span>
                    {localize('paragraph', '段落')}
                  </>
                )}
              </span>
            </div>
          ),
        }

      default:
        return {
          content: null,
          title: null,
        }
    }
  }

  const columnMap: Record<Columns, any> = {
    file_name: {
      key: 'file_name',
      dataIndex: 'file_name',
      title:
        datastoreType === PartitionCategoryType.QA
          ? localize('qa', '问答')
          : localize('document', '文档'),
      width: widthMap?.file_name ?? 480,
      render: (text: string, record: any) => (
        <Track event='datastore_doc_open'>
          <LinkText
            title={text}
            className='hover:text-font flex items-center cursor-pointer file_name'
          >
            <div
              className='flex items-center truncate'
              onClick={event => {
                event.stopPropagation()
                toDocumentDetail(record.file_id)
              }}
            >
              <img
                className='w-20px h-24px shrink-0'
                src={getFileIconByFileTypeOrMimeType(
                  record.mimetype,
                  record.file_type,
                )}
                alt=''
              />
              <span className='ml-8 mt-2 line-height-18px text truncate'>
                {getFileDisplayName(text, record.file_type)}
              </span>
              {record.file_type === DocumentType.FEISHU && (
                <img
                  className='size-16px ml-4px shrink-0'
                  src={feishuTip}
                  alt=''
                />
              )}
            </div>
            <span
              className='link op-0 shrink-0 text-16px ml-8 relative cursor-pointer c-font_1 c-op-60 flex-center'
              onClick={event => {
                event.stopPropagation()
                toDocumentDetail(record.file_id, true)
              }}
            >
              <IconFont name='super-link' />
            </span>
          </LinkText>
        </Track>
      ),
    },
    tags: {
      key: 'tags',
      dataIndex: 'tags',
      width: widthMap?.tags ?? 310,
      title: localize('tags', '标签'),
      render: (tags: string[], record: DocumentItem) => (
        <div
          className='flex items-center gap-8 w-100%'
          onClick={e => e.stopPropagation()}
        >
          <div className='flex flex-1 items-center overflow-hidden'>
            {tags?.length < DATASTORE_MAX_TAG_COUNT && (
              <Tag
                className={classNames(
                  'rounded-4px b-dashed bg-white',
                  readwrite
                    ? 'hover:bg-bg_3 hover:bg-op-8 cursor-pointer'
                    : 'opacity-40 pointer-events-none',
                )}
                onClick={() => handleEditTag(record)}
              >
                {localize('add_tags', '添加标签')}
              </Tag>
            )}
            <DocumentTagList
              className={classNames(
                'h-22px! !text-12px',
                readwrite ? '' : 'opacity-40 pointer-events-none',
              )}
              list={clone(tags)?.reverse() || []}
              // getPopupContainer={() => scrollRef.current!}
              showMaxCount={tags.length < DATASTORE_MAX_TAG_COUNT ? 2 : 3}
              onClick={() => {
                handleEditTag(record)
              }}
            />
          </div>
        </div>
      ),
    },
    file_size: {
      title: '大小',
      key: 'file_size',
      dataIndex: 'file_size',
      align: 'left',
      width: widthMap?.file_size ?? 100,
      render: (file_size: number) =>
        file_size ? formatFileSize(file_size) : '--',
    },
    segment_count: {
      title:
        datastoreType === PartitionCategoryType.QA
          ? localize('qa_count', '问答数')
          : localize('paragraph_count', '段落数'),
      key: 'segment_count',
      dataIndex: 'segment_count',
      align: 'left',
      width: widthMap?.segment_count ?? 120,
      render: (segment_count: number) => segment_count ?? '--',
    },
    extracted_text_length: {
      title: localize('character_count', '字符数'),
      key: 'extracted_text_length',
      dataIndex: 'extracted_text_length',
      align: 'left',
      width: widthMap?.extracted_text_length ?? 120,
      render: (extractedTextLength: number) =>
        extractedTextLength ? formatSize(extractedTextLength) : '--',
    },
    update_user: {
      title: '更新人',
      key: 'update_user',
      dataIndex: 'update_user',
      align: 'left',
      width: widthMap?.update_user ?? 150,
      render: (updateUser: string) => updateUser || '--',
    },
    update_time: {
      title: localize('update_time', '更新时间'),
      width: widthMap?.update_time ?? 160,
      key: 'update_time',
      dataIndex: 'update_time',
      sortIcon: ({ sortOrder }: { sortOrder: 'asc' | 'desc' }) => {
        return <SortIcon sortOrder={sortOrder} />
      },
      sorter: true,
      sortDirections: ['asc', 'desc'],
      render: (updateTime: string) =>
        dayjs(updateTime).format(dateFormat ?? 'YYYY-MM-DD HH:mm'),
    },
    status: {
      fixed: 'right',
      width: widthMap?.status ?? 120,
      title: localize('status', '状态'),
      key: 'status',
      dataIndex: 'status',
      className: '!right-80px',
      render: (status: DocumentStatus, record: DocumentItem) => {
        const { content, title } = renderPopContent(record)
        return (
          <div
            className='flex items-center gap-4'
            onClick={e => e.stopPropagation()}
          >
            <StatusTag
              status={status}
              progress={record.progress?.percentage}
              className='rounded-4px font-500 text-12px min-w-52px text-center !m-0 cursor-pointer'
              popTitle={title || null}
              popContent={content || null}
            />
            {retryFileId === record.file_id &&
              status === DocumentStatus.Fail && (
                <div className='ml-4px w-24px h-24px cursor-pointer hover:bg-[rgba(98,105,153,0.08)] flex items-center justify-center rounded-4px'>
                  <IconFont
                    name='tongshi'
                    className='shrink-0 text-16px text-#8d8d99'
                    onClick={() => {
                      if (status === DocumentStatus.Fail) {
                        onFileRetry(record.file_id)
                      }
                    }}
                  />
                </div>
              )}
            {status === DocumentStatus.Warning && (
              <div
                className='ml-4px w-24px h-24px cursor-pointer text-16px/16px flex items-center justify-center hover:bg-[rgba(98,105,153,0.08)] rounded-4px'
                onClick={async () => {
                  onFileRetry(record.file_id, 'ai_process')
                }}
              >
                <Tooltip
                  title={localize(
                    'ai_process_all_abnormal_paragraphs',
                    '一键AI处理所有异常段落',
                  )}
                >
                  <IconFont name='AIcengjiangchuli' />
                </Tooltip>
              </div>
            )}
          </div>
        )
      },
    },
    enable: {
      fixed: 'right',
      width: widthMap?.enable ?? 80,
      title: localize('enable', '启用'),
      key: 'enable',
      dataIndex: 'enable',
      className: '!right-0px',
      render: (enable: boolean, record: DocumentItem) => (
        <div onClick={e => e.stopPropagation()}>
          <Switch
            size='small'
            checked={enable}
            disabled={!readwrite}
            onClick={async () => {
              toggleDocumentEnableStatus({
                file_id: record.file_id,
                enable: !enable,
              })
            }}
          />
        </div>
      ),
    },
  }

  const columns: ColumnsType<object> = fields
    ? fields.map(field => columnMap[field])
    : Object.values(columnMap)

  const total = useMemo(() => {
    const totals =
      datastoreType === PartitionCategoryType.Multimodal
        ? multiModalTotal
        : getDocumentsData?.totals
    return totals ?? 0
  }, [getDocumentsData, multiModalTotal, selectedRowKeys])

  const handleCreate = (afterCreate?: VoidFunction) => {
    createDataStoreDocumentsModal.open({
      title:
        datastoreType === PartitionCategoryType.QA
          ? localize('batch_import_qa', '批量导入问答')
          : localize('add_document', '添加文档'),
      onFinish: async (value, documentType) => {
        if (value) {
          const data = await onDocumentCreateApi(
            {
              ...value,
              partition: {
                partition_id: Number(id),
              },
            },
            documentType,
          )
          fileWatcher.add(data.file_ids)
          if (datastoreType === PartitionCategoryType.Multimodal) {
            setMultiModalTotal(multiModalTotal + data.file_ids?.length)
            const addData = data.file_ids?.map(item => ({
              file_id: item,
              file_name: '',
              status: DocumentStatus.Ing,
              tags: [],
            }))
            setMultiModalList(pre => [...addData, ...pre] as any)
          } else {
            refreshDocuments()
          }
          createDataStoreDocumentsModal.close()
          afterCreate?.()
        }
      },
    })
  }

  useImperativeHandle(
    ref,
    () => ({
      create: handleCreate,
      onSearch: (params: Record<string, any>) => {
        datastoreType === PartitionCategoryType.Multimodal
          ? fetchMultiModalList(params)
          : fetchDocumentsList(params)
      },
    }),
    [handleCreate],
  )

  useRequest(getDatasetTags, {
    defaultParams: [Number(id)],
  })

  const onSearchInfoChange = useCallback(
    (params: any) => {
      setSearchInfo(() => {
        // fetchTableData
        const opt = { ...pageOptions, ...searchInfo, ...params }
        datastoreType === PartitionCategoryType.Multimodal
          ? fetchOptionMultiModalList(opt)
          : fetchDocumentsList(opt)
        return { ...searchInfo, ...params }
      })
    },
    [searchInfo, pageOptions, datastoreType],
  )
  const handleTableChange: OnTableChange = (_pagination, _filters, sorter) => {
    // setFilteredInfo(filters)
    const sortInfo = sorter as Sorts
    fetchDocumentsList({
      sort_by: sortInfo?.column?.key || ('' as any),
      sort_order: sortInfo.order || '',
    })
  }

  const loadMore = async () => {
    const { file_status, tags, file_name } = searchInfo
    await fetchMultiModalList({
      ...(file_status !== -1 && { file_status }),
      tags,
      file_name,
      cursor: currentMultiModalData?.next_cursor,
    })
  }

  useDebounceEffect(
    () => {
      if (!isMultiModalRepo || !fileWatcher?.fileProgressingList?.length) {
        cancelBatchProgressingFilesPoll()
        return
      }

      cancelBatchProgressingFilesPoll()
      fetchBatchProgressingFilesPoll()
    },
    [isMultiModalRepo, fileWatcher?.fileProgressingList?.length],
    {
      wait: 1000,
      maxWait: 5000,
    },
  )

  useEffect(() => {
    const documents = getDocumentsData?.rows || []
    const result = documents
    setDocuments(result)
  }, [getDocumentsData, searchInfo, tagOptions])

  useUpdateEffect(() => {
    // 知识库恢复/复制成功后，刷新文档列表
    if (props.datastoreStatus === ExpiredStatus.Activated) {
      refreshDocuments()
    }
  }, [props.datastoreStatus])

  const onRowSelectedChange = (selectedRowKeys: Key[]) => {
    setSelectedRowKeys(selectedRowKeys as number[])
  }

  const [copyDocumentsModal] = useModal(CopyDocumentsModal, {
    datasetList: datasetList?.partitions.filter(
      item =>
        item.partition_id !== id &&
        currentDocumentsInfo?.partition_model === item.partition_model &&
        currentDocumentsInfo?.partition_category === item.partition_category,
    ),
    onFinish: async data => {
      try {
        await copyDocumentsToDataset({
          file_ids: selectedRowKeys,
          target_partition_id: data.target_partition_id,
        })
        message.success(localize('copy_success', '复制成功'))
      } catch (e) {
        message.error(localize('copy_failed', '复制失败'))
      } finally {
        copyDocumentsModal.close()
      }
    },
  })

  const { runAsync: deleteDocumentsApi, loading: deleteLoading } = useRequest(
    deleteDocuments,
    { manual: true },
  )

  const { runAsync: exportQADocumentsApi, loading: exportQADocumentsLoading } =
    useRequest(exportQADocuments, {
      manual: true,
      onSuccess: async res => {
        const blob = await res.blob()
        download(blob, '问答知识库的文件.zip', 'application/zip')
      },
    })

  const {
    runAsync: batchDeleteFailedDocumentsByPartitionId,
    loading: batchDeleteLoading,
  } = useRequest(deleteFailedDocumentsByPartitionId, { manual: true })

  const [deleteDocumentsModal] = useModal(ConfirmModal, {
    okButtonProps: {
      loading: deleteLoading || batchDeleteLoading,
    },
    okText: localize('delete', '删除'),
  })

  const [exportDocumentsModal] = useModal(ConfirmModal, {
    okButtonProps: {
      danger: false,
      loading: exportQADocumentsLoading,
    },
    okText: '导出',
  })

  useEffect(() => {
    if (getDocumentsData) {
      // 判断是否有正在处理的文档
      const hasIngDocument = checkHasIngDocument(getDocumentsData.rows)
      if (!hasIngDocument) {
        cancelPolling()
      }
    }
  }, [getDocumentsData])

  const onDeleteLockFn = useLockFn(async () => {
    await deleteDocumentsApi(selectedRowKeys)
    if (datastoreType === PartitionCategoryType.Multimodal) {
      const keysSet = new Set(selectedRowKeys || [])
      setMultiModalTotal(multiModalTotal - selectedRowKeys?.length)
      setMultiModalList(preList => {
        return preList?.filter(item => !keysSet?.has(item?.file_id))
      })
    } else {
      refreshDocuments()
    }
    setSelectedRowKeys([])
    deleteDocumentsModal.close()
    fetchVersionByWorkspaceId(currentWorkspaceId)
  })

  const onDelete = () => {
    deleteDocumentsModal.open({
      children: localize(
        'confirm_delete_selected_data',
        '请确认是否删除所选数据？',
      ),
      onOk: onDeleteLockFn,
    })
  }

  const onExportLockFn = useLockFn(async () => {
    exportQADocumentsApi(selectedRowKeys).then(() => {
      setSelectedRowKeys([])
      exportDocumentsModal.close()
      fetchVersionByWorkspaceId(currentWorkspaceId)
    })
  })

  const onExport = () => {
    exportDocumentsModal.open({
      children: `是否导出已选择的${selectedRowKeys.length}个问答？`,
      onOk: onExportLockFn,
    })
  }

  const cleanAllFailDocuments = () => {
    if (!failDocumentsCount) {
      return
    }
    deleteDocumentsModal.open({
      children: localize(
        'confirm_delete_all_failed_documents',
        '请确认是否删除所有失败的文档？',
      ),
      onOk: async () => {
        try {
          await batchDeleteFailedDocumentsByPartitionId(id)
          message.success(
            localize('failed_documents_cleared', '失败文档已清空'),
          )
          datastoreType === PartitionCategoryType.Multimodal
            ? await fetchMultiModalList()
            : refreshDocuments()
          deleteDocumentsModal.close()
          fetchVersionByWorkspaceId(currentWorkspaceId)
        } catch (_e) {
          message.error(
            localize('failed_documents_clear_failed', '失败文档清空失败'),
          )
        }
      },
    })
  }

  const { run: downloadFailReasonFileFn } = useDebounceFn(
    downloadFailReasonFile,
    {
      wait: 500,
    },
  )

  const rowSelection = {
    type: 'checkbox' as const,
    onChange: onRowSelectedChange,
    selectedRowKeys,
    getCheckboxProps: record => {
      const disabled =
        (outletProps && !outletProps.readwrite) ||
        isReadonly(location.state?.permission)
      return {
        disabled:
          disabled ||
          record.status === DocumentStatus.Ing ||
          record.status === DocumentStatus.Wait ||
          record.status === DocumentStatus.AIProcess,
      }
    },
  }
  const renderTable = () => {
    return (
      <div className='flex-1 overflow-auto'>
        <StyledTable
          isEmpty={documents.length === 0}
          rowKey='file_id'
          sticky={{
            getContainer: () => wrapperRef.current!,
          }}
          rowSelection={rowSelection}
          scroll={{ x: 'none', y: 'auto' }}
          columns={
            datastoreType === PartitionCategoryType.QA
              ? columns.filter(e => e.key !== 'extracted_text_length')
              : columns
          }
          dataSource={documents}
          loading={loading}
          pagination={false}
          onChange={handleTableChange}
          onRow={(record: DocumentItem) => ({
            onMouseEnter: () => {
              setRetryFileId(record.file_id)
            },
            onMouseLeave: () => {
              setRetryFileId(-1)
            },
            onClick: () => {
              onRowClick?.(record.file_id)
            },
          })}
        />
      </div>
    )
  }

  const contentMap: Record<PartitionCategoryType, JSX.Element> = {
    [PartitionCategoryType.Multimodal]: (
      <MultimodalDatastore
        multiModalList={multiModalList}
        currentMultiModalData={currentMultiModalData}
        multiModalLoading={multiModalLoading}
        selectedRowKeys={selectedRowKeys}
        handleFailedFile={handleFailedFile}
        loadMore={loadMore}
        toggleDocumentEnableStatus={toggleDocumentEnableStatus}
        handleEditTag={handleEditTag}
        setSelectedRowKeys={setSelectedRowKeys}
        addProgressFile={addProgressFile}
        fetchMultiModalList={fetchMultiModalList}
      />
    ),
    [PartitionCategoryType.Document]: renderTable(),
    [PartitionCategoryType.QA]: renderTable(),
  }

  if (
    props.datastoreStatus !== ExpiredStatus.Activated &&
    handleRecoverDataStore
  ) {
    return (
      <DocumentsExpired
        readwrite={readwrite}
        status={props.datastoreStatus}
        handleRecover={async () => {
          handleRecoverDataStore()
        }}
      />
    )
  }

  return (
    <div
      className={classNames(
        'flex flex-col flex-1 h-full w-full',
        props.className,
      )}
    >
      {!hideHeader && (
        <div className='flex flex-items-center flex-justify-between'>
          <DocumentsSearch
            showExtraSearch={showExtraSearch}
            selectOptions={selectOptions}
            tagOptions={tagOptions}
            onSearch={params => {
              setSelectedRowKeys([])
              datastoreType === PartitionCategoryType.Multimodal
                ? fetchOptionMultiModalList(params)
                : fetchDocumentsList(params)
            }}
            onSearchInfoChange={onSearchInfoChange}
            searchInfo={searchInfo}
            setSearchInfo={setSearchInfo}
            placeholder={
              datastoreType === PartitionCategoryType.Multimodal
                ? localize('search_multi_repo_name', '按名称搜索')
                : datastoreType === PartitionCategoryType.QA
                  ? localize('search_qa_name', '搜索问答名称')
                  : localize('search_document_name', '搜索文档名称')
            }
          />
          {readwrite && (
            <div className='relative'>
              <LimitedAccess limitedType='dataStore'>
                <Button
                  type={
                    datastoreType === PartitionCategoryType.QA
                      ? 'default'
                      : 'primary'
                  }
                  icon={<IconFont name='add' />}
                  onClick={() => handleCreate()}
                >
                  <span>
                    {datastoreType === PartitionCategoryType.QA
                      ? localize('import_qa', '导入问答')
                      : localize('add_document', '添加文档')}
                  </span>
                </Button>
              </LimitedAccess>
            </div>
          )}
        </div>
      )}
      <div
        className={classNames(
          'relative flex-1 flex flex-col overflow-hidden ',
          {
            'rounded-8px': showExtraSearch,
            'bg-white adapt:mt-24 ':
              datastoreType !== PartitionCategoryType.Multimodal,
            'mt-4px': datastoreType === PartitionCategoryType.Multimodal,
          },
        )}
      >
        {contentMap[datastoreType]}
        <div className='flex items-center justify-between w-full h-[64px] px-18 b-t-1 b-solid b-[rgba(225,225,229,0.4)] py-[16px]'>
          <div className='flex items-center justify-between'>
            {selectedRowKeys.length > 0 ? (
              <div className='flex items-center'>
                <div>
                  <span>{localize('selected', '已选')}&nbsp;</span>
                  <span className='text-16px text-font font-bold relative top-1px'>
                    {selectedRowKeys.length}
                  </span>
                  <span className='mx-3'>/</span>
                  <span>
                    {total}&nbsp;{localize('data', '数据')}
                  </span>
                </div>
                <Button
                  size='small'
                  className='ml-16px font-500 important:h-32px flex items-center important:px-12px'
                  onClick={() => {
                    copyDocumentsModal.open()
                  }}
                >
                  <span className='text-14px'>
                    <IconFont name='copy' className='mr-4px text-16px' />
                    {localize('copy_to', '复制')}
                  </span>
                </Button>
                {datastoreType === PartitionCategoryType.QA && (
                  <Button
                    size='small'
                    className='ml-15 important:h-32px important:px-12px font-500 flex items-center'
                    onClick={onExport}
                  >
                    <span className='text-14px'>
                      <IconFont name='daochu-1' className='mr-4px text-16px' />
                      导出
                    </span>
                  </Button>
                )}
                <Button
                  size='small'
                  className='ml-15 important:h-32px important:px-12px font-500 flex items-center'
                  danger
                  type='primary'
                  onClick={onDelete}
                >
                  <span className='text-14px'>
                    <IconFont name='shanshu' className='mr-4px text-16px' />
                    {localize('delete', '删除')}
                  </span>
                </Button>
                {datastoreType === PartitionCategoryType.Multimodal && (
                  <>
                    <div className='w-1px h-20px mx-12px bg-#E1E1E5'></div>
                    <Button
                      size='small'
                      className='important:h-32px important:px-12px font-500 flex items-center'
                      onClick={() => {
                        setSelectedRowKeys([])
                      }}
                    >
                      <span className='text-14px'>
                        <IconFont
                          name='tuichubianji'
                          className='mr-4px text-16px'
                        />
                        {localize('delete', '退出')}
                      </span>
                    </Button>
                  </>
                )}
              </div>
            ) : (
              <div className='text-font_1 font-400 flex items-center'>
                <span>
                  {localize('total', '共计')}&nbsp;{total}&nbsp;
                  {localize('document', '文档')}
                </span>
                {!!failDocumentsCount && (
                  <>
                    <Divider className='mx-12px' type='vertical' />
                    <span>
                      {localize('failed', '失败')}{' '}
                      <span className='c-#FF5219'>{failDocumentsCount}</span>{' '}
                      {localize('document', '文档')}
                    </span>
                  </>
                )}
                {!!failDocumentsCount && (
                  <div className='flex items-center ml-12px'>
                    <Tooltip
                      title={localize('export_failed_record', '导出失败记录')}
                    >
                      <div
                        className={classNames(
                          'rounded-4px text-16px/16px w-24px h-24px flex-center c-[rgba(164,165,191)] hover:bg-[rgba(98,105,153,0.08)] cursor-pointer',
                          {
                            '!cursor-not-allowed !op-40': !failDocumentsCount,
                          },
                        )}
                        onClick={() => {
                          if (!failDocumentsCount) {
                            return
                          }
                          downloadFailReasonFileFn(
                            id,
                            datastoreName
                              ? `${dayjs().format(
                                  'YYYY年MM月DD HH时mm分',
                                )}-${datastoreName}-${localize('failed_record', '失败记录')}`
                              : localize('failed_record', '失败记录'),
                          )
                        }}
                      >
                        <IconFont name='daochu-1' />
                      </div>
                    </Tooltip>

                    <Tooltip
                      title={localize('clear_failed_document', '清空失败文档')}
                    >
                      <div
                        className={classNames(
                          'rounded-4px w-24px h-24px hover:bg-error hover:bg-op-8 cursor-pointer  c-[rgba(164,165,191)]  flex-center text-16px/16px ml-8px',
                          {
                            '!cursor-not-allowed !op-40 ': !failDocumentsCount,
                            'hover:c-error': failDocumentsCount,
                          },
                        )}
                        onClick={cleanAllFailDocuments}
                      >
                        <IconFont name='shanshu2' />
                      </div>
                    </Tooltip>
                  </div>
                )}
              </div>
            )}
          </div>
          <div>
            {[
              PartitionCategoryType.QA,
              PartitionCategoryType.Document,
            ].includes(datastoreType) && (
              <StyledPagination
                current={pageOptions.page}
                pageSize={pageOptions.page_size}
                onChange={async (page, pageSize) => {
                  const options = {
                    ...pageOptions,
                    page,
                    page_size: pageSize,
                  }
                  await fetchDocumentsList(options)
                  setSelectedRowKeys([])
                }}
                total={total}
                showSizeChanger
                showQuickJumper
              />
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default React.forwardRef(Documents)
