import { localize } from '@bty/localize'
import type { FC } from 'react'
import { useState, useMemo, useEffect } from 'react'
import { Form, message } from 'antd'
import { sample } from 'lodash-es'
import { v4 as uuidv4 } from 'uuid'
import type { ButtonProps } from 'antd/es/button/button'
import type { UploadFileStatus } from 'antd/es/upload/interface'
import type {
  CreateTextDocumentRequest,
  CreateFileDocumentRequest,
  CreatePartitionRequest,
  IEmbeddingModalItem,
} from '@apis/datastore/type'
import { PartitionCategoryType } from '@apis/datastore/type'
import {
  DocumentParseType,
  DocumentType,
  DocumentSplitType,
  WebpageParseType,
} from '@apis/datastore/model'
import { ErrorBoundaryUnknown } from '@bty/monitor'
import { Button, Modal } from '@/components'
import { colors } from '@/constants/theme'
import { fontIcons } from '@/constants/icon.ts'
import { Track } from '../../track/Track.tsx'
import type { IStepItem } from '../components/Step.tsx'
import { Step } from '../components/Step.tsx'
import {
  UploadFileForm,
  UploadQAFileForm,
  MAX_FILE_COUNT,
  UploadMultiModalForm,
} from '../components/UploadFileForm.tsx'
import { DocumentTypeSelect } from '../components/DocumentTypeSelect.tsx'
import { MulmodalTypeSelect } from '../components/MultimodalTypeSelect.tsx'
import { unescapeUnrecognizedUrls } from '../utils/index.ts'

export interface CreateDataStoreDocumentsModalProps {
  title: string
  open?: boolean
  onCancel?: () => void
  uploadFileHashPath?: string
  okButtonProps?: ButtonProps
  datastoreType?: PartitionCategoryType
  onFinish?: (
    data: CreateTextDocumentRequest | CreateFileDocumentRequest | null,
    documentType: DocumentType,
  ) => void
  hasDataset?: boolean
  embeddingModelList?: IEmbeddingModalItem[]
  partitionModelMaxLength?: number
}

export const CreateDataStoreDocumentsModal: FC<
  CreateDataStoreDocumentsModalProps
> = props => {
  const {
    open,
    onCancel,
    onFinish,
    okButtonProps,
    title,
    uploadFileHashPath,
    hasDataset = true,
    embeddingModelList = [],
    datastoreType = PartitionCategoryType.Document,
    partitionModelMaxLength,
  } = props
  const [step, setStep] = useState<number>(1)

  const [form] = Form.useForm()
  const [dataSourceFormValues] = useState<CreatePartitionRequest>()
  const [documentType, setDocumentType] = useState<DocumentType>()

  const [uploadStatus, setUploadStatus] = useState<UploadFileStatus>('done')
  const fileList = Form.useWatch('fileList', form)

  const handleResetModal = () => {
    onCancel?.()
    form.resetFields()
    setStep(1)
  }

  const hashPath = useMemo(
    () => uploadFileHashPath ?? uuidv4(),
    [open, uploadFileHashPath],
  )

  // 当前是qa知识库时设置documentType为QA
  useEffect(() => {
    if (datastoreType === PartitionCategoryType.QA) {
      setDocumentType(DocumentType.QA)
    }
  }, [documentType])

  const handleFinish = async () => {
    if (!documentType) return
    const partition = {
      ...(documentType !== DocumentType.WEBPAGE && { oss_path: hashPath }),
      partition_name: dataSourceFormValues?.partition_name ?? '',
      partition_describe: dataSourceFormValues?.partition_describe ?? '',
      partition_icon: sample(fontIcons)!,
      partition_icon_color: sample(colors)!,
      partition_category: datastoreType ?? PartitionCategoryType.Document,
      partition_model: dataSourceFormValues?.partition_model,
    }

    form.validateFields().then(async values => {
      if (
        documentType === DocumentType.FILE &&
        values?.fileList?.some((item: any) => item.file_status === 'error')
      ) {
        message.error(
          localize('file_upload_failed', '有文件上传失败请删除后重新上传'),
        )
        return
      }

      let files = getFilesBasedOnDocumentType(documentType, values)
      if (values.parseType) {
        files = files.map(item => ({
          ...item,
          parsing_config: {
            parsing_method: values.parseType,
          },
          ...(values?.rule_number === DocumentSplitType.CUSTOM && {
            split_config: {
              rule_number: values?.rule_number,
              max_length: Number(values?.max_length),
              split_pattern: unescapeUnrecognizedUrls(values?.split_pattern),
            },
          }),
        }))
      }
      onFinish?.({ partition, files }, documentType)
    })
  }

  function getFilesBasedOnDocumentType(
    documentType: DocumentType,
    values: any,
  ) {
    switch (documentType) {
      case DocumentType.TEXT:
        return [
          {
            file_name: values.textTitle || values.textContent.slice(0, 6),
            file_content: values.textContent,
            mimetype: 'text/plain',
            file_type: DocumentType.TEXT,
            enable: true,
          },
        ]
      case DocumentType.WEBPAGE:
        return Object.values(values.webUrl || {}).map((value: any) => ({
          file_url: value,
          file_name:
            value.length > 50 ? `${value.slice(0, 47)}...` : value.slice(0, 50),
          file_type: DocumentType.WEBPAGE,
          parse_type: values.webParseType ?? WebpageParseType.NORMAL,
        }))
      case DocumentType.FEISHU:
        return values.feiShuConfig.list.map(each => {
          return {
            document_id: each.id,
            document_type: each.type,
            document_title: each.title,
            node_token: each.node_token,
            from_field: each.from_field,
            regular: values.feiShuConfig.regular,
          }
        })
      default:
        return values.fileList.map((item: any) => ({
          ...item,
          file_type: documentType,
          enable: true,
        }))
    }
  }

  const getMultiModalSteps = (): IStepItem[] => {
    const _steps = [
      {
        title: localize('select_add_method', '选择添加方式'),
        content: (
          <MulmodalTypeSelect
            onSelect={type => {
              setDocumentType(type)
              setStep(step + 1)
            }}
          />
        ),
      },
      {
        title: localize('add_document', '添加文档'),
        content: (
          <div>
            <UploadMultiModalForm
              getFileStatus={e => e && setUploadStatus(e)}
              documentType={documentType}
              hashPath={hashPath}
            />
            <div className='flex flex-justify-end mt-8 pt-16'>
              <Button
                size='large'
                className='!px-19px'
                loading={uploadStatus === 'uploading'}
                onClick={() => {
                  setStep(step - 1)
                }}
              >
                {localize('previous_step', '上一步')}
              </Button>
              <Track event='datastore_upload'>
                <Button
                  size='large'
                  className='ml-12 !px-27px'
                  type='primary'
                  onClick={handleFinish}
                  {...okButtonProps}
                  disabled={okButtonProps?.disabled || uploadStatus === 'error'}
                  loading={
                    okButtonProps?.loading || uploadStatus === 'uploading'
                  }
                >
                  {localize('complete', '完成')}
                </Button>
              </Track>
            </div>
          </div>
        ),
      },
    ]

    return _steps.map((item, index) => ({ ...item, stepNum: index + 1 }))
  }

  const getSteps = (): IStepItem[] => {
    const _steps = [
      {
        title: localize('select_add_method', '选择添加方式'),
        content: (
          <>
            <DocumentTypeSelect
              withTemplate={!hasDataset}
              onSelect={type => {
                setDocumentType(type)
                if (type === DocumentType.TEMPLATE_FILE) {
                  onFinish?.(null, type)
                } else {
                  setStep(step + 1)
                }
              }}
            />
          </>
        ),
      },
      {
        title: localize('add_documents', '添加文档'),
        description: ![DocumentType.FEISHU, DocumentType.DING].includes(
          documentType,
        ) && (
          <span className='text-12px/16px c-#8D8D99 pt-2px ml-4px'>
            （{localize('added_documents', '已添加')}
            {fileList?.length ?? 0}/{MAX_FILE_COUNT}）
          </span>
        ),
        content: (
          <ErrorBoundaryUnknown
            message={localize(
              'DocumentsModal.add_documents.unknown_error',
              'DocumentsModal.add_documents 发生未知错误',
            )}
          >
            <UploadFileForm
              getFileStatus={e => e && setUploadStatus(e)}
              documentType={documentType}
              datastoreType={datastoreType}
              hashPath={hashPath}
              partitionModelMaxLength={partitionModelMaxLength}
            />
            <div className='relative mt-[-8px]'>
              <div className='flex flex-justify-end mt-8 pt-24'>
                <Button
                  size='large'
                  className='!px-19px'
                  loading={uploadStatus === 'uploading'}
                  onClick={() => {
                    setStep(step - 1)
                  }}
                >
                  {localize('previous_step', '上一步')}
                </Button>
                <Track event='datastore_upload'>
                  <Button
                    size='large'
                    className='ml-12 !px-27px'
                    type='primary'
                    onClick={handleFinish}
                    {...okButtonProps}
                    disabled={
                      okButtonProps?.disabled || uploadStatus === 'error'
                    }
                    loading={
                      okButtonProps?.loading || uploadStatus === 'uploading'
                    }
                  >
                    {localize('complete', '完成')}
                  </Button>
                </Track>
              </div>
            </div>
          </ErrorBoundaryUnknown>
        ),
      },
    ]
    return _steps.map((item, index) => ({ ...item, stepNum: index + 1 }))
  }

  const getStepConfig = {
    [PartitionCategoryType.Document]: getSteps(),
    [PartitionCategoryType.QA]: getSteps(),
    [PartitionCategoryType.Multimodal]: getMultiModalSteps(),
  }

  return (
    <Modal
      title={title}
      open={open}
      footer={null}
      onCancel={handleResetModal}
      maskClosable={false}
      width={600}
      styles={{
        body: {
          padding: '24px 32px',
        },
      }}
    >
      <>
        <Form
          name='createForm'
          initialValues={{
            parseType:
              datastoreType === PartitionCategoryType.Multimodal
                ? DocumentParseType.AI_PARSING
                : DocumentParseType.GENERAL_PARSE,
            partition_model: embeddingModelList?.[0]?.model_name,
            rule_number: DocumentSplitType.Text,
            max_length: partitionModelMaxLength ?? 500,
          }}
          form={form}
          layout='vertical'
        >
          {datastoreType === PartitionCategoryType.QA ? (
            <>
              <UploadQAFileForm
                getFileStatus={e => e && setUploadStatus(e)}
                hashPath={hashPath}
              />
              <div className='flex flex-justify-end mt-8 pt-16'>
                <Button size='large' onClick={onCancel}>
                  {localize('cancel', '取消')}
                </Button>
                <Track event='datastore_upload'>
                  <Button
                    size='large'
                    className='ml-12 !px-27px'
                    type='primary'
                    onClick={() => {
                      handleFinish?.()
                    }}
                    {...okButtonProps}
                    disabled={
                      okButtonProps?.disabled || uploadStatus === 'error'
                    }
                    loading={
                      okButtonProps?.loading || uploadStatus === 'uploading'
                    }
                  >
                    {localize('complete', '完成')}
                  </Button>
                </Track>
              </div>
            </>
          ) : (
            <Step steps={getStepConfig[datastoreType]} currentStep={step} />
          )}
        </Form>
      </>
    </Modal>
  )
}
