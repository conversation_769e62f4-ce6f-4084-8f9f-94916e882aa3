import { localize } from '@bty/localize'
import { Tooltip } from 'antd'
import { Segmented } from '@/components'

import { SearchScopeType, type KnowledgeDataValue } from '../../types'
import type { Variable } from '@/features/editor'

export default (props: {
  disabled?: boolean
  onChange?: (values: KnowledgeDataValue) => void
  variables: Variable[]
  value?: KnowledgeDataValue
}) => {
  const { disabled, onChange, value } = props
  return (
    <>
      <div
        className='h-1px w-[calc(100%+32px)] mb-16px'
        style={{
          transform: 'translate(-16px, 0)',
          background: 'rgba(225, 225, 229, 0.6)',
        }}
      />
      <Segmented
        block
        size='small'
        style={{ padding: 2 }}
        disabled={disabled}
        options={[
          {
            label: (
              <div className='flex justify-center items-center h-16px'>
                <Tooltip
                  title={localize(
                    'search_knowledge_base_tooltip',
                    '查询文档中段落内容',
                  )}
                >
                  {localize('search_knowledge_chunk', '查询段落')}
                </Tooltip>
              </div>
            ),
            value: 'content_match',
          },
          {
            label: (
              <div className='flex justify-center items-center h-16px'>
                <Tooltip
                  title={localize(
                    'insert_knowledge_base_tooltip',
                    '查询完整的文档内容',
                  )}
                >
                  {localize('search_knowledge_doc', '查询文档')}
                </Tooltip>
              </div>
            ),
            value: 'file_match',
          },
        ]}
        value={value?.match_type}
        onChange={(e: KnowledgeDataValue['match_type']) => {
          onChange?.({
            ...value,
            searchScope:
              e === 'content_match'
                ? SearchScopeType.All
                : SearchScopeType.Metadata,
            match_type: e,
          })
        }}
      />
    </>
  )
}
