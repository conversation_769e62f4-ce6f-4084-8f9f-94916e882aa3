import { localize } from '@bty/localize'
import { <PERSON>, Tooltip, Switch } from 'antd'
import {
  MetaPropertyCombinator,
  PartitionCategoryType,
} from '@apis/datastore/type'
import { FLOW_DRAFT_LOCK_STATUS } from '@/store'
import { VariableRegex } from '@/constants/common.ts'
import { CodeEditor } from '@/features/editor/CodeEditor'
import { DatasetFileSelect } from '../../components/DatasetSelect/DatasetFileSelect'
import { KnowledgeContentExample } from '../../components/KnowledageContentExample'
import { FilePopover } from '../components/FilePopover'
import { SearchRangeSelect } from '../components/SearchRangeSelect'
import { MemoryTagSelect } from '../../memory/MemoryTagSelect'
import { MemoryOutputTypeSelect } from '../../memory/MemoryOutputTypeSelect'
import { Segmented, IconFont, SliderSingle } from '@/components'
import OperationSelect from '../components/OperationSelect'
import RepoTypeSelect from '../components/RepoTypeSelect'
import MatchTypeSelect from '../components/MatchTypeSelect'
import SearchContent from '../components/SearchContent'
import MaxResultSlider from '../components/MaxResultSlider'
import ResultReRank from '../components/ResultReRank'
import SimilaritySlider from '../components/SimilaritySlider'
import { MultiRepoSearchContent } from '../components/MultiRepoSearchContent'
import { ShowKnowledgeReferenceComponent } from '@/features/agent/components/ShowKnowledgeReferenceComponent'
import { FilterType, RankingStrategy, SearchScopeType } from '../constants'
import type { JsonFormConfig } from '../../components'
import { FilterRule } from '../components/FilterRule'
import type { KnowledgeDataValue } from '../types'

// 抽取常见表单项配置到单独函数
function createBaseFormItems({
  lockStatus,
  memoryVal,
  memoryTypeVal,
  isMultiRepo,
  isExpired,
  filterDatasetList,
  variables,
  nodeElement,
  selectPopupRef,
  getCurrentDatasetItem,
  getDatasetTags,
  handleFilterTags,
  handleFormChange,
  form,
}) {
  return [
    {
      noStyle: true,
      name: ['inputs'],
      render: () => (
        <OperationSelect
          disabled={lockStatus !== FLOW_DRAFT_LOCK_STATUS.UNLOCK}
        />
      ),
    },
    {
      label: localize('knowledge_base', '知识库'),
      name: ['inputs', 'memory'],
      rightContent: (
        <FilePopover
          popupContainerRef={selectPopupRef}
          disabled={!memoryVal || VariableRegex.test(String(memoryVal))}
          memoryVal={!VariableRegex.test(String(memoryVal)) && memoryVal}
        />
      ),
      required: true,
      rules: [
        {
          required: true,
          message:
            memoryTypeVal === 'insertMemory'
              ? localize('please_select_knowledge_base', '请选择知识库')
              : localize(
                  'please_select_knowledge_base_or_input_variable_representing_knowledge_base_id',
                  '请选择知识库或输入表示知识库ID的变量',
                ),
        },
        {
          validator: (_, _value: any) => {
            if (isExpired) {
              return Promise.reject('知识库已过期，请重新选择')
            }
            return Promise.resolve()
          },
        },
      ],
      type: 'DatasetSelect',
      disabled: lockStatus !== FLOW_DRAFT_LOCK_STATUS.UNLOCK,
      componentProps: {
        datasetList: filterDatasetList,
        variables: memoryTypeVal === 'insertMemory' ? [] : variables,
        variableTipsContainer: nodeElement,
        onChange: (val: number) => {
          if (val && !VariableRegex.test(String(val))) {
            const currentDatasetItem = getCurrentDatasetItem(val)
            const isMulti =
              currentDatasetItem?.partition_category ===
              PartitionCategoryType.Multimodal
            const repoType = isMulti ? 'multi_repo' : 'normal_repo'

            getDatasetTags(val, true).then(tags => {
              const newTags = handleFilterTags(tags)

              // 创建基础1字段数组
              const fieldsToSet = [
                { name: ['inputs', 'fileIds'], value: [] },
                { name: ['inputs', 'file_id'], value: undefined },
                { name: ['inputs', 'tags'], value: newTags },
                { name: ['inputs', 'repo_type'], value: repoType },
                {
                  name: ['inputs', 'filter_sql'],
                  value: '',
                },
                {
                  name: ['inputs', 'filter_type'],
                  value: FilterType.FilterRules,
                },
                {
                  name: ['inputs', 'filter_rules'],
                  value: {
                    combinator: MetaPropertyCombinator.AND,
                    rules: [],
                  },
                },
                { name: ['inputs', 'match_type'], value: 'content_match' },
                {
                  name: ['inputs', 'searchScope'],
                  value: SearchScopeType.All,
                },
              ]

              const params = {
                file_id: undefined,
                fileIds: [],
                memory: val,
                tags: newTags,
                repo_type: repoType,
                filter_type: FilterType.FilterRules,
                filter_rules: {
                  combinator: MetaPropertyCombinator.AND,
                  rules: [],
                },
                filter_sql: '',
                searchScope: SearchScopeType.All,
                match_type: 'content_match',
              } as KnowledgeDataValue
              form.setFields(fieldsToSet)
              handleFormChange(params)
            })
          } else {
            handleFormChange({ memory: val })
          }
        },
      },
    },
    {
      name: ['inputs'],
      hidden: isMultiRepo || memoryTypeVal === 'insertMemory',
      render: () => (
        <MatchTypeSelect
          variables={variables}
          disabled={lockStatus !== FLOW_DRAFT_LOCK_STATUS.UNLOCK}
        />
      ),
    },
  ]
}

// 插入知识库的表单项
function createInsertMemoryFormItems({
  resolvedOperationType,
  lockStatus,
  memoryVal,
  variables,
  nodeElement,
  isSelectedQADataSet,
  form,
  handleFormChange,
  selectPopupRef,
}) {
  const operationTypeFormItem = {
    render: () => (
      <Segmented
        disabled={lockStatus !== FLOW_DRAFT_LOCK_STATUS.UNLOCK}
        value={resolvedOperationType}
        options={[
          {
            label: (
              <Tooltip
                title={localize('update_tooltip', '将数据内容更新在某个文件中')}
                getPopupContainer={() =>
                  selectPopupRef.current ?? document.body
                }
                destroyTooltipOnHide
              >
                {localize('update_file', '更新文件')}
              </Tooltip>
            ),
            value: 'update',
          },
          {
            label: (
              <Tooltip
                title={localize(
                  'create_tooltip',
                  '根据文件名、数据内容新建文件',
                )}
                getPopupContainer={() =>
                  selectPopupRef.current ?? document.body
                }
                destroyTooltipOnHide
              >
                {localize('create_file', '新建文件')}
              </Tooltip>
            ),
            value: 'create',
          },
        ]}
        size='small'
        onChange={value => {
          const isCreate = value === 'create'
          const fieldToReset = isCreate ? 'file_id' : 'file_name'
          const formValue = {
            [fieldToReset]: null,
            operation_type: value,
          }
          form.setFieldsValue({
            inputs: {
              [fieldToReset]: null,
              operation_type: value,
            },
          })
          handleFormChange(formValue)
        }}
      />
    ),
  }

  const inputFileNameFormItem = {
    label: localize('file_name', '文件名称'),
    name: ['inputs', 'file_name'],
    required: true,
    rules: [
      { required: true, message: localize('input_filename', '请输入文件名称') },
    ],
    render: () => (
      <CodeEditor
        className='ace-gray h-32px flex-1'
        wrapEnabled={false}
        width='100%'
        setOptions={{ maxLines: 1 }}
        variableTipsContainer={nodeElement}
        variables={variables}
        placeholder={localize(
          'new_file_placeholder',
          '输入新增文件的名称，可通过变量设置',
        )}
        singleLine
        onChange={value => handleFormChange({ file_name: value })}
      />
    ),
  }

  const selectFileNameFormItem = {
    label: localize('data_file', '数据文件'),
    name: ['inputs', 'file_id'],
    required: true,
    hidden: !memoryVal,
    rules: [{ required: true, message: localize('file_id', '请选择数据文件') }],
    render: () => (
      <DatasetFileSelect
        disabled={lockStatus !== FLOW_DRAFT_LOCK_STATUS.UNLOCK}
        memoryId={memoryVal}
        mode='single'
        onChange={e => {
          form.setFieldValue(['inputs', 'file_id'], e)
          handleFormChange({ file_id: e })
        }}
        variables={variables}
        variableTipsContainer={nodeElement as HTMLElement}
      />
    ),
  }

  const fileContentFormItem = {
    label: localize('data_content', '数据内容'),
    name: ['inputs', 'content'],
    required: true,
    type: 'TextEditor' as const,
    rules: [
      {
        required: true,
        message: localize('content_not_empty', '内容不能为空'),
      },
    ],
    tooltip: localize(
      'content_use_tips',
      '文档库仅支持纯文本数据；问答库仅支持 JSON 格式',
    ),
    className: '!mb-0',
    componentProps: {
      placeholder: isSelectedQADataSet
        ? localize(
            'is_select_QA_dataset',
            '填写或用变量输入 QA 问答的 JSON 数据',
          )
        : localize(
            'is_no_select_QA_dataset',
            '输入上下文的变量或纯文本数据，并将变量的数据或者纯文本数据导入知识库中',
          ),
      variables,
      variableTipsContainer: nodeElement,
      onChange: (e: string) => {
        handleFormChange({ content: e })
      },
      innerTooltipContent: isSelectedQADataSet && <KnowledgeContentExample />,
    },
  }

  const items = [operationTypeFormItem]

  if (resolvedOperationType === 'create') {
    items.push(inputFileNameFormItem, fileContentFormItem as any)
  } else {
    items.push(selectFileNameFormItem, fileContentFormItem as any)
  }

  return items
}

// 搜索表单项
function createSearchFormItems({
  memoryVal,
  searchScopeVal,
  rankingStrategyVal,
  isContentMatch,
  lockStatus,
  selectPopupRef,
  inNodePanel,
  isMultiRepo,
  variables,
  nodeElement,
  form,
  handleFormChange,
  expanded,
  tagsVal,
  datasetTagsMap,
}) {
  // 多仓库搜索配置
  const multiRepoSearchItems = [
    {
      noStyle: true,
      name: ['inputs'],
      hidden: !isContentMatch,
      render: () => {
        return (
          <div className='h-100% w-100%'>
            <MultiRepoSearchContent
              disabled={lockStatus !== FLOW_DRAFT_LOCK_STATUS.UNLOCK}
              name={['inputs', 'search_list']}
              variables={variables}
              nodeElement={nodeElement}
            />
          </div>
        )
      },
    },
    {
      hidden: !isContentMatch,
      label: localize('max_result_num', '最大结果数'),
      name: ['inputs', 'maxResultNum'],
      layout: 'horizontal',
      colon: false,
      tooltip: localize(
        'from_knowledge_base_query_x_results_x_is_the_number_you_set_the_maximum_value_of_x_is_30_it_is_recommended_that_x_value_does_not_exceed_10_to_avoid_exceeding_the_maximum_token_support_of_large_models',
        '最大结果数',
      ),
      render: () => <MaxResultSlider />,
    },
  ]

  // 普通仓库搜索配置
  const normalRepoSearchItems = [
    {
      noStyle: true,
      name: ['inputs'],
      hidden: !isContentMatch,
      render: () => {
        const errorContent = form.getFieldError(['inputs', 'searchContent'])
        return (
          <SearchContent
            nodeElement={nodeElement}
            disabled={lockStatus !== FLOW_DRAFT_LOCK_STATUS.UNLOCK}
            variables={variables}
            errorContent={errorContent}
          />
        )
      },
    },
    {
      label: localize('max_result_num', '最大结果数'),
      name: ['inputs', 'maxResultNum'],
      hidden: !isContentMatch,
      layout: 'horizontal',
      colon: false,
      tooltip: localize(
        'from_knowledge_base_query_x_results_x_is_the_number_you_set_the_maximum_value_of_x_is_30_it_is_recommended_that_x_value_does_not_exceed_10_to_avoid_exceeding_the_maximum_token_support_of_large_models',
        '最大结果数',
      ),
      render: () => <MaxResultSlider />,
    },
    {
      label: localize('max_result_num', '最大文档数'),
      name: ['inputs', 'maxDocumentsCount'],
      hidden: isContentMatch,
      layout: 'horizontal',
      colon: false,
      tooltip: localize('max_doc_num', '最大文档数'),
      render: () => <SliderSingle min={1} max={5} size='small' step={1} />,
    },
    {
      noStyle: true,
      name: ['inputs', 'rankingStrategy'],
      hidden: !isContentMatch || !expanded,
      render: () => <ResultReRank />,
    },
    {
      label: localize('min_similarity', '最低相似度'),
      hidden:
        !(rankingStrategyVal === RankingStrategy.ON) ||
        !isContentMatch ||
        !expanded,
      name: ['inputs', 'similarity'],
      layout: 'horizontal',
      tooltip: localize(
        'a_threshold_set_to_filter_matching_results_with_similarity_below_this_value_generally_0_4_is_suitable_and_0_5_or_above_is_precise',
        '设定的一个阈值，过滤掉相似度低于此值的匹配结果，一般0.4属于适中，0.5以上属于精确。',
      ),
      render: () => <SimilaritySlider />,
    },
  ]

  // 通用搜索表单项
  const commonSearchItems = [
    {
      label: (
        <div className='font-500 text-12px flex items-center'>
          {localize('search_range', '查询范围')}
        </div>
      ),
      name: ['inputs'],
      hidden: !memoryVal || !isContentMatch,
      render: () => {
        return (
          <SearchRangeSelect
            popupContainerRef={selectPopupRef}
            inNodePanel={inNodePanel}
            isMultiRepo={isMultiRepo}
          />
        )
      },
    },
    {
      hidden:
        !(memoryVal && searchScopeVal === SearchScopeType.Metadata) ||
        isMultiRepo,
      name: ['inputs'],
      render: () => {
        return (
          <FilterRule
            variables={variables}
            lockStatus={lockStatus}
            getPopupContainer={() => selectPopupRef.current!}
            variableTipsContainer={nodeElement as HTMLElement}
          />
        )
      },
    },
    {
      noStyle: true,
      hidden: !(memoryVal && searchScopeVal === SearchScopeType.Tags),
      render: () => (
        <>
          <div className='flex items-center text-12px font-500 mb-12'>
            <div className='flex items-center'>
              {localize('select_tag', '选择标签')}
            </div>
          </div>
          <Form.Item
            noStyle
            shouldUpdate={(prev, curr) =>
              prev.inputs.memory !== curr.inputs.memory
            }
          >
            {({ getFieldValue }) => {
              const memory = getFieldValue(['inputs', 'memory'])
              const tagsList = datasetTagsMap[memory] ?? []
              return (
                <div className='mb-16px'>
                  <MemoryTagSelect
                    tags={tagsList}
                    allEnable={true}
                    disabled={lockStatus !== FLOW_DRAFT_LOCK_STATUS.UNLOCK}
                    onChange={e => {
                      const res = e
                      form.setFieldValue(
                        ['inputs', 'tags'],
                        !e?.length ? null : e,
                      )
                      handleFormChange({ tags: res })
                    }}
                    value={tagsVal || []}
                    variables={variables}
                    variableTipsContainer={nodeElement as HTMLElement}
                  />
                </div>
              )
            }}
          </Form.Item>
        </>
      ),
    },
    {
      label: localize('select_file', '选择文件'),
      name: ['inputs', 'fileIds'],
      required: true,
      hidden: !(memoryVal && searchScopeVal === SearchScopeType.SpecifyFileIds),
      rules:
        memoryVal && searchScopeVal === SearchScopeType.SpecifyFileIds
          ? [
              {
                required: true,
                message: localize('please_select_data_file', '请选择数据文件'),
              },
            ]
          : [],
      render: () => {
        return (
          <DatasetFileSelect
            disabled={lockStatus !== FLOW_DRAFT_LOCK_STATUS.UNLOCK}
            memoryId={memoryVal}
            onChange={e => {
              form.setFieldValue(['inputs', 'fileIds'], e)
              handleFormChange({ fileIds: e })
            }}
            variables={variables}
            variableTipsContainer={nodeElement as HTMLElement}
          />
        )
      },
    },
    // 根据仓库类型选择搜索项
    ...(isMultiRepo ? multiRepoSearchItems : normalRepoSearchItems),
    // 知识原文下载链接
    {
      noStyle: true,
      hidden: !isContentMatch || !expanded,
      name: ['inputs', 'show_knowledge_url'],
      render: () => {
        const showKnowledgeUrl = form.getFieldValue([
          'inputs',
          'show_knowledge_url',
        ])
        return (
          <div className='flex flex-col mb-16px'>
            <div className='flex flex-1 items-center'>
              <div className='font-500 text-12px'>
                {localize('show_knowledge_url', '展示知识原文下载链接')}
              </div>
              <Tooltip
                title={localize(
                  'enable_after_the_flow_user_can_download_the_original_file_through_the_link_in_the_log_it_is_recommended_to_keep_the_state_closed_to_avoid_knowledge_leakage_risk',
                  '开启后，Flow 的使用者可以通过日志中的链接下载原文件。建议保持关闭状态，以避免知识泄漏风险。',
                )}
              >
                <IconFont
                  className='ml-[4px] mr-8px'
                  style={{ color: 'rgba(141, 141, 153, 0.4)' }}
                  name='jieshishuimeng'
                />
              </Tooltip>
              <Switch
                size='small'
                value={showKnowledgeUrl}
                checked={showKnowledgeUrl}
                onChange={e => {
                  form.setFieldValue(['inputs', 'show_knowledge_url'], e)
                  handleFormChange({ show_knowledge_url: e })
                }}
              />
            </div>
            {showKnowledgeUrl && (
              <ShowKnowledgeReferenceComponent business='flow' />
            )}
          </div>
        )
      },
    },
    // 输出设置
    {
      label: localize('output_setting', '输出设置'),
      required: true,
      hidden: !expanded || !isContentMatch,
      className: 'important:mb-0',
      name: ['inputs', 'outputType'],
      render: () => (
        <MemoryOutputTypeSelect
          onChange={e => {
            form.setFieldValue(['inputs', 'outputType'], e)
            handleFormChange({ outputType: e })
          }}
        />
      ),
    },
  ]

  return commonSearchItems
}

// 主函数优化
export function generateKnowledgeFormItems({
  form,
  lockStatus = FLOW_DRAFT_LOCK_STATUS.UNLOCK,
  selectPopupRef,
  memoryVal,
  searchScopeVal,
  rankingStrategyVal,
  memoryTypeVal,
  tagsVal,
  resolvedOperationType,
  handleFormChange,
  filterDatasetList,
  isSelectedQADataSet,
  isMultiRepo,
  isExpired,
  nodeElement,
  variables,
  expanded = true,
  datasetTagsMap,
  getCurrentDatasetItem,
  getDatasetTags,
  handleFilterTags,
  inNodePanel = false,
  isContentMatch,
}: any): JsonFormConfig[] {
  // 获取基础表单项
  const baseFormItems = createBaseFormItems({
    lockStatus,
    memoryVal,
    memoryTypeVal,
    isMultiRepo,
    isExpired,
    filterDatasetList,
    variables,
    nodeElement,
    selectPopupRef,
    getCurrentDatasetItem,
    getDatasetTags,
    handleFilterTags,
    handleFormChange,
    form,
  })

  const returnValue = [...baseFormItems] as JsonFormConfig[]

  // 根据内存类型添加不同的表单项
  if (memoryTypeVal !== 'insertMemory') {
    // 对于非插入内存操作，添加仓库类型选择和搜索表单项
    returnValue.splice(2, 0, {
      name: ['inputs'],
      hidden:
        !memoryVal || !VariableRegex.test(String(memoryVal)) || !isContentMatch,
      render: () => (
        <RepoTypeSelect
          disabled={lockStatus !== FLOW_DRAFT_LOCK_STATUS.UNLOCK}
        />
      ),
    })

    // 添加搜索表单项
    const searchItems = createSearchFormItems({
      memoryVal,
      searchScopeVal,
      rankingStrategyVal,
      isContentMatch,
      lockStatus,
      selectPopupRef,
      inNodePanel,
      isMultiRepo,
      variables,
      nodeElement,
      form,
      handleFormChange,
      expanded,
      tagsVal,
      datasetTagsMap,
    })

    returnValue.push(...searchItems)
  } else if (memoryVal) {
    // 对于插入内存操作，添加相应的表单项
    const insertMemoryItems = createInsertMemoryFormItems({
      resolvedOperationType,
      lockStatus,
      memoryVal,
      variables,
      nodeElement,
      isSelectedQADataSet,
      form,
      handleFormChange,
      selectPopupRef,
    })

    returnValue.push(...insertMemoryItems)
  }

  return returnValue
}
