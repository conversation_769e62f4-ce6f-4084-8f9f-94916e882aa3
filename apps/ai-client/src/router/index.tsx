import { createAuth<PERSON><PERSON><PERSON><PERSON>outer } from '@bty/react-auth'
import { Spin } from 'antd'
import { Navigate, Outlet } from 'react-router-dom'
import NiceModal from '@ebay/nice-modal-react'
import { agentRoutes } from '@/router/agent.tsx'
import { defaultRouterPath, grayscaleMap } from '@/constants/common.ts'
import { loader as loginLoader } from '@/pages/login/loginNew'
import Login from '@/pages/login/login'
import type { AuthRoute } from '@/auth'
import auth from '@/auth'
import ApplicationPublicPage from '@/pages/application/public'
import { NotAllowed } from '@/pages/notAllowed'
import { NotFound } from '@/pages/404'
import Pay from '@/pages/pay'
import { ShareAgent } from '@/pages/agent/shareAgent'
import Chat from '@/pages/agent/chat'
import HomeLayout from '@/pages/home/<USER>'
import { Explore } from '@/pages/explore/Explore'
import { AgentList } from '@/pages/agent/list/list'
import WorkSpaceSettings from '@/pages/workspace/settings'
import Overview from '@/pages/workspace/settings/Overview'
import Users from '@/pages/workspace/settings/Users'
import Orders from '@/pages/workspace/settings/Orders'
import AppList from '@/pages/application/list'
import AppDetail from '@/pages/application/detail'
import DatastorePage from '@/pages/datastores/Page'
import ChatBot, { loader as chatBotLoader } from '@/pages/chatbot'
import { Statistics } from '@/pages/statistics'
import { PushNotificationProvider } from '@/providers/PushNotificationProvider'
import ShareChannel from '@/pages/agent/shareChannel/ShareChannel.tsx'
import { dbRoutes, dbRoutesNoLayout } from '@/router/db.tsx'
import Data from '@/pages/workspace/settings/Data'
import { UsageStatistics } from '@/pages/statistics/UsageStatistics'
import { Alarm } from '@/pages/workspace/settings/alarm'
import { GlobalEnvProvider } from '@/components/global-env'
import { KnowledgeStatistics } from '@/pages/statistics/KnowledgeStatistics'
import { DataSource } from '@/pages/workspace/settings/data-source'
import { applicationRoutes } from './application'
import { templatesRoutes } from './templates'
import { chromePluginRoutes } from './chromePlugin'
import { selectWorkSpaceRoutes } from './selectWorkspace'
import { withoutLayoutPluginsRoutes, withLayoutPluginsRoutes } from './plugin'
import { filePreviewRoutes } from './filePreview'
import { dataStoreRoutes } from './datastore'
import { mcpRoutes, mcpRoutesNoLayout } from './mcp'

function LoadComponent() {
  return (
    <div className='absolute top-0 right-0 bottom-0 left-0 flex-center '>
      <Spin />
    </div>
  )
}

const router = createAuthBrowserRouter(
  [
    {
      path: '/',
      element: (
        <PushNotificationProvider>
          <Outlet />
        </PushNotificationProvider>
      ),
      errorElement: <NotFound />,
      children: [
        {
          index: true,
          errorElement: <Navigate to='/404' />,
          element: <Navigate to={defaultRouterPath} />,
        },
        {
          path: '/login',
          element: <Login />,
          loader: loginLoader,
        },
        {
          path: '/chat/quickLink?/:botId/:conversationId?',
          element: <Chat />,
          redirect: '/notAllowed',
        },
        {
          path: '/shareChannel/:botId/:conversationId?',
          element: <ShareChannel />,
          redirect: '/notAllowed',
        },
        { path: '/public/app/:id/:tag?', element: <ApplicationPublicPage /> },
        { path: '/shareAgent/:id/:robot_id', element: <ShareAgent /> },
        {
          path: '',
          element: <HomeLayout />,
          children: [
            {
              path: '/explore',
              auth: 'auth',
              element: <Explore />,
            },
            {
              path: '/chatbot/:botId',
              auth(s) {
                return s.auth && s.grayscale(grayscaleMap.chat)
              },
              loader: chatBotLoader,
              element: <ChatBot />,
            },
            {
              path: '/agent',
              auth(s) {
                return s.auth && s.grayscale(grayscaleMap.agent)
              },
              redirect: '/application', // agent现在作为首页如果没有灰度的话那么就重定向flow列表
              element: <AgentList />,
            },
            {
              path: '/application',
              auth: 'auth',
              element: <AppList />,
            },
            {
              path: '/application/:id/:tag?',
              auth: 'auth',
              element: (
                <GlobalEnvProvider path='/application/:id'>
                  {env => <AppDetail key={env} />}
                </GlobalEnvProvider>
              ),
            },
            {
              path: '/datastores',
              auth: 'auth',
              element: <DatastorePage />,
            },
            {
              path: '/statistics',
              auth: 'auth',
              element: <Statistics />,
              children: [
                {
                  path: 'usageStatistics',
                  element: <UsageStatistics />,
                },
                {
                  path: 'resourceStatistics',
                  element: <Data />,
                },
                {
                  path: 'knowledgeStatistics',
                  element: <KnowledgeStatistics />,
                },
              ],
            },
            {
              path: '/workspace/:id/settings',
              auth(_s) {
                // return s.role([RoleCode.ADMINISTRATOR, RoleCode.DEVELOPER])
                return true
              },
              redirect: '/notAllowed',
              element: <WorkSpaceSettings />,
              children: [
                {
                  path: 'overview',
                  element: <Overview />,
                },
                // {
                //   path: 'base',
                //   element: <Base />,
                // },
                {
                  path: 'users',
                  element: <Users />,
                },
                {
                  path: 'orders',
                  element: <Orders />,
                },
                {
                  path: 'alarm',
                  element: <Alarm />,
                },
                {
                  path: 'dataSource',
                  element: <DataSource />,
                },
              ],
            },
            ...dbRoutes,
            ...withLayoutPluginsRoutes,
            ...mcpRoutes,
          ] as AuthRoute[],
        },
        { path: '/notAllowed', element: <NotAllowed /> },
        { path: '/pay', auth: 'auth', element: <Pay /> },
        { path: '/404', element: <NotFound /> },
        ...applicationRoutes,
        ...dataStoreRoutes,
        ...templatesRoutes,
        ...agentRoutes,
        ...chromePluginRoutes,
        ...selectWorkSpaceRoutes,
        ...dbRoutesNoLayout,
        ...withoutLayoutPluginsRoutes,
        ...filePreviewRoutes,
        ...mcpRoutesNoLayout,
      ],
    },
  ],
  {
    auth,
    defaultRedirectPath: '/login',
    loadComponent: LoadComponent,
    provider: NiceModal.Provider,
  },
)

export default router
