# 本地联调 在.env添加 例：  VITE_AI_API_BASE_URL=http://192.168.1.76:8001
VITE_AI_API_BASE_URL=https://dev-ai-api.betteryeah.com
VITE_DATASET_API_BASE_URL=https://dev-ai-api.betteryeah.com
VITE_USER_CENTER_BASE_URL=https://dev-usercenter.bantouyan.com
VITE_AI_OSS_APP_URL=//web-bty-ai-front-dev.oss-cn-hangzhou.aliyuncs.com
VITE_AI_DATASTORE_OSS_BUCKET=bty-gemini-data-dev
VITE_AI_DATASTORE_RESOURCE_BUCKET=bty-gemini-resource-dev
VITE_AI_WS_BASE_URL=wss://dev-ai-api.betteryeah.com
VITE_KEYU_DB_WAIT_LIST_PROJECT_ID=eqlrewkk0sjeq1e8rjxch25w13m67vji08n9
VITE_DOC_URL=https://ai-docs.betteryeah.com/%E4%BB%8B%E7%BB%8D/%E6%AC%A2%E8%BF%8E%E4%BD%BF%E7%94%A8BetterYeah%20AI.html # 文档地址
VITE_HOME_WEBSITE_URL=https://dev-www.betteryeah.com # 官网地址
VITE_CHATBOT_MOBILE_URL=https://dev-m-chatbot.betteryeah.com # 移动端地址
VITE_FLOAT_BTN_IFRAME_URL=https://dev-m-chatbot.betteryeah.com/chat # 右下角按钮 iframe 地址
VITE_DB_API_BASE_URL=https://dev-gemini-db.betteryeah.com
VITE_AGENT_LINKS_URL=https://dev-links.betteryeah.com
VITE_AGENT_CHAT_URL=https://dev-agent-chat.betteryeah.com
VITE_WEB_IDE_URL=https://dev-ai.betteryeah.com/web-ide/index.html  # 本地调试， 1.启动 apps/web-ide 项目（根目录pnpm run dev:web-ide） 2.在.env添加 VITE_WEB_IDE_URL=http://localhost:3000
VITE_TRACK_TABLE=track-dev # 埋点表名
VITE_APP_NAME='BetterYeah'
VITE_APP_ICO=/ico.png
VITE_APP_OG_IMAGE=https://resource.bantouyan.com/battleyeah-ai/ai-website/assets/favicon.png

VITE_APP_LOGO=""
VITE_APP_MINI_LOGO=""
VITE_IP_WHITELIST=""

VITE_DEPLOY_TYPE=NORMAL

# office预览提供商： aliyun 或者 onlyoffice
VITE_OFFICE_PREVIEW_PROVIDER=onlyoffice
VITE_ONLYOFFICE_SERVER_URL=https://dev-document-server.betteryeah.com
