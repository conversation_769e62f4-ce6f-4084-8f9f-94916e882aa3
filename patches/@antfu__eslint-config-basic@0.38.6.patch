diff --git a/index.js b/index.js
index 8b27623fc8f0c53d93c47235382241fcb448a805..4f499e4661f78e80142d680542d25f7a9aaf1d1f 100644
--- a/index.js
+++ b/index.js
@@ -6,377 +6,394 @@ module.exports = {
   },
   reportUnusedDisableDirectives: true,
   extends: [
-    './standard',
-    'plugin:import/recommended',
-    'plugin:eslint-comments/recommended',
-    'plugin:jsonc/recommended-with-jsonc',
-    'plugin:yml/standard',
-    'plugin:markdown/recommended',
+    "./standard",
+    "plugin:import/recommended",
+    "plugin:eslint-comments/recommended",
+    "plugin:jsonc/recommended-with-jsonc",
+    "plugin:yml/standard",
   ],
   ignorePatterns: [
-    '*.min.*',
-    '*.d.ts',
-    'CHANGELOG.md',
-    'dist',
-    'LICENSE*',
-    'output',
-    'out',
-    'coverage',
-    'public',
-    'temp',
-    'package-lock.json',
-    'pnpm-lock.yaml',
-    'yarn.lock',
-    '__snapshots__',
+    "*.min.*",
+    "*.d.ts",
+    "CHANGELOG.md",
+    "dist",
+    "LICENSE*",
+    "output",
+    "out",
+    "coverage",
+    "public",
+    "temp",
+    "package-lock.json",
+    "pnpm-lock.yaml",
+    "yarn.lock",
+    "__snapshots__",
     // ignore for in lint-staged
-    '*.css',
-    '*.png',
-    '*.ico',
-    '*.toml',
-    '*.patch',
-    '*.txt',
-    '*.crt',
-    '*.key',
-    'Dockerfile',
+    "*.css",
+    "*.png",
+    "*.ico",
+    "*.toml",
+    "*.patch",
+    "*.txt",
+    "*.crt",
+    "*.key",
+    "Dockerfile",
     // force include
-    '!.github',
-    '!.vitepress',
-    '!.vscode',
+    "!.github",
+    "!.vitepress",
+    "!.vscode",
     // force exclude
-    '.vitepress/cache',
-  ],
-  plugins: [
-    'html',
-    'unicorn',
-    'antfu',
-    'no-only-tests',
-    'unused-imports',
+    ".vitepress/cache",
   ],
+  plugins: ["html", "unicorn", "antfu", "no-only-tests", "unused-imports"],
   settings: {
-    'import/resolver': {
-      node: { extensions: ['.js', '.mjs'] },
+    "import/resolver": {
+      node: { extensions: [".js", ".mjs"] },
     },
   },
   overrides: [
     {
-      files: ['*.json', '*.json5'],
-      parser: 'jsonc-eslint-parser',
+      files: ["*.json", "*.json5"],
+      parser: "jsonc-eslint-parser",
       rules: {
-        'jsonc/array-bracket-spacing': ['error', 'never'],
-        'jsonc/comma-dangle': ['error', 'never'],
-        'jsonc/comma-style': ['error', 'last'],
-        'jsonc/indent': ['error', 2],
-        'jsonc/key-spacing': ['error', { beforeColon: false, afterColon: true }],
-        'jsonc/no-octal-escape': 'error',
-        'jsonc/object-curly-newline': ['error', { multiline: true, consistent: true }],
-        'jsonc/object-curly-spacing': ['error', 'always'],
-        'jsonc/object-property-newline': ['error', { allowMultiplePropertiesPerLine: true }],
+        "jsonc/array-bracket-spacing": ["error", "never"],
+        "jsonc/comma-dangle": ["error", "never"],
+        "jsonc/comma-style": ["error", "last"],
+        "jsonc/indent": ["error", 2],
+        "jsonc/key-spacing": [
+          "error",
+          { beforeColon: false, afterColon: true },
+        ],
+        "jsonc/no-octal-escape": "error",
+        "jsonc/object-curly-newline": [
+          "error",
+          { multiline: true, consistent: true },
+        ],
+        "jsonc/object-curly-spacing": ["error", "always"],
+        "jsonc/object-property-newline": [
+          "error",
+          { allowMultiplePropertiesPerLine: true },
+        ],
       },
     },
     {
-      files: ['*.yaml', '*.yml'],
-      parser: 'yaml-eslint-parser',
+      files: ["*.yaml", "*.yml"],
+      parser: "yaml-eslint-parser",
       rules: {
-        'spaced-comment': 'off',
+        "spaced-comment": "off",
       },
     },
     {
-      files: ['package.json'],
-      parser: 'jsonc-eslint-parser',
+      files: ["package.json"],
+      parser: "jsonc-eslint-parser",
       rules: {
-        'jsonc/sort-keys': [
-          'error',
+        "jsonc/sort-keys": [
+          "error",
           {
-            pathPattern: '^$',
+            pathPattern: "^$",
             order: [
-              'publisher',
-              'name',
-              'displayName',
-              'type',
-              'version',
-              'private',
-              'packageManager',
-              'description',
-              'author',
-              'license',
-              'funding',
-              'homepage',
-              'repository',
-              'bugs',
-              'keywords',
-              'categories',
-              'sideEffects',
-              'exports',
-              'main',
-              'module',
-              'unpkg',
-              'jsdelivr',
-              'types',
-              'typesVersions',
-              'bin',
-              'icon',
-              'files',
-              'engines',
-              'activationEvents',
-              'contributes',
-              'scripts',
-              'peerDependencies',
-              'peerDependenciesMeta',
-              'dependencies',
-              'optionalDependencies',
-              'devDependencies',
-              'pnpm',
-              'overrides',
-              'resolutions',
-              'husky',
-              'simple-git-hooks',
-              'lint-staged',
-              'eslintConfig',
+              "publisher",
+              "name",
+              "displayName",
+              "type",
+              "version",
+              "private",
+              "packageManager",
+              "description",
+              "author",
+              "license",
+              "funding",
+              "homepage",
+              "repository",
+              "bugs",
+              "keywords",
+              "categories",
+              "sideEffects",
+              "exports",
+              "main",
+              "module",
+              "unpkg",
+              "jsdelivr",
+              "types",
+              "typesVersions",
+              "bin",
+              "icon",
+              "files",
+              "engines",
+              "activationEvents",
+              "contributes",
+              "scripts",
+              "peerDependencies",
+              "peerDependenciesMeta",
+              "dependencies",
+              "optionalDependencies",
+              "devDependencies",
+              "pnpm",
+              "overrides",
+              "resolutions",
+              "husky",
+              "simple-git-hooks",
+              "lint-staged",
+              "eslintConfig",
             ],
           },
           {
-            pathPattern: '^(?:dev|peer|optional|bundled)?[Dd]ependencies$',
-            order: { type: 'asc' },
+            pathPattern: "^(?:dev|peer|optional|bundled)?[Dd]ependencies$",
+            order: { type: "asc" },
           },
           {
-            pathPattern: '^exports.*$',
-            order: [
-              'types',
-              'require',
-              'import',
-            ],
+            pathPattern: "^exports.*$",
+            order: ["types", "require", "import"],
           },
         ],
       },
     },
     {
-      files: ['*.d.ts'],
+      files: ["*.d.ts"],
       rules: {
-        'import/no-duplicates': 'off',
+        "import/no-duplicates": "off",
       },
     },
     {
-      files: ['*.js', '*.cjs', '*.jsx'],
+      files: ["*.js", "*.cjs", "*.jsx"],
       rules: {
-        '@typescript-eslint/no-var-requires': 'off',
-        '@typescript-eslint/no-require-imports': 'off',
+        "@typescript-eslint/no-var-requires": "off",
+        "@typescript-eslint/no-require-imports": "off",
       },
     },
     {
-      files: ['*.ts', '*.tsx', '*.mts', '*.cts'],
+      files: ["*.ts", "*.tsx", "*.mts", "*.cts"],
       rules: {
-        'no-void': ['error', { allowAsStatement: true }],
+        "no-void": ["error", { allowAsStatement: true }],
       },
     },
     {
-      files: ['scripts/**/*.*', 'cli.*'],
+      files: ["scripts/**/*.*", "cli.*"],
       rules: {
-        'no-console': 'off',
+        "no-console": "off",
       },
     },
     {
-      files: ['*.test.ts', '*.test.js', '*.spec.ts', '*.spec.js'],
+      files: ["*.test.ts", "*.test.js", "*.spec.ts", "*.spec.js"],
       rules: {
-        'no-unused-expressions': 'off',
-        'no-only-tests/no-only-tests': 'error',
+        "no-unused-expressions": "off",
+        "no-only-tests/no-only-tests": "error",
       },
     },
     {
       // Code blocks in markdown file
-      files: ['**/*.md/*.*'],
+      files: ["**/*.md/*.*"],
       rules: {
-        '@typescript-eslint/no-redeclare': 'off',
-        '@typescript-eslint/no-unused-vars': 'off',
-        '@typescript-eslint/no-use-before-define': 'off',
-        '@typescript-eslint/no-var-requires': 'off',
-        '@typescript-eslint/comma-dangle': 'off',
-        '@typescript-eslint/consistent-type-imports': 'off',
-        '@typescript-eslint/no-namespace': 'off',
-        '@typescript-eslint/no-require-imports': 'off',
-        'import/no-unresolved': 'off',
-        'unused-imports/no-unused-imports': 'off',
-        'unused-imports/no-unused-vars': 'off',
-        'no-alert': 'off',
-        'no-console': 'off',
-        'no-restricted-imports': 'off',
-        'no-undef': 'off',
-        'no-unused-expressions': 'off',
-        'no-unused-vars': 'off',
+        "@typescript-eslint/no-redeclare": "off",
+        "@typescript-eslint/no-unused-vars": "off",
+        "@typescript-eslint/no-use-before-define": "off",
+        "@typescript-eslint/no-var-requires": "off",
+        "@typescript-eslint/comma-dangle": "off",
+        "@typescript-eslint/consistent-type-imports": "off",
+        "@typescript-eslint/no-namespace": "off",
+        "@typescript-eslint/no-require-imports": "off",
+        "import/no-unresolved": "off",
+        "unused-imports/no-unused-imports": "off",
+        "unused-imports/no-unused-vars": "off",
+        "no-alert": "off",
+        "no-console": "off",
+        "no-restricted-imports": "off",
+        "no-undef": "off",
+        "no-unused-expressions": "off",
+        "no-unused-vars": "off",
       },
     },
   ],
   rules: {
     // import
-    'import/order': 'error',
-    'import/first': 'error',
-    'import/no-mutable-exports': 'error',
-    'import/no-unresolved': 'off',
-    'import/no-absolute-path': 'off',
-    'import/newline-after-import': ['error', { count: 1, considerComments: true }],
+    "import/order": "error",
+    "import/first": "error",
+    "import/no-mutable-exports": "error",
+    "import/no-unresolved": "off",
+    "import/no-absolute-path": "off",
+    "import/newline-after-import": [
+      "error",
+      { count: 1, considerComments: true },
+    ],
 
     // Common
-    'semi': ['error', 'never'],
-    'curly': ['error', 'multi-or-nest', 'consistent'],
-    'quotes': ['error', 'single'],
-    'quote-props': ['error', 'consistent-as-needed'],
+    semi: ["error", "never"],
+    curly: ["error", "multi-or-nest", "consistent"],
+    quotes: ["error", "single"],
+    "quote-props": ["error", "consistent-as-needed"],
 
-    'unused-imports/no-unused-imports': 'error',
-    'unused-imports/no-unused-vars': [
-      'warn',
-      { vars: 'all', varsIgnorePattern: '^_', args: 'after-used', argsIgnorePattern: '^_' },
+    "unused-imports/no-unused-imports": "error",
+    "unused-imports/no-unused-vars": [
+      "warn",
+      {
+        vars: "all",
+        varsIgnorePattern: "^_",
+        args: "after-used",
+        argsIgnorePattern: "^_",
+      },
     ],
 
-    'no-param-reassign': 'off',
-    'array-bracket-spacing': ['error', 'never'],
-    'brace-style': ['error', 'stroustrup', { allowSingleLine: true }],
-    'block-spacing': ['error', 'always'],
-    'camelcase': 'off',
-    'comma-spacing': ['error', { before: false, after: true }],
-    'comma-style': ['error', 'last'],
-    'comma-dangle': ['error', 'always-multiline'],
-    'no-constant-condition': 'warn',
-    'no-debugger': 'error',
-    'no-console': ['error', { allow: ['warn', 'error'] }],
-    'no-cond-assign': ['error', 'always'],
-    'func-call-spacing': ['off', 'never'],
-    'key-spacing': ['error', { beforeColon: false, afterColon: true }],
-    'indent': ['error', 2, { SwitchCase: 1, VariableDeclarator: 1, outerIIFEBody: 1 }],
-    'no-restricted-syntax': [
-      'error',
-      'DebuggerStatement',
-      'LabeledStatement',
-      'WithStatement',
+    "no-param-reassign": "off",
+    "array-bracket-spacing": ["error", "never"],
+    "brace-style": ["error", "stroustrup", { allowSingleLine: true }],
+    "block-spacing": ["error", "always"],
+    camelcase: "off",
+    "comma-spacing": ["error", { before: false, after: true }],
+    "comma-style": ["error", "last"],
+    "comma-dangle": ["error", "always-multiline"],
+    "no-constant-condition": "warn",
+    "no-debugger": "error",
+    "no-console": ["error", { allow: ["warn", "error"] }],
+    "no-cond-assign": ["error", "always"],
+    "func-call-spacing": ["off", "never"],
+    "key-spacing": ["error", { beforeColon: false, afterColon: true }],
+    indent: [
+      "error",
+      2,
+      { SwitchCase: 1, VariableDeclarator: 1, outerIIFEBody: 1 },
     ],
-    'object-curly-spacing': ['error', 'always'],
-    'no-return-await': 'off',
-    'space-before-function-paren': [
-      'error',
+    "no-restricted-syntax": [
+      "error",
+      "DebuggerStatement",
+      "LabeledStatement",
+      "WithStatement",
+    ],
+    "object-curly-spacing": ["error", "always"],
+    "no-return-await": "off",
+    "space-before-function-paren": [
+      "error",
       {
-        anonymous: 'always',
-        named: 'never',
-        asyncArrow: 'always',
+        anonymous: "always",
+        named: "never",
+        asyncArrow: "always",
       },
     ],
 
     // es6
-    'no-var': 'error',
-    'prefer-const': [
-      'error',
+    "no-var": "error",
+    "prefer-const": [
+      "error",
       {
-        destructuring: 'all',
+        destructuring: "all",
         ignoreReadBeforeAssign: true,
       },
     ],
-    'prefer-arrow-callback': [
-      'error',
+    "prefer-arrow-callback": [
+      "error",
       {
         allowNamedFunctions: false,
         allowUnboundThis: true,
       },
     ],
-    'object-shorthand': [
-      'error',
-      'always',
+    "object-shorthand": [
+      "error",
+      "always",
       {
         ignoreConstructors: false,
         avoidQuotes: true,
       },
     ],
-    'prefer-exponentiation-operator': 'error',
-    'prefer-rest-params': 'error',
-    'prefer-spread': 'error',
-    'prefer-template': 'error',
-    'template-curly-spacing': 'error',
-    'arrow-parens': ['error', 'as-needed', { requireForBlockBody: true }],
-    'generator-star-spacing': 'off',
-    'spaced-comment': ['error', 'always', {
-      line: {
-        markers: ['/'],
-        exceptions: ['/', '#'],
-      },
-      block: {
-        markers: ['!'],
-        exceptions: ['*'],
-        balanced: true,
+    "prefer-exponentiation-operator": "error",
+    "prefer-rest-params": "error",
+    "prefer-spread": "error",
+    "prefer-template": "error",
+    "template-curly-spacing": "error",
+    "arrow-parens": ["error", "as-needed", { requireForBlockBody: true }],
+    "generator-star-spacing": "off",
+    "spaced-comment": [
+      "error",
+      "always",
+      {
+        line: {
+          markers: ["/"],
+          exceptions: ["/", "#"],
+        },
+        block: {
+          markers: ["!"],
+          exceptions: ["*"],
+          balanced: true,
+        },
       },
-    }],
+    ],
 
     // best-practice
-    'array-callback-return': 'error',
-    'block-scoped-var': 'error',
-    'consistent-return': 'off',
-    'complexity': ['off', 11],
-    'eqeqeq': ['error', 'smart'],
-    'no-alert': 'warn',
-    'no-case-declarations': 'error',
-    'no-multi-spaces': 'error',
-    'no-multi-str': 'error',
-    'no-with': 'error',
-    'no-void': 'error',
-    'no-useless-escape': 'off',
-    'no-invalid-this': 'error',
-    'vars-on-top': 'error',
-    'require-await': 'off',
-    'no-return-assign': 'off',
-    'operator-linebreak': ['error', 'before'],
-    'max-statements-per-line': ['error', { max: 1 }],
+    "array-callback-return": "error",
+    "block-scoped-var": "error",
+    "consistent-return": "off",
+    complexity: ["off", 11],
+    eqeqeq: ["error", "smart"],
+    "no-alert": "warn",
+    "no-case-declarations": "error",
+    "no-multi-spaces": "error",
+    "no-multi-str": "error",
+    "no-with": "error",
+    "no-void": "error",
+    "no-useless-escape": "off",
+    "no-invalid-this": "error",
+    "vars-on-top": "error",
+    "require-await": "off",
+    "no-return-assign": "off",
+    "operator-linebreak": ["error", "before"],
+    "max-statements-per-line": ["error", { max: 1 }],
 
     // node
     // 'n/prefer-global/process': ['error', 'never'], // Not sure if we need it as we are using `process.env.NODE_ENV` a lot in front-end.
-    'n/prefer-global/buffer': ['error', 'never'],
-    'n/no-callback-literal': 'off',
+    "n/prefer-global/buffer": ["error", "never"],
+    "n/no-callback-literal": "off",
 
     // unicorns
     // Pass error message when throwing errors
-    'unicorn/error-message': 'error',
+    "unicorn/error-message": "error",
     // Uppercase regex escapes
-    'unicorn/escape-case': 'error',
+    "unicorn/escape-case": "error",
     // Array.isArray instead of instanceof
-    'unicorn/no-instanceof-array': 'error',
+    "unicorn/no-instanceof-array": "error",
     // Prevent deprecated `new Buffer()`
-    'unicorn/no-new-buffer': 'error',
+    "unicorn/no-new-buffer": "error",
     // Keep regex literals safe!
-    'unicorn/no-unsafe-regex': 'off',
+    "unicorn/no-unsafe-regex": "off",
     // Lowercase number formatting for octal, hex, binary (0x1'error' instead of 0X1'error')
-    'unicorn/number-literal-case': 'error',
+    "unicorn/number-literal-case": "error",
     // includes over indexOf when checking for existence
-    'unicorn/prefer-includes': 'error',
+    "unicorn/prefer-includes": "error",
     // String methods startsWith/endsWith instead of more complicated stuff
-    'unicorn/prefer-string-starts-ends-with': 'error',
+    "unicorn/prefer-string-starts-ends-with": "error",
     // textContent instead of innerText
-    'unicorn/prefer-text-content': 'error',
+    "unicorn/prefer-text-content": "error",
     // Enforce throwing type error when throwing error while checking typeof
-    'unicorn/prefer-type-error': 'error',
+    "unicorn/prefer-type-error": "error",
     // Use new when throwing error
-    'unicorn/throw-new-error': 'error',
+    "unicorn/throw-new-error": "error",
     // Prefer using the node: protocol
-    'unicorn/prefer-node-protocol': 'error',
+    "unicorn/prefer-node-protocol": "error",
 
-    'no-use-before-define': ['error', { functions: false, classes: false, variables: true }],
-    'eslint-comments/disable-enable-pair': 'off',
-    'import/no-named-as-default-member': 'off',
-    'import/no-named-as-default': 'off',
-    'import/namespace': 'off',
+    "no-use-before-define": [
+      "error",
+      { functions: false, classes: false, variables: true },
+    ],
+    "eslint-comments/disable-enable-pair": "off",
+    "import/no-named-as-default-member": "off",
+    "import/no-named-as-default": "off",
+    "import/namespace": "off",
 
-    'sort-imports': [
-      'error',
+    "sort-imports": [
+      "error",
       {
         ignoreCase: false,
         ignoreDeclarationSort: true,
         ignoreMemberSort: false,
-        memberSyntaxSortOrder: ['none', 'all', 'multiple', 'single'],
+        memberSyntaxSortOrder: ["none", "all", "multiple", "single"],
         allowSeparatedGroups: false,
       },
     ],
 
     // yml
-    'yml/quotes': ['error', { prefer: 'single', avoidEscape: false }],
-    'yml/no-empty-document': 'off',
+    "yml/quotes": ["error", { prefer: "single", avoidEscape: false }],
+    "yml/no-empty-document": "off",
 
     // antfu
-    'antfu/if-newline': 'error',
-    'antfu/import-dedupe': 'error',
-    'antfu/top-level-function': 'error',
+    "antfu/if-newline": "error",
+    "antfu/import-dedupe": "error",
+    "antfu/top-level-function": "error",
     // 'antfu/prefer-inline-type-import': 'error',
   },
-}
+};
