import path from 'node:path'
import fs from 'node:fs'
import { createUnplugin } from 'unplugin'
import dotenv from 'dotenv'
import chalk from 'chalk'
import boxen from 'boxen'
import type { Options } from './types'

const realPath = path.resolve(process.cwd(), '../../')
const prefix = 'API_'
const privatePrefix = 'PRIVATE_'
const vitePrefix = 'VITE_'

const envPluginPackagePath = path.resolve(
  realPath,
  'shared/unplugins/env-plugin',
)

interface EnvConfig {
  [key: string]: string
}

interface AliasConfig {
  [key: string]: string
}

function printStartLog(envConfig: EnvConfig, aliasConfig: AliasConfig): void {
  const envMessage = Object.entries(envConfig)
    .map(([key, value]) => `\n${chalk.magenta(key)}: ${chalk.white(value)}`)
    .join('')

  const aliasMessage = Object.entries(aliasConfig)
    .map(([key, value]) => `\n${chalk.yellow(key)} -> ${chalk.white(value)}`)
    .join('')

  console.log(
    boxen(
      `${chalk.cyan('envs')}${envMessage}\n\n${chalk.cyan('alias')}${aliasMessage}`,
      {
        padding: { top: 0, bottom: 0, left: 1, right: 1 },
        margin: { top: 1, bottom: 1, left: 0, right: 0 },
        borderStyle: 'double',
        borderColor: 'green',
        title: 'env-plugin: API环境变量插件启用成功',
        titleAlignment: 'center',
        width: 110,
      },
    ),
  )
}

// 统一处理环境模式的函数
function normalizeMode(mode?: string): string {
  if (mode) {
    return mode === 'local' ? 'development' : mode
  } else {
    return process.env.NODE_ENV === 'local'
      ? 'development'
      : process.env.NODE_ENV || 'production'
  }
}

// 加载环境变量文件
function loadEnvFile(filePath: string): EnvConfig {
  try {
    return fs.existsSync(filePath)
      ? dotenv.parse(fs.readFileSync(filePath))
      : {}
  } catch (error) {
    console.error(`加载环境变量文件失败: ${filePath}`, error)
    return {}
  }
}

export const envPlugin = createUnplugin((options: Options = {}) => {
  const { mode = process.env.NODE_ENV } = options
  const getEnvVars = (): Record<string, string> => {
    // 获取环境变量
    const currentMode = normalizeMode(mode)
    const dir = envPluginPackagePath

    // 加载环境变量文件
    const envConfig = {
      ...loadEnvFile(
        path.resolve(dir, `.env${currentMode ? `.${currentMode}` : ''}`),
      ),
      ...loadEnvFile(path.resolve(dir, '.env')),
    }

    // 过滤并转换环境变量
    return Object.entries(envConfig).reduce<Record<string, string>>(
      (acc, [key, value]) => {
        if (
          key.startsWith(prefix) ||
          key.startsWith(privatePrefix) ||
          key.startsWith(vitePrefix)
        ) {
          acc[`process.env.${key}`] = JSON.stringify(value)
        }
        return acc
      },
      {},
    )
  }

  // 获取别名配置
  const getAlias = (): AliasConfig => {
    const globalPackages = {
      '@apis': 'shared/http-api/src/api',
      '@clients': 'shared/http-api/src/client',
    }

    const packagesPath = realPath

    return Object.entries(globalPackages).reduce<AliasConfig>(
      (acc, [pkg, pkgInfo]) => {
        acc[pkg] = path.resolve(packagesPath, pkgInfo).toString()
        return acc
      },
      {},
    )
  }

  return {
    name: 'unplugin-env',
    enforce: 'pre',
    vite: {
      config(config) {
        const envVars = getEnvVars()
        const alias = getAlias()

        printStartLog(envVars, alias)

        return {
          ...config,
          resolve: {
            ...config.resolve,
            alias: {
              ...config.resolve?.alias,
              ...alias,
            },
          },
          define: {
            ...config.define,
            ...envVars,
          },
        }
      },
    },
    rspack: async (compiler: any) => {
      const envVars = getEnvVars()
      const alias = getAlias()
      printStartLog(envVars, alias)

      // 设置 alias
      compiler.options.resolve = {
        ...compiler.options.resolve,
        alias: {
          ...compiler.options.resolve?.alias,
          ...alias,
        },
      }

      // 设置环境变量
      new compiler.rspack.DefinePlugin(envVars).apply(compiler)
      return compiler
    },
  }
})

export function getApiEnv(mode?: string): EnvConfig {
  const currentMode = normalizeMode(mode)
  const dir = envPluginPackagePath
  return loadEnvFile(path.resolve(dir, `.env.${currentMode}`))
}
