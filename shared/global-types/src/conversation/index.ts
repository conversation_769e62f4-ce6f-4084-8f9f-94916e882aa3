import type { LLMChannels } from '../model'

export interface ConversationData {
  conversation_id: string
  robot_avatar: string
  robot_color: string
  robot_icon: string
  robot_id: string
  robot_name: string
  robot_welcome_message: string
  run_type: string
  title: string
  uid: number
  update_time: string
  /** 神奇的字段名，实际是对话的最后记录时间 */
  latest_create_time: string
  config?: {
    model_config?: {
      model: string
      channel: LLMChannels
    }
  }
}

export interface GenerateTitleParams {
  question: string
  answer: string
  conversation_id: string
  is_main_model?: boolean
}

export type ChatActionType =
  | 'CONVERSATION'
  | 'SESSION_NEW'
  | 'AGENT_RULE_CHANGE'

export enum ChatbotRunType {
  MULTIPLE_MODELS = 'MULTIPLE_MODELS',
  AGENT_TESTING = 'AGENT_TESTING',
  AGENT = 'AGENT',
  CHATBOT = 'CHATBOT',
  DINGTALK = 'DINGTALK',
  FEISHU = 'FEISHU',
  WEWORK = 'WEWORK',
  CHAT_CLIENT = 'CHAT_CLIENT',
  CLIENT_SMART_TOOLS = 'CLIENT_SMART_TOOLS',
  AI_RESEARCH = 'AI_RESEARCH',
}

export interface CreateConversationPayload {
  title: string
  runType?: ChatbotRunType
  conversation_config?: {
    index: number
    title_suffix: string
    model_config: {
      model: string
      channel: string
      temperature?: number
      top_p?: number
      presence_penalty?: number
      frequency_penalty?: number
      prompt_plugins?: {
        time_prompt?: boolean
        length_prompt?: number
        welcome_prompt_switch?: boolean
        math_prompt_switch?: boolean
      }
    }
  }
}
