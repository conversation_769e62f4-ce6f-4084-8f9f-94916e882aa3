export type McpConfig = McpSSEConfig | McpStdioConfig | McpHttpStreamableConfig

export enum TagsType {
  AI = 'AI',
  SYNC = '同步',
  CUSTOM = '自定义',
}

export interface McpSSEConfig extends McpBaseConfig {
  type: 'sse'
  url: string
  headers?: string
}

export interface McpHttpStreamableConfig extends McpBaseConfig {
  type: 'streamableHttp'
  url: string
  headers?: string
}

export interface McpStdioConfig extends McpBaseConfig {
  type: 'command'
  command: string
  args: string
  env: string
}

export interface McpBaseConfig {
  code: string
  name: string
  auto_execute: boolean
  timeout?: number
  tags?: TagsType[]
  is_self_developed?: boolean
}

export interface McpTool {
  name: string
  description: string
  inputSchema: Record<string, unknown>
}
