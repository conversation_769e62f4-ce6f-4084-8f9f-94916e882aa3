import type { ReactNode, FC, CSSProperties } from 'react'
import type { AvatarProps } from '../avatar'
import type { FormConfig } from '../form'

/**
 * @deprecated 旧的Tool类型，已废弃
 * 使用chat/core 中提供的MessageSkill和MessageKnowledge类型
 */
export interface Tool {
  status: 'success' | 'error' | 'loading'
  icon: string
  color: string
  title: string
  id?: string
  data?: any
  type: 'icon' | 'emoji' | 'image'
  onClick?: () => void
}
export type TaskStatus = 'STARTED' | 'SUCCEEDED' | 'FAILED'
export interface Task {
  icon: string
  color: string
  title: string
  id: string
  timestamp: number
  status?: TaskStatus
}
export interface SuggestedPlugin {
  avatar: string
  title: string
  id: string
  onClick?: () => void
}
export interface MessageForm {
  flowId: string
  recordId?: string
  isSubmit: boolean
  formData?: Record<string, any>
  formWelcome: string
  formConfig: Array<FormConfig>
}

export type ActionType = 'edit' | 'copy' | 'regenerate' | 'like' | 'unlike'

// export interface ShortcutsConfig {}

export interface MessageFile {
  name: string
  type: string
  url?: string
  upload_file_id?: string
  byte_size?: number
}

export type MessageQuote = ChatMessageQuote & {
  icon: string
  url?: string
}

export interface MessageProps {
  id?: string
  feedback_id?: string
  responseExtend?: boolean
  messageType: 'text' | 'markdown' | 'fetching' | 'form' | 'task'
  position?: 'left' | 'right'
  className?: string
  footer?: ReactNode
  header?: ReactNode
  actions?: ActionType[]
  backgroundColor?: string
  extras?: ReactNode
  tools?: Tool[]
  task?: Task
  suggestedPlugins?: SuggestedPlugin[]
  pluginItemOnMessageRender?: (args: {
    avatar: string
    id: string
    title: string
  }) => JSX.Element
  form?: MessageForm
  cursor?: boolean
  sessionId?: string
  toolComponent?: FC<Tool>
  layout?: 'vertical' | 'horizontal'
  title?: string
  thinking_content?: string
  thinked_text?: string
  content?: string
  copilotMessageList?: Array<{
    title: string
    desc: string
    status?: 'success' | 'error'
  }>
  questionGuide?: string[]
  /**
   * message 右下角消息水印
   */
  watermark?: string
  file?: MessageFile
  quote?: MessageQuote
  file_ids?: string[]
  avatarInfo?: AvatarProps | null
  relatedQuestionInfo?: {
    loading: boolean
    questions: string[]
  }
  /**
   *  record 原数据
   */
  metaData?: Record<string, any>
  onHrefTagClick?: (url: string) => void
  onImageDownload?: (url: string) => void
  onActionClick?: (
    action: ActionType,
    content: string,
    file?: MessageProps['file'],
    id?: string,
  ) => void
  onCodeCopy?: (code: string) => void
  onToolClick?: (task: Tool) => void
  onTaskClick?: (task: Task, metaData?: Record<string, any>) => void
  onRegenerate?: () => void
  showRegenerateBtn?: boolean
  isWelcomeMessage?: boolean
  reportRender?: (children: ReactNode) => ReactNode
  dislikeOnMessageRender?: (options: {
    trigger: ReactNode
    record_id: string
    feedback_id?: string
  }) => JSX.Element
  /**
   * 是否隐藏操作按钮
   * @description 值为 true 时隐藏
   */
  hideActions?: boolean | { showOnHover: boolean }
  /**
   * 表单类型消息
   */
  onFormSubmit?: (
    flowId: string,
    recordId: string,
    value: Record<string, any>,
  ) => Promise<unknown>
  formDisabled?: boolean
  /**
   * 流式停止的原因
   */
  finish_reason?: ChatMessageItem['finish_reason']
}

export interface MessageItem extends MessageProps {
  id: string
  type: 'message' | 'separator'
}

export interface MessageListProps {
  data?: MessageItem[]
  // tools?: Tool[]
  toolComponent?: FC<Tool>
  className?: string
  style?: CSSProperties
  paddingX?: number
  onHrefTagClick?: MessageProps['onHrefTagClick']
  onLoadMore?: VoidFunction
  onMessageActionClick?: MessageProps['onActionClick']
  onMessageToolClick?: (task: Tool) => void
  onQuestionSend?: (data: string) => void
  onRelatedQuestionsToggle?: (enable: boolean) => void
}

export interface MessageHistoryParams {
  conversation_id: string
  create_time?: number
  page_number?: number
  page_size?: number
}

interface CopilotProgress {
  key: string
  name: string
  icon: string
  progress: number
  message: string
  status: 'SUCCEEDED' | 'FAILED'
  sub_progress?: CopilotProgress[]
}

export interface ChatApiMessageFile {
  file_name: string
  file_type: string
  file_url?: string
  file_data?: string
  byte_size: number
  upload_file_id?: string
}

export interface ChatMessageQuote {
  quote_id: string
  quote_type: 'TEXT' | 'URL'
  chat_client_tool_id?: string
  // 网页类型 ‘{content: xxx, title: xxx, url: xxx}’
  content: string
}

export interface ChatMessageItem {
  id: string
  feedback_id?: string
  create_time: string
  files?: ChatApiMessageFile[]
  upload_file_id?: string
  file_url?: string
  file_name: string
  file_type: string
  tips?: string[]
  quotes?: ChatMessageQuote[]
  is_push?: boolean
  record_status: 'SUCCEEDED' | 'FAILED'
  response: string
  session_id: string
  user_content: string
  finish_reason?: 'stop' | 'abort'
  response_ext: {
    content:
      | string
      | { input: Record<string, any>; output: string | Record<string, any> }
    duration_time: number
    status: string
    message: string
    timestamp: number
    tasks: ChatStream[]
    group_progress_list?: CopilotProgress[]
    llm_model?: string
    irl_time?: number
  }
}

export interface PaginatedMessageHistory {
  data_list: ChatMessageItem[]
  page_number: number
  page_size: number
  total_count: number
  total_pages: number
}

export interface UnreadMessageCountMap {
  robot: Record<string, number>
  conversation: Record<string, number>
}

export interface DefaultChatStream {
  duration_time: number
  status: string
  message: string
  timestamp: number
  name: string
  tool_id?: string
  tool_type?: string // 新数据返回tool_type
  function_type?: string // 旧数据返回function_type
  function_code?: string
  execution_status?: {
    message?: string
  }
  metadata?: {
    color: string
    icon: string
    inputs_properties?: {
      flowId: string
      formConfig: MessageForm['formConfig']
      inputsViewMode: 'form' | 'raw'
      formWelcome: string
    }
  }
}

export interface LLMChatStream extends DefaultChatStream {
  content: string
  type: 'TEXT'
}

export interface FunctionCallChatStream extends DefaultChatStream {
  content: {
    input: Record<string, any>
    output: string | Record<string, any>
    flow_msgs?: Array<{
      message: string
      message_type: 'TEXT'
    }>
    form_submitted?: {
      status: 'SUBMITTED'
      inputs: Record<string, any>
    }
  }
  type: 'FUNCTION'
}

export interface ChatRecordChatStream extends DefaultChatStream {
  type: 'CHAT_RECORD'
  name: 'record_id'
  content: string
}

export interface RelatedQuestionChatStream extends DefaultChatStream {
  content: {
    related_questions?: string[]
  }
  type: 'RELATED_QUESTIONS'
}

export interface TaskChatStream extends DefaultChatStream {
  status: TaskStatus
  content: {
    conversation_id: string
    task_id: string
  }
  type: 'TASK'
}

export interface ThinkChatStream extends DefaultChatStream {
  type: 'THINKING'
  content: string
}

export interface SuggestedPluginChatStream extends DefaultChatStream {
  content: {
    function_id: string
    display_name: string
    avatar: string
  }[]
  type: 'SUGGESTED_PLUGIN'
}

// 敏感话题的LLM输出
export interface LLMSensitiveOutput extends DefaultChatStream {
  type: 'LLMSensitiveOutput'
  content: string
}

/**
 * 流式中的业务异常，比如积分耗尽等等
 */
export interface ResultChatStream {
  type: 'RESULT'
  message?: string
  status?: string
  execution_status?: {
    message?: string
  }
}

export type ChatStream =
  | LLMChatStream
  | FunctionCallChatStream
  | RelatedQuestionChatStream
  | ChatRecordChatStream
  | TaskChatStream
  | ThinkChatStream
  | LLMSensitiveOutput
  | SuggestedPluginChatStream
  | ResultChatStream
