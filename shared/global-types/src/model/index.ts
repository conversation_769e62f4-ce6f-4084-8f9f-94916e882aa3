export enum LLMChannels {
  GPT = 'GPT',
  TongYiQWen = 'TongYiQWen',
  ZhiPu = 'ZhiPu',
  QianFan = 'QianFan',
  XunFei = 'XunFei',
  Tencent = 'Tencent',
  Amazon = 'Amazon',
  Azure = 'Azure',
  ChatGLM3 = 'ChatGLM3',
  Moonshot = 'Moonshot',
  Volcano = 'Volcano',
  Amazon_Llama = 'Amazon_Llama',
  DeepSeek = 'DeepSeek',
  BTY = 'BTY',
}

export interface ModelFeature {
  ability: string[]
  field_support: string[]
  icon: string
  speed: number
  stability: number
  suggest: boolean
  length: string
  think_content?: boolean
  param_config?: {
    default?: number
    max?: number
    min?: number
  }
}
export interface LLMModalItem {
  id: number
  name: string
  code: LLMChannels // model的channel，用于区分模型服务商
  methodSupport: 'tools' | 'functions' | 'normal'
  model: string // 模型key
  description: string | null
  createAt: string
  updateAt: string
  enable: boolean // 是否启用
  grayTest: boolean // 是否灰度测试
  maxTokens: number
  feature: ModelFeature
  type: string
}

export interface LLMModelLimitedItem {
  model: string
  used: boolean
  limited: boolean // 是否限制
  consume: number // 本月使用次数
  limit: number // 每月限制次数
  trial_available: boolean // 是否可用
  llm_categories: string
  llm_code: string
  llm_commercial_used: boolean
  llm_consume_points: number
  llm_enabled: boolean
  llm_channel: string
}

export interface BaseUniqueItem {
  model_unique_key?: string
}

export enum ExtendedModelType {
  Think_Model_Function = 'think_model_function',
}
