import type { AuthAppDingTalk, AuthAppWeChat } from '../agent-go'
import type { LLMChannels } from '../model'
import type { PromptStructItem, PromptType } from '../prompt'
import type { AgentConfigShortcut } from '../shortcuts'
import type { TaskDetail } from '../task'

export interface VariableConfig {
  label: string
  variable_name: string
  required: boolean
  placeholder: string
  type: 'single_text' | 'single_select' | 'multi_select'
  options?: { label: string; value: string }[]
}

export interface FormConfig {
  default_value: unknown
  description: string
  field_type: string
  key: string
  required: boolean
}

export interface BaseBotItem {
  value: string | number
  label: string
  icon?: string
  color?: string
  description?: string
  group: string
  has_sub_bot?: boolean
}

export interface UtilityFormConfig {
  component: string
  default: string
  description: string
  example: string
  field_type: string
  key: string
  name: string
  required: true
  options?: { label: string; value: string }[]
}

export interface PropertyParameter {
  type: string | { type: string }
  description: string | { description: string }
  name: string
  default: any
  example: any
  component?: string
  options?: { label: string; value: string }[]
}

export interface AgentSystemSkill {
  id: number
  function_id: string
  display_name: string
  description: string
  metadata_config: {
    icon: string
    color: string
  }
  is_delete: boolean
  created_at: string
  updated_at: string
  name: string
  parameters: {
    type: string
    inputs?: UtilityFormConfig[]
    properties?: Record<string, PropertyParameter>
    required?: string[]
  } // 暂时没用到该字段，需要用时自行补全类型
  type: 'system'
}

export interface FLowFormConfig {
  description: string
  label: string
  placeholder: string
  type: string
  required: boolean
  variableName: string
}

export interface AgentFlowConfig {
  flow_id: string
  color: string
  icon: string
  name: string
  description: string
  // inputs: any[]
  rel_flow_form_config: FLowFormConfig[]
}

// ====== for apis ======

/**
 * /agent/detail
 */
export interface AgentDetailTable {
  id: string
  is_enable: boolean
  table_id: string
  title: string
  type: 'database'
}

export interface AgentDetailDatabase {
  icon?: string
  icon_color?: string
  database_id: string
  database_name: string
  source_id: string
  table_list?: AgentDetailTable[]
  group_id?: string
}

export type AgentRelFlowStatus = 'DELETED' | 'PUBLISHED' | 'DRAFT'

export const FieldTypes = {
  Input: 'input',
  Textarea: 'textarea',
  Select: 'select',
  MultiSelect: 'multiSelect',
  Json: 'json',
  File: 'file',
  Slider: 'slider',
  Switch: 'switch',
  Radio: 'radio',
  MultiInput: 'multiInput',
  ModelSelect: 'modelSelect',
} as const

export type FiledType = (typeof FieldTypes)[keyof typeof FieldTypes]

export enum SelectViewMode {
  SELECT = 'select',
  Flat = 'flat',
}

export interface FieldItem {
  label: string
  type: FiledType
  variableName: string
  required: boolean
  placeholder?: string
  disabled?: boolean
  options?: {
    label: string
    value: string
  }[]
  viewMode?: SelectViewMode
  className?: string
}

enum SkillFlowViewMode {
  RAW = 'raw',
  FORM = 'form',
}

export interface AgentFlowSkill {
  app_id: string
  description: string
  flow_description: string
  flow_id: string
  function_id: string
  tool_id?: string
  icon: string
  color: string
  id: string
  inputs: AgentSkillBaseRequest['inputs']
  is_enable: boolean
  name: string
  outputs: AgentSkillBaseRequest['outputs']
  type: 'flow'
  agent_rel_flow_status: AgentRelFlowStatus
  flow_enabled: boolean
  rel_flow_form_config: FieldItem[]
  env?: string
  active_multi_env?: boolean
  app_group_id?: string
  inputs_properties?: {
    inputsViewMode: SkillFlowViewMode
    formWelcome: string
  }
}

export interface AgentMultiAgentSkill {
  app_id: string
  description: string
  flow_description: string
  flow_id: string
  function_id: string
  tool_id?: string
  icon: string
  color: string
  id: string
  inputs: AgentSkillBaseRequest['inputs']
  is_enable: boolean
  name: string
  outputs: AgentSkillBaseRequest['outputs']
  type: 'agent'
  agent_rel_flow_status: AgentRelFlowStatus
  flow_enabled: boolean
  rel_flow_form_config: FieldItem[]
  env?: string
  active_multi_env?: boolean
  app_group_id?: string
  has_sub_bot?: boolean
}

export interface AgentVariableConfig {
  label: string
  variable_name: string
  type: 'single_text'
  required: boolean
  placeholder: string
}

export interface AgentRuleConfig {
  channel: LLMChannels
  promptType?: PromptType
  structPrompt?: PromptStructItem[]
  messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }>
  model: string
  stream: boolean
  temperature: number
  welcome_enabled?: boolean
  welcome: string
  rule_id: string
  question_guide: string[]
  prompt_plugins: {
    length_prompt?: number
    time_prompt?: boolean
    math_prompt_switch?: boolean
    welcome_prompt_switch?: boolean
    multiple_model?: boolean
    [key: string]: any
  }
  agent_variables: AgentVariableConfig[]
  related_questions_enabled: boolean
  related_questions_obj?: {
    related_questions_type?: 'AI' | 'CUSTOM_PROMPT' | 'CLOSE'
    custom_related_questions_prompt?: string
  }
  not_show_function_call?: boolean
  is_show_think?: boolean
  dataset_global_config?: {
    description: string
    show_knowledge_url: boolean
  }
  enforce_execution?: {
    type?: 'CLOSE' | 'FLOW' | 'DATASET' | 'SUB_AGENT'
    tool_id?: string
  }
  preset_tasks_enabled: boolean
  preset_tasks: TaskDetail[]
  shortcuts: AgentConfigShortcut[]
  free_model?: boolean
}

export interface AgentSkillBaseRequest {
  flow_id: string // 当前Agent的FlowId
  rel_flow_id: string // 选中的技能的flow_id
  version_id?: string
  type: 'flow' | 'agent'
  app_id: string
  inputs?: Array<{
    key: string
    description: string
    default?: string
    required: boolean
    field_type: string
  }>
  output_mode?: string
  outputs: Array<{
    key: string
    value: string
  }>
  description: string
}

export type AgentPluginInputComponent =
  | {
      component: 'slider'
      display_name: string
      limit: { min: number; max: number }
      type: 'int'
      value: number
      required?: boolean
    }
  | {
      component: 'switch'
      display_name: string
      type: 'bool'
      value: boolean
      required?: boolean
      description?: string
    }
  | {
      component: 'card'
      display_name: string
      type: 'string'
      options: {
        label: string
        value: string
        description: string
        consume_points: string
      }[]
      value: string
      required?: boolean
    }
  | {
      component: 'select'
      display_name: string
      options: unknown[]
      required: boolean
      type: 'string'
      value: string
      llm_model?: boolean
    }
  | {
      component: 'input'
      display_name: string
      placeholder: string
      required?: boolean
      type: 'string'
      value?: string | null
    }
  | {
      component: 'formList'
      addBtnText: string
      defaultValue: unknown[]
      items: unknown[]
      required?: boolean
      value?: unknown
      limit: {
        min: number
        max: number
      }
    }

export interface AgentPluginOutputComponent {
  component: 'select'
  optional: {
    key: string
    description: string
  }[]
  value: string
}

export interface AgentPluginProperties {
  inputs?: Record<string, AgentPluginInputComponent>
  outputs?: Record<string, AgentPluginOutputComponent>
}

interface InputParameter {
  // 参数变量名称
  key: string
  // 参数中文名称
  name: string
  description: string | { description: string }
  field_type: string | { type: string }
  required: boolean
  default: any
  example: any
  component?: string
}

export interface ParametersWithInput {
  inputs: InputParameter[]
  output: unknown // 暂时没用到该字段，需要用时自行补全类型
}

export interface ParametersWithProperty {
  type: string
  required: string[]
  properties: Record<string, PropertyParameter>
}

export interface AgentPluginSkill {
  type: 'system' | 'custom_function'
  id: number
  plugin_type: 'system' | 'API' | 'MCP'
  function_id: string
  tool_id?: string
  name: string
  display_name: string
  description: string
  metadata_config?: {
    icon?: string
    color?: string
    properties?: AgentPluginProperties
  }
  parameters: ParametersWithInput | ParametersWithProperty
  is_new: boolean
  is_deleted: boolean
  created_at: string
  updated_at: string
  consume_mode: string
}

export type AgentListSystemSkillItem = Pick<
  AgentPluginSkill,
  | 'description'
  | 'function_id'
  | 'display_name'
  | 'parameters'
  | 'type'
  | 'plugin_type'
  | 'tool_id'
> & {
  name: string
  description: string
  metadata: AgentPluginSkill['metadata_config']
  is_enable: boolean
  labels: string[]
}

export interface AgentDetailResponse {
  appId: string
  developer: string
  workspace_id: string
  workspace_name: string
  isTemplate: boolean
  application?: {
    id: string
    appId: string
    flowId: string
    color?: string
    name?: string
    icon?: string
    description?: string
    draftVersionId?: string
    applicationType: string
    createdBy: string
    activeMultiEnv: boolean
    appGroupId: string
    appLabels: unknown
    copilotConversationId: string
    copilotSessionId: string
    createdAt: string
    dingtalkCardTemplateId: string
    env: 'TEST' | 'PROD' | 'DEV'
    envAppId: string
    envVar: unknown[]
    flowLock: number
    flowName: string
    isBot: boolean
    isDeleted: boolean
    isEnable: boolean
    isPublic: boolean
    isTemplate: boolean
    labels?: unknown
    lastEditedAt: string
    modifiedBy: string
    popularity: number
    recommendedSorting: number
    updatedAt: string
    workspaceId: string
    workspaceName: string
  }
  config: {
    agents: {
      app_id: string
      description: string
      flow_id: string
      id: string
      is_enable: boolean
      type: string
    }[]
    utility: AgentListSystemSkillItem[]
    rule?: {
      question_guide?: string[]
      agent_variables: VariableConfig[]
      welcome_enabled?: boolean
      shortcuts: AgentConfigShortcut[]
      preset_tasks_enabled?: boolean
      welcome?: string
      free_model?: boolean
    }
    flows: AgentFlowConfig[]
    [key: string]: any
  }
  database?: AgentDetailDatabase
  dataset?: { file_id: string; file_name: string; mimetype: string }[]
  flows?: AgentFlowSkill[]
  rule?: AgentRuleConfig
  versionId: string
  utility?: Array<AgentListSystemSkillItem>
  agents?: AgentMultiAgentSkill[]
  agent_auth_app: Array<AuthAppWeChat | AuthAppDingTalk>
}

export interface UploadFileSupportTypes {
  [key: string]: any
  name: string
  type: string
  size: number
  support: string[]
  extension: string[]
}
