{"name": "@bty/global-types", "version": "0.0.2", "private": true, "sideEffects": false, "exports": {"./agent": {"import": "./src/agent/index.ts"}, "./agent-go": {"import": "./src/agent-go/index.ts"}, "./avatar": {"import": "./src/avatar/index.ts"}, "./bot": {"import": "./src/bot/index.ts"}, "./conversation": {"import": "./src/conversation/index.ts"}, "./features": {"import": "./src/features/index.ts"}, "./file": {"import": "./src/file/index.ts"}, "./form": {"import": "./src/form/index.ts"}, "./message": {"import": "./src/message/index.ts"}, "./model": {"import": "./src/model/index.ts"}, "./prompt": {"import": "./src/prompt/index.ts"}, "./shortcuts": {"import": "./src/shortcuts/index.ts"}, "./task": {"import": "./src/task/index.ts"}, "./knowledge": {"import": "./src/knowledge/index.ts"}, "./mcp": {"import": "./src/mcp/index.ts"}, "./version": {"import": "./src/version/index.ts"}}, "devDependencies": {"@types/react": "catalog:"}}