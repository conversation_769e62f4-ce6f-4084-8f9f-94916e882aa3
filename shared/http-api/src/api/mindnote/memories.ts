import { subscribeRequest } from '../../client'

export interface Memory {
  id: string
  name: string
  describe: string
  type?: 'handwrite'
  status?: 'success'
  created_at: string
  task_structure?: TaskOfMemory[]
}

export interface TaskOfMemory {
  step: number
  name: string
  experience: string
}

interface MemoryPage {
  data: Memory[]
  page_no: number
  page_size: number
  total: number
}

export function getMemories(no = 1, size = 10) {
  return subscribeRequest.get<MemoryPage>('/v1/experience', {
    query: { page_no: no, page_size: size },
  })
}

export interface CreateMemoryPayload {
  name: string
  task_structure: TaskOfMemory[]
}

export function createMemory(data: CreateMemoryPayload) {
  return subscribeRequest.put<{ id: string }>('/v1/handwrite', data)
}

export function getMemory(id: string) {
  return subscribeRequest.get<Memory>(`/v1/experience/${id}`)
}

export function updateMemory(id: string, data: Partial<CreateMemoryPayload>) {
  return subscribeRequest.post<{ id: string }>(`/v1/experience/${id}`, data)
}

export function deleteMemory(id: string) {
  return subscribeRequest.delete(`/v1/experience/${id}`)
}
