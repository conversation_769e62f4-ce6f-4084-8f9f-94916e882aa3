import { subscribeRequest } from '../../client'
import type { TaskArtifact } from './next-agent-chat.type'

export function getNextAgentConversationListByBizId(biz_id: string) {
  return subscribeRequest.post('/v1/super_agent/conversations/biz_id', {
    biz_id,
  })
}

export function createNextAgentConversation(title: string) {
  return subscribeRequest.post('/v1/super_agent/chat/create_conversation', {
    title,
  })
}

export function updateNextAgentConversation(
  id: string,
  payload: {
    title?: string
    conversation_config?: Record<string, any>
  },
) {
  return subscribeRequest.post('/v1/super_agent/chat/update_conversation', {
    conversation_id: id,
    ...payload,
  })
}

export function generateNextAgentConversationTitle(
  conversation_id: string,
  question: string,
) {
  return subscribeRequest.post('/v1/super_agent/chat/generate_title', {
    conversation_id,
    question,
  })
}

export function deleteNextAgentConversation(conversation_id: string) {
  return subscribeRequest.delete(
    `/v1/super_agent/chat/deleted_conversation/${conversation_id}`,
  )
}

export function getNextAgentMessageList(
  conversation_id: string,
  page_no: number,
  page_size: number,
) {
  return subscribeRequest.post('/v1/super_agent/chat/event_list', {
    conversation_id,
    page_no,
    page_size,
  })
}

export function getNextAgentConversationList(
  page_no: number,
  page_size: number,
) {
  return subscribeRequest.post('/v1/super_agent/chat/conversation_list', {
    page_no,
    page_size,
  })
}

export function getFileUrl(taskId: string, path: string): Promise<string> {
  return subscribeRequest.post('/v1/super_agent/chat/oss_url', {
    task_id: taskId,
    file_path: path,
  })
}

export function getArtifacts(task_id: string): Promise<TaskArtifact[]> {
  return subscribeRequest.post('/v1/super_agent/chat/get_dir_file', {
    task_id,
  })
}

// 轮询获取对话任务信息
export function pollConversationIds(conversation_ids: string[]) {
  return subscribeRequest.post(
    '/v1/super_agent/chat/get_conversation_info_list',
    {
      conversation_ids,
    },
  )
}
