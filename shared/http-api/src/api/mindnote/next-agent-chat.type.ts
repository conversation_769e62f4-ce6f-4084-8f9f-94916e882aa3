export enum TaskStatus {
  NOT_STARTED = 'not_started', // 未开始
  IN_PROGRESS = 'in_progress', // 进行中
  PAUSE = 'pause', // 暂停
  COMPLETED = 'completed', // 已完成
  FAILED = 'failed', // 失败
  CANCELED = 'canceled', // 取消
  ConnectFail = 'connect_fail', // 连接失败
}

export enum EventStatus {
  RUNNING = 'running',
  COMPLETED = 'completed',
  SUCCESS = 'success',
  FAILED = 'failed',
}

export interface Conversation {
  conversation_config: Record<string, any>
  conversation_id: string
  created_at: string
  title: string
  has_messages: boolean
  status?: TaskStatus
  task_state?: TaskStatus
}

export enum EventType {
  USER_INPUT = 'user_input',
  PLAN_PREPARE_TITLE = 'plan_prepare_title',
  REPLY = 'reply',
  PLAN_ANALYZE = 'plan_analyze',
  READ_RECORDING = 'read_recording',
  TOOL_CALL = 'tool_call',
  TOOL_CALL_RESULT = 'tool_call_result',
  STEP_TITLE = 'step_title',
  THINK = 'think',
  TEXT = 'text',
  ASK_USER = 'ask_user',
  STEP_SUMMARY = 'step_summary',
  SUMMARY = 'summary',
}

export interface TaskArtifact {
  path: string
  file_name: string
  file_type: string
  isSummary?: boolean
}

export interface Attachment {
  file_type?: string
  file_name?: string
  file_url?: string
  isSummary?: boolean
}

export interface Operation {
  operation_type?: string
  content?: string
}

export const EventActionType = {
  AskUser: 'ask_user',
  QuestionInput: 'question_input',
  StepSummary: 'step_summary',
  Summary: 'summary',
  TextChunk: 'text_chunk',
  TextFull: 'text_full',
} as const

export type EventActionTypeValue =
  (typeof EventActionType)[keyof typeof EventActionType]

export const ToolType = {
  WebSearch: 'web_search',
  LocalSearch: 'local_search',
  BrowserUse: 'browser_use',
  WebFetch: 'web_fetch',
  TerminalOperator: 'terminal_operator',
  CreateFile: 'create_file',
  ReadFile: 'read_file',
  StrReplace: 'str_replace',
  WriteFile: 'write_file',
  MCP: 'mcp',
  BrowserUseTakeover: 'browser_use_takeover', // 浏览使用接管
  TakeOver: 'take_over', // 接管任务
  TakeOverExit: 'take_over_exit', // 接管任务退出
  JumpOver: 'jump_over', // 跳过任务
} as const

export type ToolTypeValue = (typeof ToolType)[keyof typeof ToolType]

export type CommonSearchOutput = ReadonlyArray<{
  title: string
  url: string
}>

export interface FileOperatorInput {
  file_type?: string
  file_path?: string
  file_content?: string
  new_str?: string
}

export interface FileOperatorOutput {
  path?: string
  content?: string
  file_type?: string
  file_name?: string
}

export interface BrowserUseOutput {
  clean_screenshot_path?: string
  clean_screenshot_uploaded?: boolean
}

export interface EventContent {
  action_type: EventActionTypeValue
  action_name?: string
  content?: string
  arguments?: string[]
  tool_name?: string
  tool_input?: string
  tool_output?:
    | CommonSearchOutput
    | FileOperatorOutput
    | BrowserUseOutput
    | string[]
    | string
  metadata?: Record<string, any>
  timestamp: string
  tool_call_id?: string
  attachment_files?: Array<Record<string, any> | FileOperatorOutput>
}

export interface Event {
  event_id: string
  plan_id: string | null
  plan_step_id: string | null
  plan_step_title: string | null
  plan_step_state: TaskStatus
  event_type: EventType
  content: EventContent
  timestamp: string
  event_status: EventStatus
  metadata?: Record<string, any>
  task_status?: TaskStatus
}
