import { subscribeRequest } from '../../client'
import type { SandboxInfo } from './sandbox.type'

export function sendSandboxHeart(task_id: string) {
  return subscribeRequest.post('/v1/super_agent/chat/sandbox_heart', {
    task_id,
  })
}

export function getSandboxInfo(task_id: string) {
  return subscribeRequest.post<SandboxInfo>(
    '/v1/super_agent/chat/sandbox_info',
    {
      task_id,
    },
  )
}
