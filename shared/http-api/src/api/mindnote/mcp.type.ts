import type { McpConfig, TagsType } from '@bty/global-types/mcp'

export interface McpPlugin {
  id: number
  function_id: string
  name: string
  plugin_type: 'MCP'
  source: 'CLIENT' | 'CLOUD'
  code?: string
  icon?: string
  color?: string
  description?: string
  is_auto_executed?: boolean
  config?: McpConfig
  system_function_code?: string
  is_enable?: boolean
  tags?: TagsType[]
  timeout?: number
}

export interface McpTemplate {
  code: string
  color: string
  is_self_developed: boolean
  config: {
    args?: string[]
    command?: string
    env?: Record<string, string>
    url?: string
  }
  description: string
  is_auto_executed: boolean
  is_key_required: boolean
  name: string
  system_function_code: string
}

export interface MCPSearchType {
  content: string
  search_tasks: {
    keyword: string
    keyword_en: string
    reason: string
    function: string
  }[]
}
