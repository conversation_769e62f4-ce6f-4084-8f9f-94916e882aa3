import type { Mcp<PERSON>ool } from '@bty/global-types/mcp'
import { subscribeRequest } from '../../client'
import type { McpPlugin, McpTemplate, MCPSearchType } from './mcp.type'

export function createMcpPlugin(
  payload: Omit<McpPlugin, 'id' | 'function_id'>,
) {
  return subscribeRequest.post('/v1/completion_function', payload)
}

export function batchCreateMcpPlugin(payload: { functions: any }) {
  return subscribeRequest.post('/v1/completion_function/batch', payload)
}

export function getMcpPluginList(source?: 'CLIENT' | 'CLOUD') {
  return subscribeRequest.get<McpPlugin[]>('/v1/completion_function/list', {
    query: {
      source,
    },
    noRedirect: true,
  })
}

export function deleteMcpPlugin(function_id: string) {
  return subscribeRequest.delete(`/v1/completion_function/${function_id}`)
}

export function updateMcpPlugin(
  function_id: string,
  payload: Partial<McpPlugin>,
) {
  return subscribeRequest.put(`/v1/completion_function/${function_id}`, payload)
}

export function getServerMcpTools() {
  return subscribeRequest.get<McpTool[]>(
    '/v1/completion_function/client/mcp/tools_config',
  )
}

export function setServerMcpTools(tools: Array<Omit<McpTool, 'inputSchema'>>) {
  return subscribeRequest.post(
    '/v1/completion_function/client/mcp/tools_config',
    {
      tools,
    },
  )
}

export function getMcpTemplateList(source: 'CLIENT' | 'CLOUD') {
  return subscribeRequest.get<McpTemplate[]>('/v1/mcp/template', {
    query: {
      source,
    },
  })
}

export function getConfigFromUrl(url: string) {
  return subscribeRequest.post<{
    mcpServers: Record<
      string,
      {
        command?: string
        url?: string
        args?: string[]
        env?: Record<string, unknown>
      }
    >
  }>('/v1/mcp/url_to_config', {
    url,
  })
}

export function generateMcpDescription(
  function_name: string,
  tools: McpTool[],
) {
  return subscribeRequest.post<{
    introduction: string
    tools: Array<{ title: string; content: string }>
    examples: Array<{ title: string; content: string }>
  }>('/v1/completion_function/mcp/generate_desc', {
    function_name,
    tools,
  })
}

export function generateMCPCode(name: string, source: 'CLIENT' | 'CLOUD') {
  return subscribeRequest.post('/v1/completion_function/generate_code', {
    name,
    source,
  })
}

export function getMCPSyncConfig() {
  return subscribeRequest.get('/v1/mcp/sync_config')
}

export function saveAndSyncMCPConfig(config: any) {
  return subscribeRequest.post('/v1/mcp/sync_config/saveAndSync', config)
}

export function getMCPAnalyse(content: string) {
  return subscribeRequest.post('/v1/mcp/analyse', {
    content,
  })
}

export function getMCPSearch(config: MCPSearchType) {
  return subscribeRequest.post('/v1/mcp/search', config)
}
export function syncMCPTools(
  toolsMcp: Record<string, Array<Omit<McpTool, 'inputSchema'>>>,
) {
  return subscribeRequest.post(
    '/v1/completion_function/use_config/cacheTools',
    {
      function_tools: toolsMcp,
    },
  )
}

export function setMcpEnablesBySession(
  id: string,
  scene: string,
  source: 'CLIENT' | 'CLOUD',
  enabledMcpIds: string[],
) {
  return subscribeRequest.post(
    '/v1/completion_function/use_config/saveOrUpdate',
    {
      biz_id: id,
      scene,
      source,
      enable_function: enabledMcpIds,
    },
  )
}

export function getMcpEnablesBySession(
  id: string,
  scene: string,
  source: 'CLIENT' | 'CLOUD',
) {
  return subscribeRequest.post<{
    enable_function: string[]
  }>('/v1/completion_function/use_config', {
    biz_id: id,
    source,
    scene,
  })
}
