export enum DocumentType {
  FILE = 1,
  TEXT = 2,
  WEBPAGE = 3,
  TEMPLATE_FILE = 4,
  QA = 5,
  VIDEO = 6,
  FEISHU = 7,
  DING = 8,
}

export enum DocumentSplitType {
  CUSTOM = -1, // 自定义分段
  AI = 0,
  Text = 1,
  DOCUMENT = 2,
  PAGE = 3,
  JSON = 4,
  CUSTOM_FAQ = 5,
  NO_SPLIT = 6,
}

export enum DocumentParseType {
  GENERAL_PARSE = 1, // 通用文档解析
  VISUAL_PARSE = 2, // 视觉布局解析
  AI_PARSING = 3, // AI解析
  OCR_PARSING = 4, // ocr解析
}

export enum DocumentStatus {
  Wait = 0,
  Ing = 1,
  Done = 2,
  Fail = 3,
  Warning = 4,
  AIProcess = 5,
}

export enum ParagraphExtraInfoStatus {
  Error = 'error',
  Done = 'done',
  Process = 'processing',
  Pending = 'pending',
  Warning = 'warning', // 前端逻辑
}

export enum ExtraParagraphExtraInfoStatus {
  All = 0,
}

export enum ProgressStage {
  PENDING = 0, // 待处理
  PREPROCESS_FILE = 1, // 文件预处理
  EXTRACT_FILE = 2, // 文件解析
  CHUNKING_FILE = 3, // 文件分段
  VECTORIZE_FILE = 4, // 文件向量化
}

export const OutputTypes = {
  JSON: 'json',
  TEXT: 'text',
} as const

export enum WebpageParseType {
  NORMAL = 'NORMAL',
  TURBO = 'TURBO',
}
