<script setup lang="ts">
import { SkeletonInput, Button, message } from 'ant-design-vue'
import { storeToRefs } from 'pinia'
import { saveAs } from 'file-saver'
import * as XLSX from 'xlsx'
import { useViewsStore } from '../store/views'
import FieldsMenu from './toolbar/FieldsMenu.vue'
import ColumnFilterMenu from './toolbar/ColumnFilterMenu.vue'
import SortListMenu from './toolbar/SortListMenu.vue'
import SearchData from './toolbar/SearchData.vue'
import { inject, nextTick, ref } from 'vue'
import { EventsInj, PermissionInj, ToolbarInj } from '../context'
import { Row } from '../lib'
import { RequestParams, TableType, ViewType } from '../nocodb-sdk'
import AddIcon from '../assets/add.svg?raw'
import ImportIcon from '../assets/import.svg?raw'
import DownloadIcon from '../assets/export.svg?raw'
import ClearIcon from '../assets/clear.svg?raw'
import { Modal } from 'ant-design-vue'
import { extractSdkResponseErrorMsg } from '../utils'
import { useApi, useSmartsheetStoreOrThrow } from '../composables'
import { useLocalize } from '../hooks/useLocalize'
import dayjs from 'dayjs'
const localize = useLocalize()

enum ExportTypes {
  EXCEL = 'excel',
  CSV = 'csv'
}

const { sorts, nestedFilters } = useSmartsheetStoreOrThrow()

const { isViewsLoading } = storeToRefs(useViewsStore())

const { api: $api } = useApi()

const toolbar = inject(ToolbarInj)

const events = inject(EventsInj)!

const props = defineProps<{
  table: TableType
  loadData?: (params?: any) => Promise<void>
  callAddEmptyRow?: (addAfter?: number) => Row | undefined
  updateOrSaveRow?: (
    row: Row,
    property?: string,
    ltarState?: Record<string, any>,
    args?: { metaValue?: TableType; viewMetaValue?: ViewType }
  ) => Promise<any>
  scrollToLastRow?: () => void
}>()

function addEmptyRow() {
  const rowObj = props.callAddEmptyRow?.()
  if (rowObj) {
    props.updateOrSaveRow?.(rowObj)
  }
  nextTick().then(() => {
    props.scrollToLastRow?.()
  })
}

const downloading = ref(false)
const clearLoading = ref(false)

const clearTable = async () => {
  Modal.confirm({
    title: '是否确定清空所有数据',
    icon: () => null,
    cancelText: '取消',
    okText: '清空',
    okButtonProps: {
      type: 'primary',
      danger: true
    },
    onOk: async () => {
      const baseId = props.table.base_id
      const tableId = props.table.id

      clearLoading.value = true
      const res = await $api.dbTable.clearTable(
        {
          db_id: baseId,
          table_id: tableId
        },
        {
          baseURL: window.AI_API_BASE_URL
        }
      )
      if (res?.success) {
        message.success('清空成功')
        props.loadData?.()
      }
      clearLoading.value = false
    }
  })
}

const exportFile = async (exportType: ExportTypes) => {
  const baseId = props.table.base_id
  const tableId = props.table.id
  const viewId = props.table.views?.[0]?.id

  if (!baseId || !tableId || !viewId) {
    return
  }

  const fields = props.table.columns?.filter((el) => !el.system).map((el) => el.column_name)

  let offset = 0
  // let c = 1
  const responseType = exportType === ExportTypes.EXCEL ? 'base64' : 'blob'

  try {
    downloading.value = true
    while (!isNaN(offset) && offset > -1) {
      const res = await $api.dbViewRow.export('noco', baseId, tableId, viewId, exportType, {
        responseType,
        query: {
          fields,
          offset,
          sortArrJson: JSON.stringify(sorts.value),
          filterArrJson: JSON.stringify(nestedFilters.value)
        }
      } as RequestParams)

      const { data, headers } = res
      const time = dayjs().format('YYYY-MM-DD_HH-mm-ss')
      if (exportType === ExportTypes.EXCEL) {
        const workbook = XLSX.read(data, { type: 'base64' })
        XLSX.writeFile(workbook, `${props.table.title}-${time}-export.xlsx`)
      } else if (exportType === ExportTypes.CSV) {
        const blob = new Blob([data], { type: 'text/plain;charset=utf-8' })
        saveAs(blob, `${props.table.title}-${time}-export.csv`)
      }
      offset = +headers['nc-export-offset']
      if (offset > -1) {
        // Downloading more files
        message.info(localize('download-more-files', '下载更多文件'))
      } else {
        // Successfully exported all table data
        message.success(localize('successfully-exported-all-table-data', '成功导出所有表内数据'))
        downloading.value = false
      }
    }
  } catch (e: any) {
    downloading.value = false
    message.error(await extractSdkResponseErrorMsg(e))
  }
}

const permission = inject(PermissionInj)
</script>

<template>
  <div class="nc-table-toolbar">
    <template v-if="isViewsLoading">
      <SkeletonInput active class="nc-table-toolbar-loading" />
    </template>
    <template v-else>
      <div class="ss-toolbar-group">
        <button
          v-if="permission === 'readwrite' && toolbar?.tools?.includes('add')"
          class="ss-toolbar-add-record"
          @click="addEmptyRow"
        >
          <span v-html="AddIcon" />
          {{ localize('add', '新增') }}
        </button>
        <Button
          v-if="permission === 'readwrite' && toolbar?.tools?.includes('import')"
          class="ss-toolbar-import-record nc-toolbar-btn"
          @click="events.onImportRow?.(table.id, table.base_id, table.source_id!, loadData)"
        >
          <span v-html="ImportIcon" />
          {{ localize('import', '导入') }}
        </Button>

        <FieldsMenu v-if="toolbar?.tools?.includes('fields')" />
        <ColumnFilterMenu />
        <!-- <LazySmartsheetToolbarGroupByMenu /> -->
        <div class="ss-toolbar-sort" v-if="toolbar?.tools?.includes('sort')">
          <SortListMenu />
        </div>
        <Button
          v-if="toolbar?.tools?.includes('export')"
          class="ss-toolbar-import-record nc-toolbar-btn"
          :loading="downloading"
          @click="exportFile(ExportTypes.EXCEL)"
        >
          <span v-html="DownloadIcon" />
          {{ localize('export', '导出') }}
        </Button>
        <Button
          v-if="toolbar?.tools?.includes('export')"
          class="ss-toolbar-import-record nc-toolbar-btn ss-toolbar-clear-table"
          :loading="clearLoading"
          @click="clearTable"
        >
          <span v-html="ClearIcon" class="ss-toolbar-clear-table-icon" />
          {{ localize('clear', '清空') }}
        </Button>
      </div>
      <div class="ss-toolbar-group">
        <SearchData v-if="toolbar?.tools?.includes('search')" />
        <template v-if="permission === 'readwrite' && toolbar?.tools?.includes('tableMetaConfig')">
          <span class="ss-toolbar-sep" />
          <button class="ss-toolbar-desc" @click="events.onTableMetaUpdate">
            {{ localize('invoke-configuration', '调用配置') }}
          </button>
        </template>
      </div>
    </template>
  </div>
</template>

<style lang="css">
.nc-toolbar-btn {
  border-radius: 4px;
  border-width: 0px;
  font-weight: 500;
  font-size: 16px;
  line-height: 1;
  padding-left: 8px;
  padding-right: 8px;
  color: rgba(74, 82, 104, 1);
  background-color: transparent;
  box-shadow: none;
}

.nc-toolbar-btn[disabled] {
  cursor: not-allowed !important;
  color: rgba(154, 162, 175, 1) !important;
}

.nc-toolbar-btn:hover {
  background: rgba(98, 105, 153, 0.08);
  color: #7b67ee !important;
}

.nc-toolbar-btn:focus {
  background-color: rgba(244, 244, 245, 1);
  color: rgba(31, 41, 58, 1) !important;
}

.nc-active-btn > .ant-btn {
  background-color: rgba(231, 231, 233, 0.2);
}
</style>

<style scoped lang="css">
.nc-table-toolbar {
  width: 100%;
  padding: 12px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
  flex-shrink: 0;
}

.ss-toolbar-group {
  display: flex;
  align-items: center;
}

.ss-toolbar-add-record {
  display: flex;
  align-items: center;
  gap: 4px;
  height: 32px;
  font-size: 14px;
  font-weight: 500;
  color: #7b67ee;
  padding: 0 10px;
  border-radius: 4px;

  &:hover {
    background: rgba(98, 105, 153, 0.08);
  }
}

.ss-toolbar-import-record {
  display: flex;
  align-items: center;
  gap: 4px;
  height: 32px;
  font-size: 14px;
  font-weight: 500;
  color: #4a5268;
  padding: 0 10px;
}

.ss-toolbar-clear-table:hover {
  background: rgba(255, 82, 25, 0.08) !important;
  color: #ff5219 !important;
}

.ss-toolbar-sep {
  width: 1px;
  height: 16px;
  background-color: rgba(225, 225, 229, 0.8);
  margin: 0 20px;
}

.ss-toolbar-desc {
  height: 32px;
  border-radius: 8px;
  padding: 0 10px;
  background-color: #fff;
  border: 1px solid rgba(225, 225, 229, 0.8);
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
  color: #17171d;

  &:hover {
    background-color: rgba(98, 105, 153, 0.08);
  }
}

.nc-table-toolbar-loading {
  width: 11rem !important;
  height: 1rem !important;
  margin-left: 0.5rem;
  border-radius: 0.25rem !important;
  overflow: hidden;
}
</style>
