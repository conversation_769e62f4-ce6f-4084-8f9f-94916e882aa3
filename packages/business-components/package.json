{"name": "@bty/business-components", "type": "module", "version": "1.0.4", "private": true, "description": "内部包含接口请求和业务逻辑处理的组件", "exports": {".": "./src/index.ts", "./*": "./src/*.ts"}, "main": "./src/index.ts", "peerDependencies": {"@ant-design/icons": "^4.8.0", "ahooks": "^3.7.8", "antd": "5.20.0", "dayjs": "^1.11.13", "react": "catalog:", "react-dom": "catalog:"}, "dependencies": {"@bty/async-loader": "workspace:*", "@bty/chat-fetch": "workspace:*", "@bty/components": "workspace:*", "@bty/constant": "workspace:*", "@bty/global-types": "workspace:*", "@bty/hooks": "workspace:*", "@bty/localize": "workspace:*", "@bty/util": "workspace:*", "@ebay/nice-modal-react": "^1.2.13", "@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "ali-oss": "catalog:", "classnames": "^2.3.2", "copy-to-clipboard": "^3.3.3", "lodash-es": "^4.17.21", "overlayscrollbars-react": "^0.5.2", "polished": "^4.2.2", "react18-json-view": "^0.2.8", "smooth-scrollbar": "catalog:"}, "devDependencies": {"@bty/tsconfig": "workspace:*", "@types/ali-oss": "catalog:", "@types/lodash-es": "^4.17.8", "@types/node": "catalog:", "@types/react": "catalog:", "@types/react-dom": "catalog:", "ahooks": "^3.7.8", "dayjs": "^1.11.13", "react": "catalog:", "react-dom": "catalog:"}}