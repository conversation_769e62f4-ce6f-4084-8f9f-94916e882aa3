import { css, Global } from '@emotion/react'
import { useEventListener } from 'ahooks'
import { Select as AntdSelect } from 'antd'
import type { SizeType } from 'antd/es/config-provider/SizeContext'
import classNames from 'classnames'
import { uniq } from 'lodash-es'
import type { ReactNode } from 'react'
import { useMemo, useRef, useEffect, useState } from 'react'
import Scrollbar from 'smooth-scrollbar'
import type { LLMChannels } from '@apis/llm/model'
import type {
  LLMModalItem,
  LLMModelLimitedItem,
  ModelFeature,
} from '@apis/llm/type'
import { IconFont, Select } from '@bty/components'
import { DefaultModelIcon } from './constant'
import { ModelOptionItem } from './ModelOptionItem'

const { Option, OptGroup } = AntdSelect

interface BaseUniqueItem {
  model_unique_key?: string
}

export interface ModelSelectProps {
  disabled?: boolean
  className?: string
  value?: {
    channel: LLMChannels
    model: string
    modelSetting?: any
    field_support?: string[]
  }
  onChange?: (value: {
    channel: LLMChannels
    model: string
    modelSetting?: any
    field_support?: string[]
  }) => void
  placeholder?: string
  getPopupContainer?: () => HTMLElement
  size?: SizeType
  filterByMethodSupport?: string[]
  filterByModelList?: string[]
  showModelDescription?: boolean
  popupWrapper?: HTMLDivElement | null
  popupMatchSelectWidth?: boolean
  asyncSupport?: boolean
  listHeight?: number
  llmModelList: (LLMModalItem & BaseUniqueItem)[]
  llmModelLimitedList: (LLMModelLimitedItem & BaseUniqueItem)[]
  llmAsyncSupportModelLimitedList: (LLMModelLimitedItem & BaseUniqueItem)[]
  getLlmModelListLoading: boolean
  getAsyncSupportModelListLoading: boolean
  showPoints?: boolean
  onLevelUp?: () => void
}

export interface ModelSelectOption {
  model: string
  label: ReactNode
  value: string
  disabled: boolean
  description: string | null
  needLevelUp: boolean
  feature: ModelFeature
  type?: string
  model_unique_key: string
  field_support?: string[]
  onLevelUp?: () => void
}

type ModelOptionMap = Record<
  string,
  {
    label?: string
    options: Array<ModelSelectOption>
  }
>

const CHANNEL_SORT_HEIGHT: Record<string, number> = {
  GPT: 99,
  DeepSeek: 98,
  Amazon: 97,
  Google: 96,
  Moonshot: 95,
  Volcano: 94,
  ZMN: -2,
  ZMN_QianFan: -2,
  ZMN_TongYiQWen: -2,
  ZMN_Volcano: -2,
}

export function GLobalSelectStyle() {
  return (
    <Global
      styles={css`
        .limitMaxWidth {
          max-width: 350px;
        }
        .ant-select-selector {
          padding: 4px 10px !important;
          background-color: #fff !important;
          border-color: rgba(225, 225, 229, 0.8) !important;
          box-shadow: none !important;
        }
        .ant-select-selector:hover {
          background-color: rgba(98, 105, 153, 0.08) !important;
          border-color: rgba(225, 225, 229, 0.8) !important;
        }
        .ant-select-item-group:not(:first-of-type) {
          margin-top: 4px;
          border-top: 1px solid #e1e1e599;
          border-radius: 0%;
        }
      `}
    />
  )
}

/**
 * TODO 业务需要，废弃一部分模型，暂时由前端控制，后续该逻辑需要服务端去做
 * 后续做法：
 * 服务端平滑降级后，模型会被标记为enable: false,
 * 1、业务保持现有逻辑，从getLLMModelOptions中过滤enable false 的模型
 * 2、在Select value中特殊处理，对于废弃模型，需要保持原始名称，以防止出现 GPT__cpt-3.5-turbo的显示
 */
const deprecatedModels = ['gpt-4']

function generalModalValue(channel: LLMChannels, model: string) {
  // 兼容老数据，废弃的模型，需要显示格式化之前的model名称，💩
  if (deprecatedModels.includes(model)) {
    return model
  }
  return `${channel}___${model}`
}

function parseModelValue(value: string) {
  const parseValues = value.split('___')
  return {
    channel: (parseValues?.[0] ?? '') as LLMChannels,
    model: parseValues?.[1] ?? '',
  }
}

export function ModelSelect(props: ModelSelectProps) {
  const {
    disabled,
    className,
    placeholder,
    filterByMethodSupport,
    filterByModelList,
    size = 'small',
    // showModelDescription = true,
    asyncSupport = false,
    listHeight = 456,
    llmModelList,
    llmModelLimitedList,
    llmAsyncSupportModelLimitedList,
    getLlmModelListLoading,
    getAsyncSupportModelListLoading,
    showPoints = false,
  } = props
  const wrapperRef = useRef<HTMLDivElement>(null)
  const droprRef = useRef<HTMLDivElement>(null)
  const [visiable, setVisiable] = useState(false)
  const [scrollPosition, setScrollPosition] = useState(0) // 用于保存滚动位置
  useEffect(() => {
    const element = droprRef?.current?.firstChild?.firstChild as HTMLElement
    if (!element) return
    const scrollbar = Scrollbar.init(element, {
      alwaysShowTracks: false,
    })

    // 计算选中项的位置并滚动到该位置
    const selectedOption = droprRef?.current?.querySelector(
      '.ant-select-item-option-selected',
    ) as HTMLElement
    if (selectedOption) {
      setScrollPosition(selectedOption.offsetTop)
      Scrollbar.get(element)?.scrollTo(0, selectedOption.offsetTop)
    }

    return () => {
      scrollbar.destroy()
    }
  }, [visiable, droprRef?.current])
  useEffect(() => {
    const element = droprRef?.current?.firstChild?.firstChild as HTMLElement
    if (scrollPosition !== 0) {
      Scrollbar.get(element)?.scrollTo(0, scrollPosition)
    }
  }, [visiable, scrollPosition])

  const loading = useMemo(
    () =>
      asyncSupport ? getAsyncSupportModelListLoading : getLlmModelListLoading,
    [asyncSupport, getLlmModelListLoading, getAsyncSupportModelListLoading],
  )
  const limitedList = useMemo(
    () =>
      asyncSupport ? llmAsyncSupportModelLimitedList : llmModelLimitedList,
    [asyncSupport, llmAsyncSupportModelLimitedList, llmModelLimitedList],
  )

  const getLLMModelOptions = () => {
    if (!llmModelList.length) {
      return []
    }
    const resultList = llmModelList.filter(item => {
      if (deprecatedModels.includes(item.model)) {
        return false
      }
      if (filterByMethodSupport || filterByModelList) {
        return (
          item.enable &&
          ((filterByMethodSupport
            ? filterByMethodSupport.includes(item.methodSupport)
            : false) ||
            (filterByModelList
              ? filterByModelList.includes(item.model)
              : false))
        )
      }

      return item.enable
    })

    const channels = uniq(resultList.map(item => item.code)).sort((a, b) => {
      return (CHANNEL_SORT_HEIGHT[b] ?? 0) - (CHANNEL_SORT_HEIGHT[a] ?? 0)
    })

    const groupedData = channels.reduce((acc, channel) => {
      return {
        ...acc,
        [channel]: {
          label: resultList.find(v => v.code === channel)?.name,
          options: resultList
            .filter(v => v.code === channel)
            .map(item => {
              const hasLimitedList = limitedList.find(
                j => j.model_unique_key === item.model_unique_key,
              )
              const canUsed = hasLimitedList?.llm_enabled
              const field_support = item?.feature?.field_support || []
              return {
                // label: item.model,
                model: item.model,
                label: (
                  <div className='h-full flex flex-items-center gap-x-6px'>
                    {item?.feature?.icon ? (
                      <img
                        src={item?.feature?.icon || DefaultModelIcon}
                        alt=''
                        className='w-16px h-16px'
                      />
                    ) : (
                      <IconFont
                        name='muxing'
                        className='mr-8px text-16px text-#626999 text-op-60'
                      />
                    )}

                    <span>{item.model}</span>
                  </div>
                ),
                value: generalModalValue(item.code, item.model),
                description: item.description,
                feature: item.feature,
                disabled: !hasLimitedList || item.grayTest,
                type: item.type,
                field_support,
                model_unique_key: item.model_unique_key,
                needLevelUp: item.grayTest
                  ? false
                  : hasLimitedList
                    ? !canUsed
                    : false,
              }
            })
            .filter(item => !item.disabled),
        },
      }
    }, {} as ModelOptionMap)

    return Object.values(groupedData).filter(item => item.options.length > 0)
  }

  /**
   * 判断当前选中的模型是否是废弃模型
   * 服务端平滑降级后，模型会被标记为enable: false,
   * 1、当model不在llmModelList中或者模型的enable为false时为废弃模型
   */
  const isDeprecatedModel = useMemo(() => {
    return (
      llmModelList.findIndex(
        item =>
          item.enable &&
          item.model === props.value?.model &&
          item.code === props.value?.channel,
      ) === -1
    )
  }, [props.value?.model, props.value?.channel, llmModelList])

  const modelOptions = useMemo(
    () => getLLMModelOptions(),
    [limitedList, llmModelList],
  )
  useEventListener(
    'wheel',
    e => {
      const isPinch = !Number.isInteger(e.deltaY) && e.ctrlKey

      if (isPinch) {
        e.preventDefault()
      }
    },
    { target: wrapperRef },
  )

  const renderOptions = (options: ModelSelectOption[]) => {
    return options.map(opt => {
      const findLimitItem = limitedList?.find(
        item => item.model_unique_key === opt.model_unique_key,
      )
      return (
        <Option
          key={opt.value}
          value={opt.value}
          label={opt.label}
          disabled={opt.disabled}
          field_support={opt.field_support}
          className='important:p-0 mt-4px'
        >
          <ModelOptionItem
            opt={opt}
            findLimitItem={findLimitItem}
            showPoints={showPoints}
            onLevelUp={props.onLevelUp}
          />
        </Option>
      )
    })
  }

  return (
    <div ref={wrapperRef} onWheel={e => e.stopPropagation()}>
      <GLobalSelectStyle />
      <Select<string, ModelSelectOption>
        disabled={disabled}
        // open={true}
        className={classNames('nodrag nopan w-full', className)}
        placeholder={placeholder}
        value={
          props.value
            ? isDeprecatedModel
              ? props.value.model
              : generalModalValue(props.value.channel, props.value.model)
            : undefined
        }
        onChange={(value, option) => {
          const { channel, model } = parseModelValue(value)
          props.onChange?.({
            channel,
            model,
            field_support: (option as ModelSelectOption)?.field_support || [],
          })
        }}
        loading={loading}
        popupClassName='selectLLM'
        // getPopupContainer={() => wrapperRef.current as HTMLElement}
        size={size}
        onDropdownVisibleChange={(open: boolean) => setVisiable(open)}
        listHeight={listHeight}
        virtual={false}
        dropdownRender={menu => {
          return <div ref={droprRef}>{menu}</div>
        }}
        optionLabelProp='label'
        // options={modelOptions}
        popupMatchSelectWidth={
          // popupWrapper?.clientWidth || popupMatchSelectWidth
          false
        }
      >
        {modelOptions.length > 1
          ? modelOptions.map(item => {
              return (
                <OptGroup label={item.label} key={item.label}>
                  {renderOptions(item.options)}
                </OptGroup>
              )
            })
          : renderOptions(modelOptions[0]?.options ?? [])}
      </Select>
    </div>
  )
}
