.bty-mark-content {
  color-scheme: light;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  word-wrap: break-word;
}

.bty-mark-content details,
.bty-mark-content figcaption,
.bty-mark-content figure {
  display: block;
}

.bty-mark-content summary {
  display: list-item;
}

.bty-mark-content [hidden] {
  display: none !important;
}

.bty-mark-content a {
  background-color: transparent;
  text-decoration: none;
}

.bty-mark-content abbr[title] {
  border-bottom: none;
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
}

.bty-mark-content b,
.bty-mark-content strong {
  font-weight: 600;
}

.bty-mark-content dfn {
  font-style: italic;
}

.bty-mark-content h1 {
  margin: 10px 0;
  font-weight: 600;
  font-size: 28px;
}

.bty-mark-content mark {
  background-color: #fff8c5;
  color: #1f2328;
}

.bty-mark-content small {
  font-size: 90%;
}

.bty-mark-content sub,
.bty-mark-content sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

.bty-mark-content sub {
  bottom: -4px;
}

.bty-mark-content sup {
  top: -7px;
}

.bty-mark-content img {
  border-style: none;
  max-width: 100%;
  box-sizing: content-box;
}

.bty-mark-content code,
.bty-mark-content kbd,
.bty-mark-content pre,
.bty-mark-content samp {
  font-family: monospace;
  font-size: 14px;
  line-height: 1.45;
}

.bty-mark-content figure {
  margin: 14px 35px;
}

.bty-mark-content hr {
  box-sizing: content-box;
  overflow: hidden;
  background: transparent;
  border-bottom: 1px solid #d1d9e0b3;
  height: 1px;
  padding: 0;
  margin: 21px 0;
  background-color: #d1d9e0;
  border: 0;
}

.bty-mark-content input {
  font: inherit;
  margin: 0;
  overflow: visible;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

.bty-mark-content [type='button'],
.bty-mark-content [type='reset'],
.bty-mark-content [type='submit'] {
  -webkit-appearance: button;
  appearance: button;
}

.bty-mark-content [type='checkbox'],
.bty-mark-content [type='radio'] {
  box-sizing: border-box;
  padding: 0;
}

.bty-mark-content ::-webkit-input-placeholder {
  color: inherit;
  opacity: 0.54;
}

.bty-mark-content ::placeholder {
  color: #59636e;
  opacity: 1;
}

.bty-mark-content details summary {
  cursor: pointer;
}

.bty-mark-content kbd {
  display: inline-block;
  padding: 4px;
  font:
    11px ui-monospace,
    SFMono-Regular,
    SF Mono,
    Menlo,
    Consolas,
    Liberation Mono,
    monospace;
  line-height: 10px;
  color: #1f2328;
  vertical-align: middle;
  background-color: #f6f8fa;
  border: solid 1px #d1d9e0b3;
  border-bottom-color: #d1d9e0b3;
  border-radius: 6px;
  box-shadow: inset 0 -1px 0 #d1d9e0b3;
}

.bty-mark-content h1,
.bty-mark-content h2,
.bty-mark-content h3,
.bty-mark-content h4,
.bty-mark-content h5,
.bty-mark-content h6 {
  margin-top: 12px !important;
  margin-bottom: 12px !important;
  font-weight: 700 !important;
  line-height: 1.25 !important;
}

.bty-mark-content h2 {
  font-weight: 600;
  font-size: 18px !important;
}

.bty-mark-content h3 {
  font-weight: 600;
  font-size: 18px !important;
}

.bty-mark-content h4 {
  font-weight: 600;
  font-size: 16px !important;
}

.bty-mark-content h5 {
  font-weight: 600;
  font-size: 14px !important;
}

.bty-mark-content h6 {
  font-weight: 600;
  font-size: 12px !important;
  color: #59636e;
}

.bty-mark-content p {
  margin-top: 0;
  margin-bottom: 10px;
}

.bty-mark-content blockquote {
  margin: 0;
  padding: 0 14px;
  color: #59636e;
  border-left: 4px solid #d1d9e0;
}

.bty-mark-content ul,
.bty-mark-content ol {
  margin-top: 0;
  margin-bottom: 0;
  padding-left: 28px;
}

.bty-mark-content ol ol,
.bty-mark-content ul ol {
  list-style-type: lower-roman;
}

.bty-mark-content ul ul ol,
.bty-mark-content ul ol ol,
.bty-mark-content ol ul ol,
.bty-mark-content ol ol ol {
  list-style-type: lower-alpha;
}

.bty-mark-content dd {
  margin-left: 0;
}

.bty-mark-content tt,
.bty-mark-content code,
.bty-mark-content samp {
  font-family: ui-monospace, monospace;
  font-size: 12px;
}

.bty-mark-content pre {
  margin-top: 0;
  margin-bottom: 0;
  font-family: ui-monospace, monospace;
  font-size: 12px;
  word-wrap: normal;
}

.bty-mark-content a:not([href]) {
  color: inherit;
  text-decoration: none;
}

.bty-mark-content .absent {
  color: #d1242f;
}

.bty-mark-content .anchor {
  float: left;
  padding-right: 4px;
  margin-left: -20px;
  line-height: 1;
}

.bty-mark-content .anchor:focus {
  outline: none;
}

.bty-mark-content p,
.bty-mark-content blockquote,
.bty-mark-content ul,
.bty-mark-content ol,
.bty-mark-content dl,
.bty-mark-content table,
.bty-mark-content pre,
.bty-mark-content details {
  margin-top: 0;
  margin-bottom: 8.4px;
}

.bty-mark-content blockquote > :first-child {
  margin-top: 0;
}

.bty-mark-content blockquote > :last-child {
  margin-bottom: 0;
}

.bty-mark-content h1 tt,
.bty-mark-content h1 code,
.bty-mark-content h2 tt,
.bty-mark-content h2 code,
.bty-mark-content h3 tt,
.bty-mark-content h3 code,
.bty-mark-content h4 tt,
.bty-mark-content h4 code,
.bty-mark-content h5 tt,
.bty-mark-content h5 code,
.bty-mark-content h6 tt,
.bty-mark-content h6 code {
  padding: 0 2.8px;
  font-size: inherit;
}

.bty-mark-content summary h1,
.bty-mark-content summary h2,
.bty-mark-content summary h3,
.bty-mark-content summary h4,
.bty-mark-content summary h5,
.bty-mark-content summary h6 {
  display: inline-block;
}

.bty-mark-content summary h1,
.bty-mark-content summary h2 {
  padding-bottom: 0;
  border-bottom: 0;
}

.bty-mark-content div > ol:not([type]) {
  list-style-type: decimal;
}

.bty-mark-content ul {
  list-style-type: disc;
}

.bty-mark-content ol {
  list-style-type: decimal;
}

.bty-mark-content ul ul,
.bty-mark-content ul ol,
.bty-mark-content ol ol,
.bty-mark-content ol ul {
  margin-top: 0;
  margin-bottom: 0;
}

.bty-mark-content li > p {
  margin-top: 14px;
}

.bty-mark-content li + li {
  margin-top: 56px;
}

.bty-mark-content dl {
  padding: 0;
}

.bty-mark-content dl dt {
  padding: 0;
  margin-top: 14px;
  font-size: 14px;
  font-style: italic;
  font-weight: 600;
}

.bty-mark-content dl dd {
  padding: 0 14px;
  margin-bottom: 14px;
}


.bty-mark-content table {
  border-spacing: 0;
  border-collapse: collapse;
  display: block;
  width: max-content;
  max-width: 100%;
  overflow: auto;
  font-variant: tabular-nums;
  font-size: 14px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  margin-top: 0px;
  margin-bottom: 4px;
}

.bty-mark-content td,
.bty-mark-content th {
  padding: 0;
  line-height: 1.4;
}

.bty-mark-content table th {
  font-weight: 600;
}

.bty-mark-content table th,
.bty-mark-content table td {
  padding: 6px 13px;
  border: 1px solid #d1d9e0;
}

.bty-mark-content table td > :last-child {
  margin-bottom: 0;
}

.bty-mark-content table tr {
  background-color: #ffffff;
  border-top: 1px solid #d1d9e0b3;
}

.bty-mark-content table tr:nth-child(2n) {
  background-color: #f6f8fa;
}

.bty-mark-content table img {
  background-color: transparent;
}

.bty-mark-content code,
.bty-mark-content tt {
  padding: 3px 6px;
  margin: 0;
  font-size: 85%;
  white-space: break-spaces;
  border-radius: 6px;
  line-height: 1.45;
}

.bty-mark-content code br,
.bty-mark-content tt br {
  display: none;
}

.bty-mark-content del code {
  text-decoration: inherit;
}

.bty-mark-content samp {
  font-size: 85%;
}

.bty-mark-content pre {
  word-break: break-all;
  white-space: pre;
}

.bty-mark-content pre code {
  font-size: 100%;
  line-height: 1.45;
}

.bty-mark-content pre > code {
  padding: 0;
  margin: 0;
  word-break: break-all;
  white-space: pre;
  background: transparent;
  border: 0;
}

.bty-mark-content .highlight {
  margin-bottom: 14px;
}

.bty-mark-content .highlight pre {
  margin-bottom: 0;
  word-break: normal;
}

.bty-mark-content .highlight pre,
.bty-mark-content pre {
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
  color: #1f2328;
  background-color: #f6f8fa;
  border-radius: 6px;
  margin-top: 8px;
}

.bty-mark-content pre code,
.bty-mark-content pre tt {
  display: inline;
  max-width: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  line-height: inherit;
  word-wrap: normal;
  background-color: transparent;
  border: 0;
  line-height: 1.45;
}

.bty-mark-content p {
  margin-top: 8px;
}

.bty-mark-content ul {
  margin-top: 8px !important;
}

.bty-mark-content li {
  line-height: 21px !important;
  margin-top: 8px !important;
}

.bty-mark-content .markdown-paragraph {
  font-size: 14px !important;
  line-height: 24px !important;
  margin-top: 8px !important;
  margin-bottom: 8px !important;
}

.bty-mark-content strong {
  font-weight: 700 !important;
}

.bty-mark-content table th {
  background-color: rgb(245, 245, 245);
  padding: 9px 14px;
  border: 0px;
  text-align: left;
}

.bty-mark-content table td {
  padding: 9px 14px;
  border: 0px;
  color: rgba(0, 0, 0, 0.6)
}

.bty-mark-content table tr {
  border: 0px;
}

.bty-mark-content table tr:nth-child(2n) {
  background-color: rgb(245, 245, 245);
}
