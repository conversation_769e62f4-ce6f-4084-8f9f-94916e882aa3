{"name": "@bty/chat-sdk", "type": "module", "version": "0.2.12", "license": "MIT", "publishConfig": {"access": "public"}, "main": "dist/cjs/index.cjs", "module": "dist/es/index.js", "systemjs": "dist/system/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"lint": "eslint src/", "build:type": "tsup src/index.ts --dts-only --format esm", "build:code": "NODE_OPTIONS=--max-old-space-size=4096 tsx scripts/build.ts", "build": "NODE_ENV=production pnpm run build:code && pnpm run build:type", "visualizer": "tsx scripts/visualizer.ts"}, "dependencies": {"@types/react": "catalog:", "@types/react-dom": "catalog:", "@unocss/reset": "catalog:", "react": "catalog:", "react-dom": "catalog:"}, "devDependencies": {"@bty/chat": "workspace:*", "@bty/global-types": "workspace:*", "@bty/tsconfig": "workspace:*", "@bty/unplugins": "workspace:*", "@bty/util": "workspace:*", "@types/qs": "^6.9.16", "@vitejs/plugin-react-swc": "catalog:", "esbuild-plugin-svgr": "^2.1.0", "rollup-plugin-visualizer": "^5.12.0", "tsx": "^3.14.0", "typescript": "^5.1.6", "unocss": "catalog:", "vite": "^5.4.9", "vite-plugin-dts": "^3.7.3", "vite-plugin-svgr": "catalog:"}}