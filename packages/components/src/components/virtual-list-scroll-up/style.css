.virtual-scroll-up-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  padding: 0;
  margin: 0;
}

/* 滚动条样式 */
.virtual-scroll-up-list::-webkit-scrollbar {
  width: 6px;
}

.virtual-scroll-up-list::-webkit-scrollbar-track {
  background: rgba(222, 222, 222, .75);
}

.virtual-scroll-up-list::-webkit-scrollbar-thumb {
  background: #0000004d !important;
  border-radius: 3px;
}

.virtual-scroll-up-list::-webkit-scrollbar-thumb:hover {
  background: #00000080 !important;
}

.virtual-scroll-up-list .virtual-scroll-item {
  width: 100%;
  flex-shrink: 0;
}

.virtual-scroll-up-list .icon-loading {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  width: 100%;
  height: 100%;
  z-index: 999;
  left: 0;
  top: 0;
  opacity: 0.5;
}

.virtual-scroll-up-list .icon-loading img {
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}


.virtual-scroll-up-list .loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  position: sticky;
  top: -16px;
  z-index: 10;
}

.virtual-scroll-up-list .no-more-data-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #9CA3AF;
  top: -16px;
  z-index: 10;
  position: absolute;
  inset: 0;
  height: 24px;
}

.virtual-scroll-up-list .scroll-to-bottom {
  position: fixed;
  bottom: 140px;
  right: 20px;
  cursor: pointer;
  z-index: 100;
}

.virtual-scroll-up-list .to-bottom-button {
  font-size: 16px;
  color: #9ca3af;
  background-color: #fff;
  border-radius: 50%;
  padding: 5px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  justify-content: center;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .virtual-scroll-up-list::-webkit-scrollbar {
    width: 0;
  }

  .virtual-scroll-up-list .scroll-to-bottom {
    bottom: 120px;
    right: 16px;
    padding: 10px 20px;
    font-size: 14px;
  }
}
