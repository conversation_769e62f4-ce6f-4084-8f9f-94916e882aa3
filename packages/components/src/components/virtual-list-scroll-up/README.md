# VirtualListScrollUp 虚拟滚动列表组件

一个高性能的 React 虚拟滚动列表组件，专为处理大量数据而设计，支持自动滚动、分页加载、历史消息加载、样式和 UI 高度定制。

## 特性

- 🚀 **高性能渲染**：虚拟滚动技术，只渲染可见区域的数据项
- 📱 **自适应分页**：智能分页加载，减少 DOM 节点数量
- 🔄 **双向加载**：支持向上加载历史数据，向下显示新数据
- 📍 **位置保持**：加载新数据时保持用户当前的滚动位置
- ⬇️ **自动滚动**：可配置自动滚动到底部功能
- 🎯 **边缘检测**：智能检测滚动边缘，触发相应的加载逻辑
- 🔧 **高度可配置**：丰富的配置选项满足不同使用场景
- 🎨 **可自定义 UI**：支持自定义滚动到底部按钮、加载指示器、无数据提示、样式 className

## 安装与导入

```tsx
import VirtualListScrollUp from '@bty/components';
```

## 基础用法

```tsx
import React, { useCallback } from 'react';
import VirtualListScrollUp from '@bty/components';

function ChatComponent() {
  const messages = [
    { id: '1', role: 'user', content: '你好' },
    { id: '2', role: 'assistant', content: '你好！有什么可以帮助你的吗？' },
    // ...
  ];

  const renderMessage = useCallback((message, index) => {
    const isUser = message.role === 'user';
    return (
      <div className={isUser ? 'user-message' : 'bot-message'}>
        <div className="message-avatar">{isUser ? '👤' : '🤖'}</div>
        <div className="message-content">{message.content}</div>
      </div>
    );
  }, []);

  return (
    <VirtualListScrollUp
      items={messages}
      renderItem={renderMessage}
      idExtractor={msg => msg.id}
    />
  );
}
```

## API 参考

### VirtualListScrollUpProps

| 属性 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|-------|------|
| `items` | `T[]` | ✅ | - | 要渲染的数据项数组 |
| `renderItem` | `(item: T, index: number) => React.ReactNode` | ✅ | - | 渲染单个列表项的函数 |
| `idExtractor` | `(item: T) => string \| number` | ✅ | - | 提取每个项目唯一键的函数 |
| `pageSize` | `number` | ❌ | `20` | 每页显示的项目数量 |
| `edgeThreshold` | `number` | ❌ | `100` | 边缘阈值（px），用于触发加载 |
| `onLoadMore` | `() => void` | ❌ | - | 加载更多数据的回调函数 |
| `hasMoreData` | `boolean` | ❌ | `true` | 是否还有更多数据可以加载 |
| `loading` | `boolean` | ❌ | `false` | 是否正在加载中 |
| `autoScrollToBottom` | `boolean` | ❌ | `true` | 是否自动滚动到底部 |
| `onScroll` | `(scrollTop: number, isAtBottom: boolean) => void` | ❌ | - | 滚动事件回调函数 |
| `children` | `React.ReactNode` | ❌ | - | 子组件内容（渲染在列表底部） |
| `scrollToBottomElement` | `React.ReactNode` | ❌ | - | 自定义滚动到底部按钮元素 |
| `showScrollToBottom` | `boolean` | ❌ | `true` | 是否显示滚动到底部按钮 |
| `loadingElement` | `React.ReactNode` | ❌ | - | 自定义加载指示器元素 |
| `noMoreDataElement` | `React.ReactNode` | ❌ | - | 自定义没有更多数据提示元素 |
| `showNoMoreData` | `boolean` | ❌ | `true` | 是否显示没有更多数据提示 |
| `overrides` | `VirtualListScrollUpClassNames` | ❌ | - | 各部分 className 覆盖 |
| `styles` | `VirtualListScrollUpStyles` | ❌ | - | 各部分内联样式覆盖 |
| `containerStyle` | `React.CSSProperties` | ❌ | - | 主容器额外样式（已废弃，请使用 styles.container） |

#### VirtualListScrollUpClassNames

| 属性 | 类型 | 说明 |
|------|------|------|
| `container` | `string` | 主容器 className |
| `loadingIndicator` | `string` | 加载指示器 className |
| `scrollItem` | `string` | 列表项容器 className |
| `scrollToBottom` | `string` | 滚动到底部按钮 className |
| `noMoreData` | `string` | 没有更多数据提示 className |

#### VirtualListScrollUpStyles

| 属性 | 类型 | 说明 |
|------|------|------|
| `container` | `React.CSSProperties` | 主容器样式 |
| `scrollItem` | `React.CSSProperties` | 列表项样式 |
| `scrollToBottom` | `React.CSSProperties` | 滚动到底部按钮样式 |
| `loadingIndicator` | `React.CSSProperties` | 加载指示器样式 |
| `noMoreData` | `React.CSSProperties` | 没有更多数据提示样式 |

#### VirtualListScrollUpRef

组件通过 ref 暴露的方法：

| 方法 | 类型 | 说明 |
|------|------|------|
| `scrollToBottom` | `() => void` | 滚动到列表底部 |
| `scrollTo` | `(top: number) => void` | 滚动到指定位置 |

## 高级用法

### 1. 自定义滚动到底部按钮

```tsx
<VirtualListScrollUp
  items={messages}
  renderItem={renderMessage}
  idExtractor={msg => msg.id}
  scrollToBottomElement={<div className="my-scroll-btn">回到底部</div>}
/>
```

### 2. 完全隐藏滚动按钮

```tsx
<VirtualListScrollUp
  items={messages}
  renderItem={renderMessage}
  idExtractor={msg => msg.id}
  showScrollToBottom={false}
/>
```

### 3. 自定义加载和无数据提示

```tsx
<VirtualListScrollUp
  items={messages}
  renderItem={renderMessage}
  idExtractor={msg => msg.id}
  loading={isLoading}
  loadingElement={<div>加载中...</div>}
  noMoreDataElement={<div>没有更多了</div>}
/>
```

### 4. 样式和 className 定制

```tsx
<VirtualListScrollUp
  items={messages}
  renderItem={renderMessage}
  idExtractor={msg => msg.id}
  overrides={{
    container: 'my-list-container',
    scrollItem: 'my-item',
    scrollToBottom: 'my-bottom-btn',
  }}
  styles={{
    container: { height: 400 },
    scrollToBottom: { right: 24, bottom: 24 },
  }}
/>
```

### 5. 禁用自动滚动

```tsx
<VirtualListScrollUp
  items={messages}
  renderItem={renderMessage}
  idExtractor={msg => msg.id}
  autoScrollToBottom={false}
/>
```

### 6. ref 获取方法

```tsx
import { useRef } from 'react'

const listRef = useRef<VirtualListScrollUpRef>(null)

// 组件内部暴露 scrollToBottom 和 scrollTo 方法
<VirtualListScrollUp ref={listRef} ... />

// 跳转到底部
listRef.current?.scrollToBottom()

// 跳转到指定位置
listRef.current?.scrollTo(1000)
```

### 7. 监听滚动事件

```tsx
<VirtualListScrollUp
  items={messages}
  renderItem={renderMessage}
  idExtractor={msg => msg.id}
  onScroll={(scrollTop, isAtBottom) => {
    console.log('当前滚动位置:', scrollTop)
    console.log('是否在底部:', isAtBottom)
  }}
/>
```

### 8. 加载更多数据

```tsx
const [loading, setLoading] = useState(false)
const [hasMore, setHasMore] = useState(true)

const handleLoadMore = useCallback(async () => {
  setLoading(true)
  try {
    // 加载更多数据的逻辑
    const moreData = await fetchMoreMessages()
    if (moreData.length === 0) {
      setHasMore(false)
    }
    // 更新消息列表...
  } finally {
    setLoading(false)
  }
}, [])

return (
  <VirtualListScrollUp
    items={messages}
    renderItem={renderMessage}
    idExtractor={msg => msg.id}
    onLoadMore={handleLoadMore}
    hasMoreData={hasMore}
    loading={loading}
  />
)
```

## 工作原理

- **分页渲染**：只渲染当前页及前后各一页（3×pageSize）
- **边缘检测**：滚动到边缘自动加载/分页
- **位置保持**：顶部加载历史消息时自动保持视觉位置
- **自动滚动**：新消息时如在底部自动滚动

## 样式定制

组件默认 className：

- `virtual-scroll-up-list` 主容器
- `virtual-scroll-item` 列表项
- `loading-indicator` 加载指示器
- `scroll-to-bottom` 滚动到底部按钮
- `no-more-data-indicator` 没有更多数据提示

可通过 `overrides`/`styles` 进行覆盖。

## 性能优化建议

- 用 `useCallback` 缓存 renderItem
- idExtractor 使用稳定唯一 id
- pageSize 不宜过大（建议10-50）
- 避免在 renderItem 中进行复杂计算

## 常见问题

### Q: 如何自定义每个部分的样式？
A: 用 `overrides`（className）和 `styles`（内联样式）分别覆盖。

### Q: 如何实现消息跳转？
A: 通过 ref 获取组件实例，调用 `scrollToBottom` 或 `scrollTo` 方法。

### Q: 如何处理图片加载导致的高度变化？
A: 建议图片加载完成后调用组件的重新计算方法，或为图片设置固定高度。

### Q: 滚动到底部按钮什么时候显示？
A: 当用户不在列表底部且 `showScrollToBottom` 为 true 时显示，按钮有缩放动画效果。

### Q: 加载指示器在哪里显示？
A: 加载指示器显示在列表顶部，当 `loading` 为 true 时显示。

### Q: 无更多数据提示什么时候显示？
A: 当 `hasMoreData` 为 false、不在加载中、有数据且 `showNoMoreData` 为 true 时显示。

## 更新日志

### 最新版本
- ✨ 新增 VirtualListScrollUpRef 接口，支持 scrollTo 方法
- ✨ 新增 overrides/styles 支持
- ✨ 支持自定义 loading/noMoreData 元素
- ✨ 支持 ref 获取 scrollToBottom 和 scrollTo 方法
- 🎨 滚动到底部按钮支持缩放动画效果
- 🔧 优化边缘检测和分页逻辑

### 历史版本
- ✨ 新增自定义滚动到底部按钮功能
- ✨ 新增控制滚动按钮显示/隐藏的选项
- 🎨 支持完全自定义滚动按钮 UI
- ✨ 支持虚拟滚动和分页渲染
- ✨ 支持自动滚动到底部
- ✨ 支持历史消息加载
- ✨ 支持滚动位置保持
