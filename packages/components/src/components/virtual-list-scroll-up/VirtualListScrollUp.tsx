import React, { useCallback, useEffect, useMemo, useRef, useState, forwardRef, useImperativeHandle } from 'react'
import './style.css'
import { cn } from '@bty/util'

function Loading({ className = '' }: { className?: string }) {
  return <div className={cn('icon-loading', className,)}>
    <img src='https://resource.bantouyan.com/battleyeah-ai/chat-mobile/circle.png' />
  </div>
}

function ToBottomButton() {
  return <span className={'to-bottom-button'}>↓</span>
}

/**
 * 样式覆盖配置接口
 */
export interface VirtualListScrollUpClassNames {
  /** 主容器的 className */
  container?: string
  /** 加载指示器的 className */
  loadingIndicator?: string
  /** 列表项容器的 className */
  scrollItem?: string
  /** 滚动到底部按钮的 className */
  scrollToBottom?: string
  /** 没有更多数据提示的 className */
  noMoreData?: string
}

export interface VirtualListScrollUpStyles {
  container?: React.CSSProperties
  scrollItem?: React.CSSProperties
  scrollToBottom?: React.CSSProperties
  loadingIndicator?: React.CSSProperties
  /** 没有更多数据提示的样式 */
  noMoreData?: React.CSSProperties
}

/**
 * 虚拟滚动列表组件的属性接口
 * @template T - 列表项的数据类型
 */
export interface VirtualListScrollUpProps<T> {
  /** 要渲染的数据项数组 */
  items: T[]
  /** 渲染单个列表项的函数 */
  renderItem: (item: T, index: number) => React.ReactNode
  /** 每页显示的项目数量，默认20 */
  pageSize?: number
  /** 边缘阈值，用于触发加载的距离，默认100px */
  edgeThreshold?: number
  /** 加载更多数据的回调函数 */
  onLoadMore?: () => void
  /** 是否还有更多数据可以加载 */
  hasMoreData?: boolean
  /** 是否正在加载中 */
  loading?: boolean
  /** 是否自动滚动到底部 */
  autoScrollToBottom?: boolean
  /** 用于提取每个项目唯一键的函数 */
  idExtractor: (item: T) => string | number
  /** 滚动事件回调函数 */
  onScroll?: (scrollTop: number, isAtBottom: boolean) => void
  /** 子组件内容 */
  children?: React.ReactNode
  /** 自定义滚动到底部按钮元素，如果不提供则使用默认样式 */
  scrollToBottomElement?: React.ReactNode
  /** 是否显示滚动到底部按钮，默认为true */
  showScrollToBottom?: boolean
  /** 自定义加载指示器元素，如果不提供则使用默认样式 */
  loadingElement?: React.ReactNode
  /** 自定义没有更多数据提示元素，如果不提供则使用默认文本 */
  noMoreDataElement?: React.ReactNode
  /** 是否显示没有更多数据提示，默认为true */
  showNoMoreData?: boolean
  /** 样式覆盖配置，可以自定义各部分的 className */
  overrides?: VirtualListScrollUpClassNames
  /** 自定义容器样式 */
  containerStyle?: React.CSSProperties
  /** 自定义样式 */
  styles?: VirtualListScrollUpStyles
}

/**
 * 暴露给外部的方法接口
 */
export interface VirtualListScrollUpRef {
  scrollToBottom: () => void
  scrollTo: (top: number) => void
}

/**
 * 虚拟滚动hook的配置选项接口
 */
interface UseVirtualScrollOptions<T> {
  /** 数据项数组 */
  items: T[]
  /** 每页显示数量 */
  pageSize: number
  /** 边缘触发阈值 */
  edgeThreshold: number
  /** 加载更多数据的回调 */
  onLoadMore?: () => void
  /** 是否还有更多数据 */
  hasMoreData?: boolean
  /** 是否启用自动滚动到底部 */
  autoScrollToBottom?: boolean
}

/**
 * 虚拟滚动自定义Hook
 * 提供虚拟滚动的核心逻辑，包括分页渲染、自动滚动、加载更多等功能
 */
function useVirtualScroll<T>({
  items,
  pageSize,
  edgeThreshold,
  onLoadMore,
  hasMoreData = true,
  autoScrollToBottom = true,
}: UseVirtualScrollOptions<T>) {
  // 滚动容器的引用
  const scrollRef = useRef<HTMLDivElement>(null)

  // 是否启用自动滚动状态
  const [autoScroll, setAutoScroll] = useState(autoScrollToBottom)

  // 是否触底状态
  const [hitBottom, setHitBottom] = useState(true)

  // 是否正在加载状态
  const [isLoading, setIsLoading] = useState(false)

  // 当前渲染的起始索引，默认从最后一页开始显示
  const [renderIndex, setRenderIndex] = useState(
    Math.max(0, items.length - pageSize),
  )

  /**
   * 滚动到底部的方法
   * 使用requestAnimationFrame确保DOM更新后再执行滚动
   */
  const scrollToBottom = useCallback(() => {
    const dom = scrollRef.current
    if (dom) {
      requestAnimationFrame(() => {
        setAutoScroll(true)
        // 将渲染索引设置为最后一页
        setRenderIndex(Math.max(0, items.length - pageSize))
        // 滚动到容器底部
        dom.scrollTo(0, dom.scrollHeight)
      })
    }
  }, [items.length, pageSize])

  /**
   * 保持滚动位置的方法
   * 当在顶部加载新数据时，维持用户当前的视觉位置
   */
  const maintainScrollPosition = useCallback(() => {
    const dom = scrollRef.current
    if (dom) {
      // 记录当前的滚动高度和位置
      const scrollHeight = dom.scrollHeight
      const scrollTop = dom.scrollTop

      requestAnimationFrame(() => {
        // 计算新增的高度差
        const newScrollHeight = dom.scrollHeight
        const heightDiff = newScrollHeight - scrollHeight
        // 调整滚动位置以保持视觉连续性
        dom.scrollTo(0, scrollTop + heightDiff)
      })
    }
  }, [])

  /**
   * 自动滚动效果
   * 当启用自动滚动且需要滚动到底部时执行
   */
  useEffect(() => {
    if (autoScroll && autoScrollToBottom) {
      scrollToBottom()
    }
  }, [autoScroll, autoScrollToBottom, scrollToBottom])

  /**
   * 处理数据长度变化的副作用
   * 根据不同情况决定是否需要滚动或保持位置
   */
  const lastItemsLength = useRef(items.length)
  useEffect(() => {
    const lengthDiff = items.length - lastItemsLength.current

    if (lengthDiff > 0) {
      // 新增了数据
      if (hitBottom && autoScrollToBottom) {
        // 如果在底部且启用自动滚动，滚动到新的底部
        scrollToBottom()
      } else if (renderIndex === 0 && lengthDiff > 0) {
        // 如果在顶部加载了更多数据，保持当前视觉位置
        maintainScrollPosition()
      }
    }

    // 更新上一次的数据长度
    lastItemsLength.current = items.length
  }, [
    items.length,
    hitBottom,
    autoScrollToBottom,
    renderIndex,
    scrollToBottom,
    maintainScrollPosition,
  ])

  /**
   * 安全设置渲染索引的方法
   * 确保索引在有效范围内
   */
  const setRenderIndexSafe = useCallback(
    (newIndex: number) => {
      // 计算最大有效索引
      const maxIndex = Math.max(0, items.length - pageSize)
      // 限制索引在有效范围内
      const safeIndex = Math.min(maxIndex, Math.max(0, newIndex))
      setRenderIndex(safeIndex)
    },
    [items.length, pageSize],
  )

  /**
   * 滚动事件处理函数
   * 处理虚拟滚动的核心逻辑，包括边缘检测和分页加载
   */
  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {

      const target = e.currentTarget
      const { scrollTop, clientHeight, scrollHeight } = target

      // 计算底部位置
      const bottomHeight = scrollTop + clientHeight

      // 判断是否触碰到顶部和底部边缘
      const isTouchTopEdge = scrollTop <= edgeThreshold
      const isTouchBottomEdge = bottomHeight >= scrollHeight - edgeThreshold
      const isAtBottom = bottomHeight >= scrollHeight - 20 // 10px的容错范围

      // 顶部边缘触发逻辑：向上加载更多内容
      if (isTouchTopEdge && !isTouchBottomEdge && !isLoading) {
        if (renderIndex > 0) {
          // 还有上一页数据可以显示，加载前一页（不需要检查 hasMoreData）
          const prevPageIndex = Math.max(0, renderIndex - pageSize)
          setRenderIndexSafe(prevPageIndex)
        } else if (onLoadMore && hasMoreData) {
          // 已经到达第一页且还有更多数据，需要从服务器加载更多历史数据
          setIsLoading(true)
          Promise.resolve(onLoadMore()).finally(() => {
            setIsLoading(false)
          })
        }
      }

      // 底部边缘触发逻辑：向下显示更多内容
      if (isTouchBottomEdge) {
        const nextPageIndex = renderIndex + pageSize
        setRenderIndexSafe(nextPageIndex)
      }

      // 更新底部状态和自动滚动状态
      setHitBottom(isAtBottom)
      setAutoScroll(isAtBottom)
    },
    [
      edgeThreshold,
      hasMoreData,
      isLoading,
      renderIndex,
      pageSize,
      onLoadMore,
      setRenderIndexSafe,
    ],
  )

  /**
   * 计算当前可见的数据项
   * 使用useMemo优化性能，只在依赖项变化时重新计算
   */
  const visibleItems = useMemo(() => {
    // 计算结束索引，显示当前页及前后各一页的数据（3倍pageSize）
    const endIndex = Math.min(renderIndex + 3 * pageSize, items.length)
    return items.slice(renderIndex, endIndex)
  }, [items, renderIndex, pageSize])

  // 返回hook的状态和方法
  return {
    scrollRef, // 滚动容器引用
    visibleItems, // 当前可见的数据项
    renderIndex, // 当前渲染起始索引
    hitBottom, // 是否触底状态
    autoScroll, // 自动滚动状态
    isLoading, // 加载状态
    handleScroll, // 滚动处理函数
    scrollToBottom, // 滚动到底部方法
    setAutoScroll, // 设置自动滚动状态
    setRenderIndex: setRenderIndexSafe, // 安全设置渲染索引
  }
}

/**
 * 虚拟滚动列表组件
 * 支持大量数据的高性能渲染，提供自动滚动、分页加载等功能
 */
export const VirtualListScrollUp = forwardRef<VirtualListScrollUpRef, VirtualListScrollUpProps<any>>(({
  items,
  renderItem,
  pageSize = 20,
  edgeThreshold = 100,
  onLoadMore,
  hasMoreData = true,
  loading = false,
  autoScrollToBottom = true,
  idExtractor,
  onScroll,
  children,
  scrollToBottomElement,
  showScrollToBottom = true,
  loadingElement,
  noMoreDataElement,
  showNoMoreData = true,
  overrides = {},
  styles = {},
}, ref) => {
  // 使用虚拟滚动hook获取所需的状态和方法
  const {
    scrollRef,
    visibleItems,
    renderIndex,
    hitBottom,
    handleScroll,
    scrollToBottom,
  } = useVirtualScroll({
    items,
    pageSize,
    edgeThreshold,
    onLoadMore,
    hasMoreData,
    autoScrollToBottom,
  })

  // 暴露给外部的方法
  useImperativeHandle(ref, () => ({
    scrollToBottom: () => {
      scrollToBottom()
    },
    scrollTo: (top: number) => {
      const dom = scrollRef.current
      if (dom) {
        dom.scrollTo(0, top)
      }
    }
  }))

  // 控制 ToBottomBtn 缩放动画的状态
  const [showButton, setShowButton] = useState(false)

  // 监听 hitBottom 变化，控制按钮的显示/隐藏动画
  useEffect(() => {
    setShowButton(!hitBottom && showScrollToBottom)
  }, [hitBottom, showScrollToBottom])

  /**
   * 带回调的滚动处理函数
   * 在内部处理滚动的同时，调用外部传入的滚动回调
   */
  const handleScrollWithCallback = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      // 先执行内部滚动逻辑
      handleScroll(e)

      // 如果有外部滚动回调，则调用它
      if (onScroll) {
        const target = e.currentTarget
        onScroll(target.scrollTop, hitBottom)
      }
    },
    [handleScroll, onScroll, hitBottom],
  )

  // 判断是否显示"没有更多数据"提示
  const shouldShowNoMoreData = showNoMoreData && !hasMoreData && !loading && items.length > 0

  return (
    <div
      ref={scrollRef}
      className={cn('virtual-scroll-up-list', overrides.container)}
      onScroll={handleScrollWithCallback}
      style={styles.container}
    >
      {/* 加载指示器：显示在列表顶部 */}
      {loading && (
        <div
          className={cn('loading-indicator', overrides.loadingIndicator)}
          style={styles.loadingIndicator}
        >
          {loadingElement || <Loading />}
        </div>
      )}

      {/* 没有更多数据提示：显示在列表顶部 */}
      {shouldShowNoMoreData && (
        <div
          className={cn(
            'no-more-data-indicator',
            overrides.noMoreData
          )}
          style={styles.noMoreData}
        >
          {noMoreDataElement || '没有更多消息了'}
        </div>
      )}

      {/* 渲染可见的列表项 */}
      {visibleItems.map((item, index) => (
        <div
          key={idExtractor(item) || index.toString()}
          className={cn('virtual-scroll-item', overrides.scrollItem)}
          style={styles.scrollItem}
        >
          {renderItem(item, renderIndex + index)}
        </div>
      ))}

      {/* 渲染子组件内容 */}
      {children}

      {/* 滚动到底部按钮：使用缩放动画控制显示/隐藏 */}
      <div
        className={cn('scroll-to-bottom', overrides.scrollToBottom)}
        style={{
          ...styles.scrollToBottom,
          transform: `scale(${showButton ? 1 : 0})`,
          transition: 'transform 0.2s ease-in-out',
          transformOrigin: 'center',
        }}
        onClick={scrollToBottom}
      >
        {scrollToBottomElement || <ToBottomButton />}
      </div>
    </div>
  )
})

export default VirtualListScrollUp
