import type { CopyPayload } from '@bty/util'
import { cn } from '@bty/util'
import type { FC } from 'react'
import { memo, useMemo, useState, useEffect } from 'react'
import type { Components, Options } from 'react-markdown'
import ReactMarkdown from 'react-markdown'
import rehypeKatex from 'rehype-katex'
import rehypeRaw from 'rehype-raw'
import RemarkBreaks from 'remark-breaks'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import type { Scrollbar } from 'smooth-scrollbar/interfaces'
import type { LayoutMode } from '@bty/hooks'
import { MarkdownCodeBlock } from './components/MarkdownCodeBlock'
import { useMarkdownSections } from './utils/useMarkdownSections'
import { removeStrikethrough } from './plugin/removeStrikethrough'
import renderCite from './plugin/renderCite'
import { escapeBrackets } from './utils/utils'
import {
  <PERSON>down<PERSON>,
  MarkdownH2,
  <PERSON>downH3,
  <PERSON><PERSON>H4,
  MarkdownH5,
} from './components/MarkdownH1'
import { MarkdownImageGrid } from './components/MarkdownImageGrid'
import { MarkdownLi } from './components/MarkdownLi'
import { MarkdownOl } from './components/MarkdownOl'
import { MarkdownParagraph } from './components/MarkdownParagraph'
import { MarkDownTable, MarkDownTableTd } from './components/MarkDownTable'
import { MarkdownImage } from './components/MarkdownImage'
import { MarkdownVideo } from './components/MarkdownVideo'
import type { MarkdownReference } from './types'
import { MarkdownAnchor } from './components/MarkdownAnchor'
import { MarkdownCite } from './components/MarkdownCite'
import { getComponentRender } from './utils/getComponentRender'

import 'katex/dist/katex.min.css'

const MemoizedReactMarkdown: FC<Options> = memo(
  ReactMarkdown,
  (prevProps, nextProps) =>
    prevProps.children === nextProps.children &&
    prevProps.className === nextProps.className,
)

interface MarkdownBaseProps {
  content: string // Markdown内容
  references?: MarkdownReference[] // 引用列表
  renderHtml?: boolean // 是否渲染HTML
  codeLoading?: boolean // 是否显示光标
  imagePreview?: boolean // 是否支持图片预览
  scrollBarRef?: { current?: Scrollbar } | undefined
  layoutMode?: LayoutMode
  onReferenceClick?: (item: MarkdownReference) => void // 引用点击处理函数
  onCopy?: (payload?: CopyPayload) => void
  onHrefClick?: (url: string) => void // 链接点击处理函数
  renders?: {
    cite?: Components['cite'] | boolean // 自定义引用渲染组件
    img?: Components['img'] | boolean
    a?: Components['a'] | boolean
    p?: Components['p'] | boolean
    li?: Components['li'] | boolean
    ol?: Components['ol'] | boolean

    code?: Components['code'] | boolean
    codeToolbar?: (params: {
      language: string
      code: string
      onCopy: (payload?: CopyPayload) => void
    }) => React.ReactNode // 自定义代码块工具栏

    h1?: Components['h1'] | boolean
    h2?: Components['h2'] | boolean
    h3?: Components['h3'] | boolean
    h4?: Components['h4'] | boolean
    h5?: Components['h5'] | boolean
    video?: Components['video'] | boolean
    table?: Components['table'] | boolean
    td?: Components['td'] | boolean
    em?: Components['em'] | boolean
  }
}

/**
 * Markdown消息渲染组件
 * 说明：
 * - 支持代码高亮、数学公式、图片预览、引用、HTML渲染等功能
 * - 通过分割图片网格和文本，支持多段内容混排
 * - 通过自定义渲染函数，满足业务定制需求
 */
export const MarkdownBase = memo(
  ({
    content,
    references = [],
    imagePreview = true,
    codeLoading = false,
    renderHtml = false,
    scrollBarRef,
    layoutMode = 'desktop',
    renders = {},
    onHrefClick,
    onReferenceClick,
    onCopy,
  }: MarkdownBaseProps) => {
    // 引用相关数据源逻辑
    const [searchData, setSearchData] = useState<MarkdownReference[]>([])
    const [setDataSourceList, appendSetDataSource] = useState<
      React.Dispatch<MarkdownReference[]>[]
    >([])

    useEffect(() => {
      if (!references.length) return
      setSearchData(references)
    }, [references])

    useEffect(() => {
      setDataSourceList.forEach(setDataSource => setDataSource?.(searchData))
    }, [setDataSourceList, searchData])

    // 转义后的内容，防止Markdown语法冲突
    const escapedContent = useMemo(() => escapeBrackets(content), [content])

    // Markdown段落状态，支持图片网格和文本混排
    const sections = useMarkdownSections(escapedContent)

    // 插件配置，根据是否允许HTML渲染动态配置插件
    const rehypePlugins = renderHtml
      ? [
          [rehypeKatex, { strict: false, throwOnError: false }], // 数学公式
          renderCite, // 引用渲染
          [rehypeRaw, { allowDangerousHtml: false }], // HTML渲染
        ]
      : [[rehypeKatex, { strict: false, throwOnError: false }], renderCite]

    const renderCiteComp: Components['cite'] = useMemo(() => {
      return props => {
        return (
          <MarkdownCite
            dataSource={searchData}
            onClick={onReferenceClick}
            appendSetDataSource={appendSetDataSource}
          >
            {props.children}
          </MarkdownCite>
        )
      }
    }, [appendSetDataSource, onReferenceClick, searchData])

    /**
     * 渲染代码块组件
     * 说明：
     * - 支持行内代码、代码块高亮、HTML渲染、图表渲染等
     * - 支持代码块复制、滚动条联动、iframe渲染等
     */
    const renderCode: Components['code'] = useMemo(() => {
      return ({ inline, className, children }) => {
        return (
          <MarkdownCodeBlock
            scrollBarRef={scrollBarRef}
            inline={inline}
            className={className}
            codeLoading={codeLoading}
            onCopy={onCopy}
            renderToolbar={({ language, code, onCopy }) =>
              renders.codeToolbar
                ? renders.codeToolbar({ language, code, onCopy })
                : null
            }
          >
            {children}
          </MarkdownCodeBlock>
        )
      }
    }, [codeLoading, renders.codeToolbar])

    // 渲染链接，支持点击、图片预览、复制
    const renderAnchor: Components['a'] = useMemo(
      () => props => (
        <MarkdownAnchor
          {...props}
          onCopy={onCopy}
          onClick={onHrefClick}
          imagePreview={imagePreview}
        />
      ),
      [imagePreview],
    )

    // 渲染图片，支持预览、复制
    const renderImage: Components['img'] = useMemo(
      () => props => {
        return (
          <MarkdownImage
            layoutMode={layoutMode}
            src={props.src || ''}
            {...props}
            preview={imagePreview}
            onCopy={onCopy}
          />
        )
      },
      [imagePreview],
    )

    // 渲染视频，支持自定义视频组件
    const renderVideo: Components['video'] = useMemo(
      () => props => {
        return <MarkdownVideo src={props.src || ''} />
      },
      [],
    )

    // 渲染段落，支持复制
    const renderParagraph: Components['p'] = useMemo(
      () => props => {
        return (
          <MarkdownParagraph onCopy={onCopy}>
            {props.children}
          </MarkdownParagraph>
        )
      },
      [],
    )

    // 渲染表格，支持复制
    const renderTable: Components['table'] = useMemo(() => {
      return props => {
        return <MarkDownTable onCopy={onCopy}>{props.children}</MarkDownTable>
      }
    }, [])

    const citeComp = getComponentRender(renders.cite, renderCiteComp)

    const imgComp = getComponentRender(renders.img, renderImage)

    const aComp = getComponentRender(renders.a, renderAnchor)

    const pComp = getComponentRender(renders.p, renderParagraph)

    const liComp = getComponentRender(renders.li, MarkdownLi)

    const olComp = getComponentRender(renders.ol, MarkdownOl)

    const codeComp = getComponentRender(renders.code, renderCode)

    const h1Comp = getComponentRender(renders.h1, MarkdownH1)

    const h2Comp = getComponentRender(renders.h2, MarkdownH2)

    const h3Comp = getComponentRender(renders.h3, MarkdownH3)

    const h4Comp = getComponentRender(renders.h4, MarkdownH4)

    const h5Comp = getComponentRender(renders.h5, MarkdownH5)

    const videoComp = getComponentRender(renders.video, renderVideo)

    const tableComp = getComponentRender(renders.table, renderTable)

    const tdComp = getComponentRender(renders.td, MarkDownTableTd)

    const emComp = getComponentRender(renders.em, ({ children }) => (
      <span>*{children[0]}*</span>
    ))

    return sections.map((section, index) => {
      // 渲染图片网格
      if (section.type === 'imageGrid' && section.images) {
        return (
          <MarkdownImageGrid
            key={index}
            images={section.images}
            onCopy={onCopy}
            imagePreview={imagePreview}
            layoutMode={layoutMode}
          />
        )
      } else if (section.type === 'text' && section.content) {
        return (
          <MemoizedReactMarkdown
            key={index}
            className={cn(
              'chat-markdown-message ui-prose ui-max-w-none ui-break-words ui-markdown ui-break-all ui-overflow-y-hidden [&_.katex-display]:ui-overflow-x-auto [&_.katex-display]:ui-overflow-y-hidden',
              codeLoading ? 'result-streaming' : '',
            )}
            components={{
              cite: citeComp, // 支持外部自定义cite
              a: aComp,
              p: pComp,
              li: liComp,
              ol: olComp,
              img: imgComp,
              code: codeComp,
              h1: h1Comp,
              h2: h2Comp,
              h3: h3Comp,
              h4: h4Comp,
              h5: h5Comp,
              video: videoComp,
              table: tableComp,
              td: tdComp,
              em: emComp,
            }}
            rehypePlugins={rehypePlugins as any}
            remarkPlugins={[
              remarkGfm,
              [remarkMath, { singleDollarTextMath: false }], // 数学公式
              removeStrikethrough, // 删除线处理
              RemarkBreaks, // 换行处理
            ]}
            remarkRehypeOptions={{
              footnoteLabel: '脚注',
              footnoteBackLabel: null,
              footnoteBackContent: null,
            }}
            linkTarget='_blank' // 链接打开方式
          >
            {section.content}
          </MemoizedReactMarkdown>
        )
      }
      return null
    })
  },
)
