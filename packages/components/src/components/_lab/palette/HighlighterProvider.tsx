import { createContext } from 'react'
import type { HighlighterCore } from 'shiki/core'
import { useHighlighter } from './useHighlighter'

export const HighlighterContext = createContext<HighlighterCore | null>(null)

type HighlighterProviderProps = React.PropsWithChildren

export function HighlighterProvider({ children }: HighlighterProviderProps) {
  const highlighter = useHighlighter()

  return (
    <HighlighterContext.Provider value={highlighter}>
      {children}
    </HighlighterContext.Provider>
  )
}
