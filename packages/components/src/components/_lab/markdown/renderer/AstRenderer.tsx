import type { Root } from 'mdast'
import { Heading<PERSON>ender<PERSON> } from './HeadingRenderer'
import { ListRenderer } from './ListRenderer'
import { CodeRenderer } from './CodeRenderer'
import { ParagraphRenderer } from './ParagraphRenderer'
import { TableRenderer } from './TableRenderer'
import { BlockquoteRenderer } from './BlockquoteRenderer'

export function AstRenderer({ ast }: { ast: Root }) {
  const nodes = ast.children

  const children = nodes.map((node, index) => {
    switch (node.type) {
      case 'html':
        return (
          <div
            key={`html-${index}`}
            dangerouslySetInnerHTML={{ __html: node.value }}
          />
        )
      case 'heading':
        return (
          <HeadingRenderer
            key={`heading-${index}`}
            ast={node}
            depth={node.depth}
          />
        )
      case 'paragraph':
        return <ParagraphRenderer key={`paragraph-${index}`} ast={node} />
      case 'list':
        return <ListRenderer key={`list-${index}`} ast={node} />
      case 'code':
        return <CodeRenderer key={`code-${index}`} ast={node} />
      case 'table':
        return <TableRenderer key={`table-${index}`} ast={node} />
      case 'blockquote':
        return <BlockquoteRenderer key={`blockquote-${index}`} ast={node} />
      case 'thematicBreak':
        return (
          <hr
            key={`thematicBreak-${index}`}
            className='my-8 border-none h-px bg-[#d1d9e0]'
          />
        )
      default:
        return null
    }
  })

  return <>{children}</>
}
