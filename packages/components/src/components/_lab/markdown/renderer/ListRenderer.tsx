import type { List, ListItem } from 'mdast'
import type { VariantProps } from 'class-variance-authority'
import { cva } from 'class-variance-authority'
import { cn } from '@bty/util'
import { CommonRenderer } from './CommonRenderer'
import { CodeRenderer } from './CodeRenderer'

function ListItemRenderer({ ast }: { ast: ListItem }) {
  const showCheckbox = typeof ast.checked === 'boolean'

  const children = ast.children.map((child, index) => {
    switch (child.type) {
      case 'paragraph': {
        const { checked } = ast
        const pTag = (
          <p
            className={cn(
              'leading-[1.75]',
              showCheckbox ? '!m-0' : 'mb-[0.5em]',
            )}
          >
            <CommonRenderer ast={child} />
          </p>
        )

        if (showCheckbox) {
          return (
            <div className='flex items-start gap-8px'>
              <input
                className='!mt-[7.5px] accent-[#7b61ff] pointer-events-none'
                type='checkbox'
                checked={checked}
              />
              {pTag}
            </div>
          )
        }

        return pTag
      }
      case 'html':
        return (
          <div
            key={`html-${index}`}
            dangerouslySetInnerHTML={{ __html: child.value }}
          />
        )
      case 'list':
        return <ListRenderer key={`list-${index}`} ast={child} />
      case 'code':
        return <CodeRenderer key={`code-${index}`} ast={child} />
      default:
        return null
    }
  })

  return (
    <li
      className={cn(
        'ps-[0.375em] my-[0.5em] break-words [word-break:break-word]',
        { 'list-none': showCheckbox },
      )}
    >
      {children}
    </li>
  )
}

const listStyles = cva('mt-0 mb-[1.25em] ps-[1.625em]', {
  variants: {
    listType: {
      ordered: 'list-decimal',
      unordered:
        'list-disc [&_:is(ul)]:list-circle [&_:is(ul)_:is(ul)]:list-square',
    },
  },
  defaultVariants: {
    listType: 'unordered',
  },
})

export interface ListRendererProps extends VariantProps<typeof listStyles> {
  ast: List
}

export function ListRenderer({ ast }: ListRendererProps) {
  const { ordered, start, children } = ast
  const listType = ordered ? 'ordered' : 'unordered'

  return ordered ? (
    <ol className={listStyles({ listType })} start={start}>
      {children.map((child, index) => (
        <ListItemRenderer key={index} ast={child} />
      ))}
    </ol>
  ) : (
    <ul className={listStyles({ listType })}>
      {children.map((child, index) => (
        <ListItemRenderer key={index} ast={child} />
      ))}
    </ul>
  )
}
