import type { Table as TableAst } from 'mdast'
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from '../../_components/Table'
import { CommonRenderer } from './CommonRenderer'

interface TableRendererProps {
  ast: TableAst
}

export function TableRenderer({ ast }: TableRendererProps) {
  const tableHead = ast.children[0]
  const tableBody = ast.children.slice(1)

  return (
    <Table className='min-w-full w-max max-w-max border border-solid border-[rgba(0,0,0,0.08)] border-collapse border-spacing-0 rounded-[8px] [font-variant:tabular-nums]'>
      {tableHead ? (
        <TableHeader>
          <TableRow className='border-none bg-white'>
            {tableHead.children.map((cell, index) => (
              <TableHead
                key={index}
                className='font-semibold text-left border-none px-[14px] py-[10px] bg-[#f5f5f5]'
              >
                <CommonRenderer ast={cell} />
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
      ) : null}
      {tableBody ? (
        <TableBody>
          {tableBody.map((row, rowIndex) => (
            <TableRow
              key={rowIndex}
              className='border-none bg-white nth-child(2n):bg-[#f5f5f5]'
            >
              {row.children.map((cell, cellIndex) => (
                <TableCell
                  key={cellIndex}
                  className='min-w-100px max-w-[max(30vw,320px)] px-[14px] py-[10px] border-none text-[#0009]'
                >
                  <CommonRenderer ast={cell} />
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      ) : null}
    </Table>
  )
}
