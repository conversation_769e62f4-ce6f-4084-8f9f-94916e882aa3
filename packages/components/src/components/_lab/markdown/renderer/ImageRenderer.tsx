import type { Image } from 'mdast'
import { Image as AntdImage } from 'antd'
import { useRenderer } from './RendererProvider'

export function ImageRenderer({ ast }: { ast: Image }) {
  const { rendererOptions } = useRenderer()

  const { url, alt, title } = ast

  if (alt?.startsWith('Video')) {
    return (
      <div className='video-container'>
        <video className='w-full' src={url} controls />

        <div className='controls'>
          <button id='playPause' className='btn'>
            ▶
          </button>
          <div className='progress' id='progress'>
            <div className='progress-bar' id='progress-bar'></div>
          </div>
          <button id='mute' className='btn'>
            🔊
          </button>
          <button id='fullscreen' className='btn'>
            ⛶
          </button>
        </div>
      </div>
    )
  }

  const previewable = rendererOptions?.image?.previewable

  return (
    <AntdImage
      wrapperClassName='[&>.ant-image-mask]:hidden cursor-pointer'
      referrerPolicy='no-referrer'
      preview={
        previewable === false
          ? false
          : { rootClassName: '[&_.ant-image-preview-img]:max-h-[90%]!' }
      }
      alt={alt}
      src={url}
      title={title}
    />
  )
}
