import type { Code } from 'mdast'
import React, { useState } from 'react'
import { Prism as SyntaxHighlighter } from '@bty/async-loader/react-syntax-highlighter'
import { oneLight } from 'react-syntax-highlighter/dist/esm/styles/prism'
import { CopyIcon, SuccessTwotoneIcon } from '@bty/react-icons'
import copy from 'copy-to-clipboard'
import { ExperimentalButton } from '../../button'
import { useRenderer } from './RendererProvider'

const CodeBanner = React.memo(
  ({ lang, value }: { lang: string; value: string }) => {
    const [isCopied, setIsCopied] = useState(false)

    const handleCopy = () => {
      copy(value)
      setIsCopied(true)
      setTimeout(() => {
        setIsCopied(false)
      }, 1000)
    }
    return (
      <div className='h-[40px] px-[14px] flex justify-between items-center text-[12px] rounded-t-[12px] bg-[#f5f5f5]'>
        <span>{lang}</span>
        {isCopied ? (
          <div className='w-[24px] h-[24px] flex items-center justify-center'>
            <SuccessTwotoneIcon />
          </div>
        ) : (
          <ExperimentalButton
            className='text-[#8d8d99] !md:hover:bg-[#626999]/[0.08] !md:hover:text-[#8d8d99]'
            variant='ghost'
            size='sm-icon'
            onClick={handleCopy}
          >
            <CopyIcon />
          </ExperimentalButton>
        )}
      </div>
    )
  },
)

function InnnerCodeRenderer({ ast }: { ast: Code }) {
  const { lang, value } = ast

  const { rendererOptions } = useRenderer()

  const copyable = rendererOptions?.code?.copyable

  const customStyle: React.CSSProperties = {
    borderRadius: copyable ? '0 0 12px 12px' : 12,
    fontSize: 14,
    margin: copyable ? '0' : '0 0 16px 0',
  }

  const codeElement = (
    <SyntaxHighlighter
      customStyle={customStyle}
      language={lang}
      style={oneLight}
      PreTag='div'
    >
      {value.replace(/\n$/, '')}
    </SyntaxHighlighter>
  )

  if (copyable) {
    return (
      <div className='mb-[16px]'>
        <CodeBanner lang={lang} value={value} />
        {codeElement}
      </div>
    )
  }

  return codeElement
}

function InlineCodeRenderer({ children }: { children: string }) {
  return (
    <code className='break-normal font-[ui-monospace,monospace] text-[0.875em] px-[0.4em] py-[0.2em] rounded-[6px] bg-[#818b981f] whitespace-break-spaces'>
      {children}
    </code>
  )
}

type Computed = typeof InnnerCodeRenderer & {
  Inline: typeof InlineCodeRenderer
}

const CodeRenderer = InnnerCodeRenderer as Computed
CodeRenderer.Inline = InlineCodeRenderer

export { CodeRenderer }
