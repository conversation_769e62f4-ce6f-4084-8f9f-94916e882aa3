import 'remark-directive'
import type { Parent } from 'mdast'
import React from 'react'
import { TextRenderer } from './TextRenderer'
import { Code<PERSON>enderer } from './CodeRenderer'
import { AnchorRenderer } from './AnchorRenderer'
import { ImageRenderer } from './ImageRenderer'
import { useRenderer } from './RendererProvider'

export function CommonRenderer({ ast }: { ast: Parent }) {
  const { renderer } = useRenderer()

  const children = ast.children.map((child, index) => {
    switch (child.type) {
      case 'text':
        return <TextRenderer key={`text-${index}`}>{child.value}</TextRenderer>
      case 'link':
        return <AnchorRenderer key={`link-${index}`} ast={child} />
      case 'image':
        return <ImageRenderer key={`image-${index}`} ast={child} />
      case 'textDirective': {
        if (child.name !== 'cite') {
          return null
        }

        const content =
          child.children?.[0]?.type === 'text'
            ? child.children[0].value || ''
            : ''
        const cite = renderer?.cite

        if (Array.isArray(cite)) {
          const [Cite, props] = cite
          return React.createElement(
            Cite,
            {
              key: `cite-${index}`,
              ...props,
            },
            content,
          )
        } else if (cite) {
          return React.createElement(
            cite,
            {
              key: `cite-${index}`,
            },
            content,
          )
        }

        return (
          <cite
            key={`cite-${index}`}
            data-ref={content}
            className='citation ui-mx-[2px] ui-cursor-pointer ui-inline-block ui-w-[14px] ui-h-[14px] ui-text-center ui-text-[12px] ui-leading-[14px] ui-bg-[#626999] ui-bg-opacity-[0.12] ui-rounded-[4px] ui-text-[#b2b5ce]'
          >
            {content}
          </cite>
        )
      }
      case 'strong': {
        // &nbsp; 用来避免错误换行
        const children = <CommonRenderer ast={child} />
        const strong = renderer?.strong

        if (Array.isArray(strong)) {
          const [Strong, props] = strong
          return React.createElement(
            Strong,
            {
              key: `strong-${index}`,
              ...props,
            },
            children,
          )
        }

        return (
          <>
            &nbsp;
            {React.createElement(
              strong || 'strong',
              {
                key: `strong-${index}`,
                className: 'font-semibold',
              },
              children,
            )}
            &nbsp;
          </>
        )
      }
      case 'emphasis':
        return (
          <em key={`emphasis-${index}`}>
            <CommonRenderer ast={child} />
          </em>
        )
      case 'break':
        return <br />
      case 'inlineCode':
        return (
          <CodeRenderer.Inline key={`inline-code-${index}`}>
            {child.value}
          </CodeRenderer.Inline>
        )
      default:
        return null
    }
  })

  return <>{children}</>
}
