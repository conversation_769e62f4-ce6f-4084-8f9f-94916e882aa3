import { createContext, useContext, useMemo } from 'react'

// 基础 props 类型，包含 markdown 渲染器通用的 props
interface BaseRendererProps {
  className?: string
}

// 提取组件额外的 props 类型（排除基础 props）
type ExtractAdditionalProps<T> =
  T extends React.ComponentType<infer P>
    ? Omit<P, keyof BaseRendererProps>
    : never

// 通用渲染器组件的类型定义
type ComponentRenderer<
  T extends React.ComponentType<
    BaseRendererProps & any
  > = React.ComponentType<BaseRendererProps>,
> = T | [T, ExtractAdditionalProps<T>]

export interface RendererContextValue {
  cite?: ComponentRenderer
  strong?: ComponentRenderer
}

const RendererContext = createContext<{
  renderer?: RendererContextValue
  rendererOptions?: {
    code?: {
      copyable?: boolean
    }
    image?: {
      previewable?: boolean
    }
  }
} | null>(null)

type RendererProviderProps = React.PropsWithChildren<{
  renderer?: RendererContextValue
  rendererOptions?: {
    code?: {
      copyable?: boolean
    }
    image?: {
      previewable?: boolean
    }
  }
}>

export function RendererProvider({
  renderer,
  children,
  rendererOptions,
}: RendererProviderProps) {
  const value = useMemo(
    () => ({
      renderer,
      rendererOptions,
    }),
    // todo: stable value
    [renderer],
  )

  return (
    <RendererContext.Provider value={value}>
      {children}
    </RendererContext.Provider>
  )
}

export function useRenderer() {
  const context = useContext(RendererContext)
  return context
}
