import type { Root, Text, Parent } from 'mdast'
import { visit } from 'unist-util-visit'

/**
 * 将 [xxx] 格式的引用转换为 textDirective 节点
 * 支持标准的 markdown directive 语法
 */
export function remarkCite() {
  return function transformer(tree: Root) {
    // 将 [数字] 转换为 textDirective 节点
    visit(
      tree,
      'text',
      (node: Text, index: number | null, parent: Parent | null) => {
        if (!node.value || !parent || index === null) {
          return
        }

        // 检查是否包含 [数字] 格式的引用
        const bracketRegex = /\[(\d+)\]/g
        if (bracketRegex.test(node.value)) {
          // 重置正则表达式
          bracketRegex.lastIndex = 0

          const parts = []
          let lastIndex = 0
          let match = bracketRegex.exec(node.value)

          while (match !== null) {
            const startIndex = match.index
            const fullMatch = match[0]
            const content = match[1]

            // 添加匹配前的文本
            if (startIndex > lastIndex) {
              parts.push({
                type: 'text',
                value: node.value.slice(lastIndex, startIndex),
              })
            }

            // 添加 textDirective 节点
            parts.push({
              type: 'textDirective',
              name: 'cite',
              attributes: {},
              children: [
                {
                  type: 'text',
                  value: content,
                },
              ],
            })

            lastIndex = startIndex + fullMatch.length
            match = bracketRegex.exec(node.value)
          }

          // 添加剩余文本
          if (lastIndex < node.value.length) {
            parts.push({
              type: 'text',
              value: node.value.slice(lastIndex),
            })
          }

          // 替换节点
          if (parts.length > 0) {
            parent.children.splice(index, 1, ...(parts as any))
          }
        }
      },
    )
  }
}
