/**
 * 规范化不规范的 Markdown 内容
 * 主要处理以下问题：
 * 1. 图片和标题之间缺少换行符
 * 2. 连续的加粗标签
 * 3. 链接和标题之间缺少换行符
 * 4. 其他常见的格式问题
 */
export const normalizeMarkdown = (content: string): string => {
  if (!content) return content

  let normalized = content.trim()

  // 1. 处理图片后直接跟标题的情况
  // 匹配模式：![alt](url)## 标题 或 ![alt](url)# 标题
  normalized = normalized.replace(
    /(\!\[([^\]]*)\]\(([^)]+)\))(#{1,6}\s+)/g,
    '$1\n\n$4',
  )

  // 2. 处理链接后直接跟标题的情况
  // 匹配模式：[text](url)## 标题 或 [text](url)# 标题
  normalized = normalized.replace(
    /(\[([^\]]*)\]\(([^)]+)\))(#{1,6}\s+)/g,
    '$1\n\n$4',
  )

  // 3. 处理连续的加粗标签
  // 将 **text1****text2** 规范化为 **text1** **text2**
  normalized = normalized.replace(/(\*\*[^*]+\*\*)(\*\*)/g, '$1 $2')

  return normalized
}
