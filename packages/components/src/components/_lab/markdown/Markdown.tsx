import { cn } from '@bty/util'
import React, { useMemo, useState, useEffect, useImperativeHandle } from 'react'
import { unified } from 'unified'
import remarkParse from 'remark-parse'
import remarkGfm from 'remark-gfm'
import type { Root } from 'mdast'
import { escapeBrackets } from '../../markdown/utils/utils'
import type { ClassNames } from '../_utils/styleUtils'
import { normalizeMarkdown } from './utils/normalizeMarkdown'
import 'katex/dist/katex.min.css'
import { AstRenderer } from './renderer/AstRenderer'
import {
  type RendererContextValue,
  RendererProvider,
} from './renderer/RendererProvider'
import { remarkCite } from './plugins/remarkCite'

export interface MarkdownProps {
  overrides?: {
    root?: ClassNames
  }
  /**
   * 需要解析的 markdown 内容
   */
  content?: string
  /**
   * 从远端下载文件内容并解析，`remote` 开启时生效
   */
  url?: string
  /**
   * 是否从远端下载文件内容并解析
   * @default false
   */
  remote?: boolean
  /**
   * 每一个节点的渲染器
   */
  as?: RendererContextValue
  /**
   * 是否允许复制代码块内容
   * @default false
   */
  codeCopyable?: boolean
  /**
   * 是否支持图片预览
   * @default true
   */
  imagePreviewable?: boolean
  /**
   * 加载中指示器
   * @default null
   */
  loadingIndicator?: React.ReactNode
  /**
   * 需要解析的 markdown 内容
   */
  children?: string
}

export interface MarkdownRef {
  getContent: () => string | undefined
}

function InnerMarkdown(
  {
    overrides,
    content: contentProp,
    url,
    remote = false,
    as,
    codeCopyable = false,
    imagePreviewable = true,
    loadingIndicator = null,
    children: childrenProp,
  }: MarkdownProps,
  ref: React.Ref<MarkdownRef>,
) {
  const [loading, setLoading] = useState(false)

  const [content, setContent] = useState(
    remote ? undefined : contentProp || childrenProp || '',
  )

  useEffect(() => {
    // 设置300ms延迟才显示loading状态
    let isCompleted = false
    let loadingTimer: number | null = null

    if (remote && url) {
      loadingTimer = window.setTimeout(() => {
        if (!isCompleted) {
          setLoading(true)
        }
      }, 300)

      fetch(url)
        .then(response => response.text())
        .then(setContent)
        .finally(() => {
          setLoading(false)
          isCompleted = true
          if (loadingTimer) {
            window.clearTimeout(loadingTimer)
          }
        })
    }

    // 确保清理函数总是被返回，即使没有执行fetch操作
    return () => {
      isCompleted = true
      if (loadingTimer) {
        window.clearTimeout(loadingTimer)
      }
    }
    // ignore remote
  }, [url])

  useEffect(() => {
    if (remote) {
      return
    }
    setContent(contentProp || '')
  }, [contentProp])

  // 转义后的内容，防止Markdown语法冲突
  const normalizedContent = useMemo(
    () => escapeBrackets(normalizeMarkdown(content)),
    [content],
  )

  const { ast } = useMemo(() => {
    const processor = unified()
      .use(remarkParse)
      .use(remarkCite)
      .use(remarkGfm, { singleTilde: false })
    // .use(remarkRehype, { allowDangerousHtml: true })
    const ast = processor.runSync(processor.parse(normalizedContent))
    return { ast }
  }, [normalizedContent])

  useImperativeHandle(ref, () => ({
    getContent: () => {
      return content
    },
  }))

  return (
    <div
      className={cn(
        'bty-mark-content relative text-[1rem]/[1.75] text-justify [&>:first-child]:!mt-0      chat-markdown-message ui-prose max-w-none break-words ui-markdown ui-break-all [&_.katex-display]:ui-overflow-x-auto [&_.katex-display]:ui-overflow-y-hidden',
        overrides?.root,
      )}
    >
      {loading && loadingIndicator}
      <RendererProvider
        renderer={as}
        rendererOptions={{
          code: {
            copyable: codeCopyable,
          },
          image: {
            previewable: imagePreviewable,
          },
        }}
      >
        <AstRenderer ast={ast as Root} />
      </RendererProvider>
    </div>
  )
}

export const Markdown = React.forwardRef(InnerMarkdown)
