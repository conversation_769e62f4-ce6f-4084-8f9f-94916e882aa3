import React, { useState, useEffect, useImperative<PERSON>andle } from 'react'
import { parse } from 'papaparse'
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from '../../_components/Table'
import type { ClassNames } from '../../_utils/styleUtils'

export interface CSVPreviewProps {
  overrides?: {
    root?: ClassNames
  }
  url?: string
  content?: string
  loadingIndicator?: React.ReactNode
}

export interface CSVPreviewRef {
  getContent: () => string | undefined
}

function InnerCSVPreview(
  { overrides, url, content, loadingIndicator }: CSVPreviewProps,
  ref: React.Ref<CSVPreviewRef>,
) {
  const [loading, setLoading] = useState(true)

  const [_, setError] = useState<string | null>(null)

  const [headers, setHeaders] = useState<string[]>([])

  const [rows, setRows] = useState<Record<string, unknown>[]>([])

  const downloadAndParseData = () => {
    parse(url, {
      header: true,
      worker: true,
      preview: 101,
      download: true,
      delimiter: ',',
      skipEmptyLines: true,
      complete: results => {
        if (results.errors.length > 0) {
          return
        }
        const headers = results.meta.fields
        setHeaders(headers)
        const rows = results.data as Record<string, unknown>[]
        setRows(rows)

        setLoading(false)
      },
      error: () => {},
    })
  }

  const parseData = () => {
    parse(content, {
      header: true,
      worker: true,
      preview: 101,
      delimiter: ',',
      skipEmptyLines: true,
      complete: results => {
        if (results.errors.length > 0) {
          return
        }
        const headers = results.meta.fields
        setHeaders(headers)
        const rows = results.data as Record<string, unknown>[]
        setRows(rows)

        setLoading(false)
      },
      error: () => {},
    })
  }

  useEffect(() => {
    if (url) {
      setError(null)
      downloadAndParseData()
      return
    }

    if (content) {
      setError(null)
      parseData()
    }
  }, [url, content])

  useImperativeHandle(ref, () => ({
    getContent: () => {
      return `${headers.join(',')}\n${rows
        .map(row => Object.values(row).join(','))
        .join('\n')}`
    },
  }))

  if (loading) {
    return loadingIndicator
  }

  return (
    <Table className={overrides?.root}>
      <TableHeader>
        <TableRow>
          {headers.map((header, index) => (
            <TableHead key={index} className='min-w-[120px]'>
              {header}
            </TableHead>
          ))}
        </TableRow>
      </TableHeader>
      <TableBody>
        {rows.map((row, rowIndex) => (
          <TableRow key={rowIndex}>
            {headers.map((header, cellIndex) => (
              <TableCell key={cellIndex} className='max-w-[200px] truncate'>
                {String(row[header])}
              </TableCell>
            ))}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}

export const CSVPreview = React.forwardRef(InnerCSVPreview)
