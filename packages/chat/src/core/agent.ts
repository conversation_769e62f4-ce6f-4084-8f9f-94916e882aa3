import type { AgentDetailResponse } from '@bty/global-types/agent'
import type { ApiClient } from './apiClient'
import { EventCallBack } from './runtime/eventCallBack'
import { observe } from './runtime/observer'
import type { <PERSON>rror<PERSON>andle } from './types/error'
import { Model, type ModelData } from './model'

interface AgentOptionsWithId {
  id: string
}

interface AgentOptionsWithDetail {
  agentConfig: AgentDetailResponse
}

export type AgentOptions = AgentOptionsWithId | AgentOptionsWithDetail

type AgentEvent = 'onAgentLoaded'
type PropertyListenerEvent = 'loading' | 'agentConfig'
type Event = AgentEvent | PropertyListenerEvent

export class Agent extends EventCallBack<Event> {
  id: string

  agentConfig: AgentDetailResponse | undefined

  agentFileSupport: {
    fileAccepts: string[]
    allowFileTypeNameList: string[]
  }

  agentLLMModel: ModelData | undefined

  loading = true

  private model: Model

  constructor(
    options: AgentOptions,
    private readonly apiClient: ApiClient,
    private readonly onError: <PERSON>rror<PERSON>and<PERSON>,
  ) {
    super()
    this.model = new Model(apiClient, onError)
    if ('id' in options) {
      this.id = options.id
      this.loading = true
    } else {
      this.agentConfig = options.agentConfig
      this.id = options.agentConfig.application.flowId
      this.loading = false
    }

    return observe(this, this.emit.bind(this), [
      'loading',
      'agentConfig',
      'agentFileSupport',
    ])
  }

  async init() {
    await Promise.all([this.initAgentInfo(), this.initAgentUploadFileSupport()])
  }

  async reload() {
    await Promise.all([
      this.reloadAgentInfo(),
      this.reloadAgentUploadFileSupport(),
    ])
  }

  async reloadAgentInfo() {
    this.loading = true
    try {
      const res = await this.apiClient.chatApi.Agent.queryAgentById(this.id)
      this.agentConfig = res.data

      // 副作用，还没想好放哪，先放这里
      if (!this.apiClient.auth.workspaceId) {
        this.apiClient.updateAuth({
          workspaceId: res.data.workspace_id,
        })
      }
      if (this.agentConfig.rule?.free_model) {
        this.reloadAgentLLMModelList()
      }
      this.loading = false
      this.emit('onAgentLoaded')
    } catch (e: any) {
      this.onError({
        type: 'API_ERROR',
        module: 'agent',
        content: e,
      })
    }
  }

  async initAgentInfo() {
    if (!this.agentConfig) {
      this.reloadAgentInfo()
    } else if (this.agentConfig.rule?.free_model) {
      this.reloadAgentLLMModelList()
    }
  }

  async reloadAgentUploadFileSupport() {
    try {
      const res =
        await this.apiClient.chatApi.Agent.getUploadFileSupportFileTypes(
          this.id,
        )
      const config = res?.data ?? []

      const fileAccepts = new Set<string>()
      const allowFileTypeNameList = new Set<string>()

      config.forEach(item => {
        allowFileTypeNameList.add(item.name)
        item.support.forEach(type => fileAccepts.add(type))
        item.extension.forEach(type => fileAccepts.add(type))
      })

      this.agentFileSupport = {
        fileAccepts: Array.from(fileAccepts),
        allowFileTypeNameList: Array.from(allowFileTypeNameList),
      }
    } catch (e: any) {
      this.onError({
        type: 'API_ERROR',
        module: 'agent',
        content: e,
      })
    }
  }

  async reloadAgentLLMModelList() {
    if (!this.agentConfig?.rule?.free_model) {
      return
    }

    try {
      this.agentLLMModel = await this.model.reloadLLMModelList()
    } catch (e) {
      console.error(e)
    }
  }

  async initAgentUploadFileSupport() {
    if (!this.agentFileSupport) {
      this.reloadAgentUploadFileSupport()
    }
  }
}
