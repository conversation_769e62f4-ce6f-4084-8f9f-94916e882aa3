import { sse } from '@bty/http-client'
import type { ChatStream } from '@bty/global-types/message'
import type { ApiClient } from '../apiClient'
import type { Conversation } from '../conversation'
import type {
  AssistantMessage,
  SendMessagePayload,
  UserMessage,
} from '../types/message'
import {
  generalAssistantMessage,
  generalUserMessage,
  processKnowledge,
  processRelateQuestion,
  processSkills,
  processSuggestPlugin,
} from '../utils/messsage'
import { ASSISTANT_ID_PREFIX, USER_ID_PREFIX } from '../constants/message'
import type { ChatUI } from '../ui'
import { nextTick, sleep } from '../../utils/eventLoop'
import { onTargetDomSizeChange, waitImagesLoaded } from '../../utils/dom'
import { EventCallBack } from '../runtime/eventCallBack'
import { observe } from '../runtime/observer'
import type { ChatUtil } from '../util'
import type { FormSkill } from '../types/skill'
import type { ErrorHandle } from '../types/error'
import { MessageHistory } from './messageHistory'
import { MessageInput } from './messageInput'
import type { RemoteMessage } from './remoteMessage'

const EVENT_SOURCE_TIMEOUT = 120 * 1000
const TOOL_TIMEOUT = 300 * 1000

type PropertyListenerEvent = 'generating'
type MessageEvent = 'onChatEnd' | 'onMessage' | 'onMessageInit'
type Event = MessageEvent | PropertyListenerEvent

export class Message extends EventCallBack<Event> {
  generating = false

  messageHistory: MessageHistory

  messageInput: MessageInput

  private hasNewRemoteMessage = false

  private abortCurrentSSE: () => void | undefined
  private currentMessageTimeout: NodeJS.Timeout | null = null
  private currentSkillTimeout: NodeJS.Timeout | null = null

  constructor(
    private readonly agentId: string,
    private readonly conversation: Conversation,
    private readonly apiClient: ApiClient,
    private readonly remoteMessage: RemoteMessage,
    private readonly util: ChatUtil,
    private readonly ui: ChatUI,
    private readonly onError: ErrorHandle,
  ) {
    super()
    this.messageHistory = new MessageHistory(
      this.conversation,
      this.apiClient,
      this.onError,
    )
    this.messageInput = new MessageInput(this.util)
    this.remoteMessage.on(
      'remoteMessageData',
      this.onRemoteMessageUpdate.bind(this),
    )

    return observe(this, this.emit.bind(this), ['generating'])
  }

  async init() {
    await this.messageHistory.loadMessageList(true)

    // 等待MessageList Dom加载完成
    await onTargetDomSizeChange(this.ui.messageListScrollContent)
    await waitImagesLoaded(this.ui.messageListScrollContent)
    this.ui.scrollMessageListToBottom(false)

    if (
      this.remoteMessage.remoteMessageData.conversation[
        this.conversation.conversationId
      ]
    ) {
      this.markConversationToRead()
    }
    this.emit('onMessageInit')
  }

  async resetMessageList() {
    this.messageHistory.resetMessageList()
    // 恢复userMessage滚动到顶部而触发的滚动区域高度的补偿值
    if (this.ui.messageListScrollContent) {
      this.ui.messageListScrollContent.style.minHeight = 'auto'
    }
  }

  private clearTimeout = () => {
    if (this.currentMessageTimeout) {
      clearTimeout(this.currentMessageTimeout)
      this.currentMessageTimeout = null
    }
    if (this.currentSkillTimeout) {
      clearTimeout(this.currentSkillTimeout)
      this.currentSkillTimeout = null
    }
  }

  private stopAssistantMessagePendingStatus() {
    this.messageHistory.updateLastMessage('isFetching', false)
    this.messageHistory.updateLastMessage('isStreaming', false)
    const assistantMessage =
      this.messageHistory.getLastMessage() as AssistantMessage

    if (!assistantMessage) return

    this.messageHistory.updateLastMessage(
      'skills',
      assistantMessage.skills.map(item => {
        return {
          ...item,
          status: item.status === 'PENDING' ? 'FAILED' : item.status,
        }
      }),
    )
    if (assistantMessage.knowledge) {
      this.messageHistory.updateLastMessage('knowledge', {
        ...assistantMessage.knowledge,
        status:
          assistantMessage.knowledge.status === 'PENDING'
            ? 'FAILED'
            : assistantMessage.knowledge.status,
      })
    }
    if (assistantMessage.think) {
      this.messageHistory.updateLastMessage('think', {
        ...assistantMessage.think,
        status:
          assistantMessage.think.status === 'PENDING'
            ? 'FAILED'
            : assistantMessage.think.status,
      })
    }
    if (
      assistantMessage.relatedQuestion &&
      assistantMessage.relatedQuestion.status === 'PENDING'
    ) {
      this.messageHistory.updateLastMessage('relatedQuestion', undefined)
    }
  }

  async sendMessage(payload: SendMessagePayload) {
    if (this.generating) {
      throw new Error(
        '[BetterYeah Chat] The previous message is not yet finished, please send a message after it is completed.',
      )
    } else {
      this.generating = true

      // 为了给用户删掉最后一个会话后，发送消息，当前会话丢失打的补丁
      if (!this.conversation.conversationId) {
        const id = await this.conversation.createConversationAndRefreshList()
        await this.conversation.updateConversationId(id)
        await sleep(100)
      }

      let userMessage: UserMessage | undefined
      const assistantMessage = generalAssistantMessage(
        this.conversation.currentSessionId,
      )

      if (payload.action === 'regenerate') {
        assistantMessage.userMessageId = payload.originUserMessageId
      } else {
        userMessage = generalUserMessage({
          content: payload.content,
          sessionId: this.conversation.currentSessionId,
          file: payload.file,
          quote: payload.quote,
        })
        assistantMessage.userMessageId = userMessage.id
        this.messageHistory.list.push(userMessage)
      }
      this.messageHistory.isEmpty = false

      await nextTick()
      await this.ui.scrollLastMessageToViewTop()

      this.messageHistory.list.push(assistantMessage)

      const authKey =
        this.apiClient.auth.apiType === 'openApi'
          ? 'Access-Key'
          : 'Authorization'
      const headers = {
        [authKey]: this.apiClient.auth.accessKey,
        'Workspace-Id': this.apiClient.auth.workspaceId,
      }

      const timeoutHandle = () => {
        this.messageHistory.updateLastMessage('success', false)
        this.messageHistory.updateLastMessage(
          'errorMessage',
          '回答超时，请重试',
        )
        this.stopAssistantMessagePendingStatus()

        this.abortCurrentSSE?.()
        this.generating = false
        this.emit('onChatEnd')
      }

      this.abortCurrentSSE = sse<ChatStream>({
        url: this.apiClient.chatApi.Message.chatURL,
        method: 'POST',
        body: {
          content: payload.content,
          file_ids: payload.file?.upload_file_id
            ? [payload.file.upload_file_id]
            : undefined,
          file_action: payload.file?.upload_file_id ? 'FILE_URL' : undefined,
          enforce_tool_call_params: {
            shortcut_function_id: payload.shortcut?.shortcutFunctionId,
            tool_arguments: payload.shortcut?.toolArguments,
            chat_client_tool_id: payload.quote?.tool_id,
            chat_quote_id: payload.quote?.quote_id,
            ...(payload.enforce_tool_call_params ?? {}),
          },
          conversation_id: this.conversation.conversationId,
          robot_id: this.agentId,
          stream: true,
          inputs: {
            userMessage: payload.content,
            ...(payload.variables ?? this.conversation.variables ?? {}),
          },
          action: payload.action,
          response_mode: 'streaming',
        },
        headers,
        onOpen: () => {
          this.clearTimeout()
          this.currentMessageTimeout = setTimeout(
            timeoutHandle,
            EVENT_SOURCE_TIMEOUT,
          )
        },
        onMessage: content => {
          this.clearTimeout()
          this.currentSkillTimeout = setTimeout(timeoutHandle, TOOL_TIMEOUT)
          const assistantMessage =
            this.messageHistory.getLastMessage() as AssistantMessage

          // 比较Hack，但是服务端没办法处理think的状态，只能如此
          if (content.type !== 'THINKING' && assistantMessage.think) {
            this.messageHistory.updateLastMessage('think', {
              ...assistantMessage.think,
              status: 'SUCCEEDED',
            })
          }

          switch (content.type) {
            case 'FUNCTION':
              if (content.tool_type === 'dataset') {
                this.messageHistory.updateLastMessage(
                  'knowledge',
                  processKnowledge(content, assistantMessage.knowledge),
                )
              } else if (
                content.tool_type === 'flow' ||
                content.tool_type === 'database' ||
                content.tool_type === 'system' ||
                content.tool_type === 'custom_function' ||
                content.tool_type === 'agent'
              ) {
                this.messageHistory.updateLastMessage(
                  'skills',
                  processSkills(content, assistantMessage.skills),
                )
              }
              break
            case 'THINKING':
              assistantMessage.think = assistantMessage.think
                ? assistantMessage.think
                : {
                    status: 'PENDING',
                    content: '',
                  }
              assistantMessage.think.content += content.content
              this.messageHistory.updateLastMessage('think', {
                ...assistantMessage.think,
              })
              break
            case 'RELATED_QUESTIONS':
              this.messageHistory.updateLastMessage(
                'relatedQuestion',
                processRelateQuestion(
                  content,
                  assistantMessage.relatedQuestion,
                ),
              )
              // 推荐提问的时候，实际上已经结束流式了
              this.messageHistory.updateLastMessage('isStreaming', false)
              break
            case 'TASK':
              // Chat Message 中不存在TASK消息
              break
            case 'SUGGESTED_PLUGIN':
              this.messageHistory.updateLastMessage(
                'suggestPlugin',
                processSuggestPlugin(content),
              )
              break
            case 'LLMSensitiveOutput':
              this.messageHistory.updateLastMessage('content', content.content)
              this.messageHistory.updateLastMessage('think', {
                status: 'SUCCEEDED',
                content: content.content,
              })
              break
            case 'TEXT':
              this.messageHistory.updateLastMessage(
                'content',
                assistantMessage.content + content.content,
              )
              break
            case 'CHAT_RECORD':
              this.messageHistory.updateLastMessage(
                'id',
                `${ASSISTANT_ID_PREFIX}-${content.content}`,
              )
              this.messageHistory.updateLastMessage('recordId', content.content)
              if (payload.action !== 'regenerate') {
                const userId = `${USER_ID_PREFIX}-${content.content}`
                this.messageHistory.updateLastMessage('userMessageId', userId)
                this.messageHistory.updateMessage(userMessage.id, {
                  ...userMessage,
                  id: userId,
                })
              }
              assistantMessage.skills.forEach(item => {
                if (item.form) {
                  item.form.recordId = content.content
                }
              })
              this.messageHistory.updateLastMessage(
                'skills',
                assistantMessage.skills,
              )
              break
            case 'RESULT':
              // 这里是处理流程错误，比如FLow、技能调用错误等情况
              if (content.message) {
                this.messageHistory.updateLastMessage(
                  'errorMessage',
                  content.message,
                )
                this.messageHistory.updateLastMessage(
                  'success',
                  content.status !== 'FAILED',
                )
              }
              break
            default:
              break
          }
          // 第一帧出现后，修改fetching状态, 并切换streaming状态
          this.messageHistory.updateLastMessage('isFetching', false)
          this.messageHistory.updateLastMessage('isStreaming', true)
          this.emit('onMessage')
        },
        onError: error => {
          this.clearTimeout()
          const response = (error as any).response
          const lastMessage =
            this.messageHistory.getLastMessage() as AssistantMessage

          this.onError({
            type: 'CHAT_ERROR',
            module: 'message',
            content: {
              message: error.message,
              code: response.code,
              assistantMessage: lastMessage,
              userMessage: this.messageHistory.findMessage(
                lastMessage.userMessageId,
              ) as UserMessage,
            },
          })
          this.generating = false
          this.handlePullRemoteMessage()
          return false
        },
        onClose: () => {
          this.clearTimeout()
          this.generating = false
          this.messageHistory.updateLastMessage('isFetching', false)
          this.messageHistory.updateLastMessage('isStreaming', false)
          this.handlePullRemoteMessage()
          this.emit('onChatEnd')
          // 第一次问答要生产会话摘要作为conversation Title
          if (this.messageHistory.list.length <= 3 && userMessage) {
            const question = userMessage.content
            const assistantMessage = this.messageHistory.getLastMessage()
            const answer = assistantMessage?.content
            if (question && answer) {
              this.conversation.onTitleGenerate(
                this.conversation.conversationId,
                question,
                answer,
              )
            }
          }
        },
      })
    }
  }

  async onMessageFormSubmit(form: FormSkill, values: Record<string, any>) {
    try {
      await this.apiClient.innerChatApi.Message.submitForm(
        form.flowId,
        form.recordId,
        this.apiClient.auth.workspaceId,
        values,
        this.conversation.variables,
      )
      const message = this.messageHistory.list.find(
        item => item.recordId === form.recordId && item.role === 'ASSISTANT',
      ) as AssistantMessage
      if (message) {
        const newMessage = { ...message }
        newMessage.skills.forEach(skill => {
          if (skill.form && skill.form.flowId === form.flowId) {
            skill.form.isSubmit = true
            skill.form.formData = values
          }
        })
        this.messageHistory.updateMessage(message.id, newMessage)
      }
    } catch (e: any) {
      this.onError({
        type: 'API_ERROR',
        module: 'message',
        content: e,
      })
    }
  }

  addAssistantMessage(message: AssistantMessage) {
    this.messageHistory.list.push({
      ...message,
      from: 'CUSTOM_PUSH',
    })
  }

  private onRemoteMessageUpdate() {
    const data = this.remoteMessage.remoteMessageData
    if (data.conversation[this.conversation.conversationId]) {
      this.hasNewRemoteMessage = true
      this.handlePullRemoteMessage()
    }
  }

  private async handlePullRemoteMessage() {
    if (this.hasNewRemoteMessage && !this.generating) {
      await this.messageHistory.loadMessageList(true)
      await this.markConversationToRead()
      // 执行UI副作用
      await onTargetDomSizeChange(this.ui.messageListScrollContent)
      await waitImagesLoaded(this.ui.messageListScrollContent)
      await this.ui.scrollLastMessageToViewTop()
    }
  }

  private async markConversationToRead() {
    try {
      await this.apiClient.chatApi.Chat.markMessageAsRead(
        this.conversation.conversationId,
        this.apiClient.auth.workspaceId,
      )
      this.hasNewRemoteMessage = false
      // 不用再执行获取了，因为服务端在已读后会主动推送一条消息过来，在消息中会由remoteMessage内部刷新
      // await this.remoteMessage.fetchRemoteMessageDataDebounce()
    } catch (e: any) {
      this.onError({
        type: 'API_ERROR',
        module: 'conversation',
        content: e,
      })
    }
  }

  stopCurrentMessageRequest() {
    if (this.generating) {
      this.abortCurrentSSE?.()
      this.clearTimeout()
      this.stopAssistantMessagePendingStatus()
      this.messageHistory.updateLastMessage('finishReason', 'abort')
      this.generating = false
      this.emit('onChatEnd')
      this.handlePullRemoteMessage()
    }
  }
}
