import type { ApiClient } from '../apiClient'
import type {
  AssistantMessage,
  MessageItem,
  UserMessage,
} from '../types/message'
import {
  transformAssistantMessage,
  transformUserMessage,
} from '../utils/messsage'
import type { Conversation } from '../conversation'
import { EventCallBack } from '../runtime/eventCallBack'
import { observe } from '../runtime/observer'
import type { ErrorHandle } from '../types/error'

const DEFAULT_PAGE_SIZE = 20

type PropertyListenerEvent = 'loading' | 'list' | 'hasMore' | 'isEmpty'

export class MessageHistory extends EventCallBack<PropertyListenerEvent> {
  loading = true

  requesting = false

  list: MessageItem[] = []

  hasMore = true

  isEmpty = true

  private nextPageNo: 1

  private readonly pageSize = DEFAULT_PAGE_SIZE

  constructor(
    private readonly conversation: Conversation,
    private readonly apiClient: ApiClient,
    private readonly onError: ErrorHandle,
  ) {
    super()

    return observe(this, this.emit.bind(this), [
      'loading',
      'list',
      'hasMore',
      'isEmpty',
    ])
  }

  async loadMessageList(reload?: boolean) {
    if (reload) {
      this.nextPageNo = 1
      this.hasMore = true
      this.isEmpty = true
    }

    if (this.requesting) {
      console.warn('[BetterYeah Chat] message history is loading, please wait')
      return
    }

    if (!this.conversation.conversationId) {
      console.warn('[BetterYeah Chat] conversationId is undefined')
      this.list = []
      return
    }

    if (!this.hasMore) {
      console.warn('[BetterYeah Chat] No more messages')
      return
    }

    this.loading = true
    this.requesting = true

    try {
      const res = await this.apiClient.chatApi.Message.list({
        conversation_id: this.conversation.conversationId,
        page_number: this.nextPageNo,
        page_size: this.pageSize,
      })

      const data = res.data
      const messageList = data?.data_list ?? []
      this.isEmpty = data.total_count <= 0
      const list: MessageItem[] = []
      messageList.forEach(message => {
        if (
          message.user_content ||
          message.quotes?.length ||
          message.files?.length
        ) {
          list.push(transformUserMessage(message))
        }
        list.push(transformAssistantMessage(message))
      })
      if (this.nextPageNo === 1) {
        this.list = list
      } else {
        this.list = [...list, ...this.list]
      }
      this.hasMore = data.page_number < data.total_pages

      if (this.hasMore) {
        this.nextPageNo++
      }
    } catch (e: any) {
      this.onError({
        type: 'API_ERROR',
        module: 'messageHistory',
        content: e,
      })
    } finally {
      this.loading = false
      this.requesting = false
    }
  }

  async resetMessageList() {
    this.nextPageNo = 1
    this.hasMore = true
    this.list = []
    this.isEmpty = true
  }

  updateLastMessage<T extends keyof AssistantMessage>(
    key: T,
    value: AssistantMessage[T],
  ) {
    const lastMessage = this.getLastMessage() as AssistantMessage

    if (lastMessage) {
      /**
       * 这里使用一个新的Object对象, 是因为React中，渲染Message的组件使用了memo，
       * 如果使用lastMessage[key] = value，会导致组件不更新
       */
      this.list[this.list.length - 1] = {
        ...lastMessage,
        [key]: value,
      }
    }
  }

  updateMessage(id: string, message: AssistantMessage | UserMessage) {
    const index = this.list.findIndex(item => item.id === id)
    if (index !== -1) {
      this.list[index] = {
        ...this.list[index],
        ...message,
      }
    }
  }

  findMessage(id: string) {
    return this.list.find(item => item.id === id)
  }

  deleteMessage(id: string) {
    this.list = this.list.filter(item => item.id !== id)
  }

  getLastMessage() {
    return this.list[this.list.length - 1]
  }
}
