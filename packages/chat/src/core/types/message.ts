import type { MessageFile, Task } from '@bty/global-types/message'
import type {
  GroupProcessList,
  MessageKnowledge,
  MessageRelatedQuestion,
  MessageSkill,
  MessageThink,
} from './skill'

export interface BaseMessage {
  id: string
  /**
   * 消息内容, Markdown 字符串
   */
  content: string
  /**
   * 消息所属的session id，当执行clear history时，会新建一个新的 session
   */
  sessionId: string
  /**
   * 消息时间戳
   */
  create_time: string
  /**
   * 对应接口中的recordId
   */
  recordId?: string
  /**
   * 点踩的记录id
   */
  feedbackId?: string
}

export interface AssistantMessage extends BaseMessage {
  role: 'ASSISTANT'
  /**
   * 消息类型
   * 1、SYSTEM_PUSH 系统推送消息，一般为TASK完成的消息推送，或者API调用的消息推送
   * 2、CHAT 用户聊天过程中AI回复的消息
   * 3. 用户自行插入的消息，比如Agent规则配置页面，保存规则后插入的welcome消息
   */
  from: 'SYSTEM_PUSH' | 'CHAT' | 'CUSTOM_PUSH'
  /**
   * 推荐提问
   */
  relatedQuestion?: MessageRelatedQuestion
  /**
   * 调用的技能
   */
  skills: MessageSkill[]
  /**
   * 调用的知识库
   */
  knowledge: MessageKnowledge
  /**
   * 触发的任务
   */
  task?: Task
  /**
   *
   */
  think?: MessageThink
  /**
   * 服务端在2023年1月31日已经去掉了, 只有历史消息才会有
   * 原本的逻辑是添加插件后，推送一条“我学会了xxx技能， 你可以问我xxxx
   */
  tips?: string[]
  /**
   * Agent 在处理一些批量任务时候的任务进度,
   */
  groupProcessList?: GroupProcessList[]
  /**
   * AI 推荐插件，用于编辑时，用户上传了不支持的文件格式，AI推荐合理的解析插件
   */
  suggestPlugin?: SuggestPlugin[]
  /**
   * 用户发送的消息的内容，用于重新生成
   */
  userMessageId?: string
  /**
   * 用于标记本次聊天是否发生错误
   */
  success: boolean
  /**
   * 发生错误时，记录本次聊天的错误消息
   */
  errorMessage?: string
  /**
   * 是否处于fetching状态，一般是AI第一帧可视消息还没输出的时候，比如文本的第一个字没有输出，并且技能的也没触发到
   */
  isFetching: boolean
  /**
   * 是否处于流式状态
   */
  isStreaming: boolean
  /**
   * 停止的原因
   */
  finishReason?: 'stop' | 'abort'
  /**
   * 耗时
   */
  durationTime?: number
  /**
   * 模型
   */
  llmModel?: string
  /**
   * 首字耗时
   */
  irlTime?: number
}

interface MessageUrlQuote {
  content: string
  quote_id: string
  quote_type: 'URL'
  title: string
  url: string
  icon: string
  tool_id?: string
}

interface MessageTextQuote {
  content: string
  quote_id: string
  tool_id?: string
  quote_type: 'TEXT'
}

export type ChatMessageQuote = MessageUrlQuote | MessageTextQuote

export interface UserMessage extends BaseMessage {
  role: 'USER'
  file?: MessageFile
  quote?: ChatMessageQuote
}

export type MessageItem = UserMessage | AssistantMessage

export interface SendMessagePayload {
  content: string
  file?: MessageFile
  quote?: ChatMessageQuote
  shortcut?: {
    shortcutFunctionId?: string
    toolArguments?: Record<string, unknown>
  }
  variables?: Record<string, any>
  action?: 'regenerate'
  originUserMessageId?: string // 原来的UserMessage的ID，用于重新生成的时候，绑定原始的用户消息（仅前端逻辑使用）
  enforce_tool_call_params?: Record<string, any>
}

export interface RemoteMessageData {
  agent: Record<string, number> // agentId, messageCount
  conversation: Record<string, number> // conversationId, messageCount
}

export interface SuggestPlugin {
  displayName: string
  functionId: string
  avatar: string
}
