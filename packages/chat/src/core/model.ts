import { generateModelUniqueKey } from '@bty/util'
import {
  ExtendedModelType,
  type BaseUniqueItem,
  type LLMModalItem,
  type LLMModelLimitedItem,
} from '@bty/global-types/model'
import { Version, VersionStatus } from '@bty/global-types/version'
import type { ApiClient } from './apiClient'
import type { ErrorHandle } from './types/error'

export interface ModelData {
  llmModelList: (LLMModalItem & BaseUniqueItem)[]
  llmModelLimitedList: (LLMModelLimitedItem & BaseUniqueItem)[]
  agentNotSupportFCModelList: string[]
}

export class Model {
  private modelData: ModelData | null = null

  constructor(
    private readonly apiClient: ApiClient,
    private readonly onError: ErrorHandle,
  ) {}

  /**
   * 获取模型数据
   */
  getModelData(): ModelData | null {
    return this.modelData
  }

  /**
   * 重新加载并获取 LLM 模型列表
   */
  async reloadLLMModelList(): Promise<ModelData> {
    try {
      const creatorVersionInfo =
        await this.apiClient.chatApi.Agent.getAgentCreatorVersionInfoByWorkspaceId(
          this.apiClient.auth.workspaceId,
        )

      const isLimited =
        creatorVersionInfo?.data?.status === VersionStatus.LoseEffect ||
        creatorVersionInfo?.data?.product_code === Version.FREE

      const getLLMModelListRes =
        await this.apiClient.chatApi.Agent.getLLMModelList(
          'textGenerate',
          ExtendedModelType.Think_Model_Function,
        )

      const llmModelList = (getLLMModelListRes?.data || []).map(item => {
        return {
          ...item,
          model_unique_key: generateModelUniqueKey(item, 'channel'),
        }
      })

      const agentNotSupportFCModelList = (getLLMModelListRes?.data || [])
        .filter(item => item.methodSupport === 'normal')
        .map(item => item.model)

      const getLLMModelListLimitedRes =
        await this.apiClient.chatApi.Agent.getLLMModelListLimited(
          this.apiClient.auth.workspaceId,
        )

      const llmModelLimitedList = (getLLMModelListLimitedRes?.data || [])
        .filter(v =>
          isLimited
            ? v.trial_available === undefined
              ? true
              : v.trial_available
            : true,
        )
        .map(item => {
          return {
            ...item,
            model_unique_key: generateModelUniqueKey(item, 'model_limit'),
          }
        })

      this.modelData = {
        llmModelList,
        llmModelLimitedList,
        agentNotSupportFCModelList,
      }

      return this.modelData
    } catch (e: any) {
      this.onError({
        type: 'API_ERROR',
        module: 'model',
        content: e,
      })
      throw e
    }
  }

  /**
   * 获取 LLM 模型列表（如果没有缓存则重新加载）
   */
  async getLLMModelList(): Promise<ModelData> {
    if (!this.modelData) {
      return await this.reloadLLMModelList()
    }
    return this.modelData
  }

  /**
   * 清空模型数据缓存
   */
  clearCache(): void {
    this.modelData = null
  }
}
