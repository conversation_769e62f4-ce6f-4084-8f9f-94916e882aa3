import type {
  ConversationData,
  CreateConversationPayload,
} from '@bty/global-types/conversation'
import { ChatbotRunType } from '@bty/global-types/conversation'
import type { LLMChannels } from '@bty/global-types/model'
import type { ApiClient } from './apiClient'
import { EventCallBack } from './runtime/eventCallBack'
import type { ChatConfig } from './core'
import { observe } from './runtime/observer'
import type { ChatUI } from './ui'
import type { Agent } from './agent'
import type { ErrorHandle } from './types/error'

const DEFAULT_PAGE_SIZE = 60

export interface ConversationOptions {
  defaultConversationId?: string
  conversationListLimit?: number
  defaultVariables?: Record<string, any>
}

type ConversationEvent = 'onConversationChange'
type PropertyListenerEvent =
  | 'conversationId'
  | 'conversationData'
  | 'conversationLoadLoading'
  | 'currentTaskId'
  | 'currentSessionId'
  | 'variables'

type Event = ConversationEvent | PropertyListenerEvent

export class Conversation extends EventCallBack<Event> {
  conversationId: string

  conversationData = {
    loading: false,
    list: [] as ConversationData[],
    nextPageNo: 1,
    hasMore: true,
  }

  conversationLoadLoading = false

  currentTaskId: string

  variables: Record<string, any> = {}

  defaultVariables: Record<string, any> = {}

  currentSessionId: string

  defaultConversationId: string

  constructor(
    private readonly options: ConversationOptions,
    private readonly agent: Agent,
    private readonly apiClient: ApiClient,
    private readonly chatConfig: ChatConfig,
    private readonly ui: ChatUI,
    private readonly onError: ErrorHandle,
  ) {
    super()
    this.defaultConversationId = options.defaultConversationId
    this.defaultVariables = options.defaultVariables ?? {}

    return observe(this, this.emit.bind(this), [
      'conversationId',
      'conversationData',
      'conversationLoadLoading',
      'currentTaskId',
      'currentSessionId',
      'variables',
    ])
  }

  setDefaultConversationId(id: string | undefined) {
    this.defaultConversationId = id
  }

  async loadConversationList(reload?: boolean) {
    if (this.conversationData.loading) {
      console.warn(
        '[BetterYeah Chat] The conversation list is loading, please do not reload',
      )
      return
    }

    this.conversationData.loading = true
    reload && (this.conversationData.nextPageNo = 1)

    try {
      const runTypeResult = this.getRunType()
      const result = await this.apiClient.chatApi.Conversation.list(
        runTypeResult,
        {
          pageNo: this.conversationData.nextPageNo,
          pageSize: this.options.conversationListLimit ?? DEFAULT_PAGE_SIZE,
        },
      )
      if (result.data) {
        const list = result.data.data ?? []
        this.conversationData.list =
          this.conversationData.nextPageNo === 1
            ? list
            : [...this.conversationData.list, ...list]

        this.conversationData.hasMore =
          result.data.total > this.conversationData.list.length
        if (this.conversationData.hasMore) {
          this.conversationData.nextPageNo++
        }
      }
    } catch (e: any) {
      this.onError({
        type: 'API_ERROR',
        module: 'conversation',
        content: e,
      })
    } finally {
      this.conversationData.loading = false
    }
  }

  async updateConversationId(id: string | undefined) {
    if (id !== this.conversationId) {
      this.conversationId = id
      if (id) {
        await this.loadCurrentSession(id)
      }
      this.emit('onConversationChange')
    }
  }

  async loadCurrentSession(conversationId: string) {
    try {
      const result =
        await this.apiClient.chatApi.Conversation.currentSession(conversationId)
      this.currentSessionId = result.data
    } catch (e: any) {
      this.onError({
        type: 'API_ERROR',
        module: 'conversation',
        content: e,
      })
    }
  }

  async createNewSession() {
    try {
      const result = await this.apiClient.chatApi.Message.refreshSession(
        this.conversationId,
        this.variables,
      )
      if (result.data.session_id) {
        this.currentSessionId = result.data.session_id
        return result.data.session_id
      }
    } catch (e: any) {
      this.onError({
        type: 'API_ERROR',
        module: 'conversation',
        content: e,
      })
    }
  }

  async createConversation(
    conversation_config?: CreateConversationPayload['conversation_config'],
  ): Promise<string | undefined> {
    try {
      const result = await this.apiClient.chatApi.Conversation.create({
        title: `新会话${this.conversationData.list.length > 0 ? this.conversationData.list.length : ''}`,
        runType: this.chatConfig.runType,
        conversation_config,
      })
      if (result?.data?.conversation_id) {
        return result.data.conversation_id
      }
    } catch (e: any) {
      this.onError({
        type: 'API_ERROR',
        module: 'conversation',
        content: e,
      })
    }
  }

  async createConversationAndRefreshList() {
    const conversationId = await this.createConversation()
    this.loadConversationList(true)
    return conversationId
  }

  async deleteConversation(id: string) {
    try {
      await this.apiClient.chatApi.Conversation.delete(id)
      await this.loadConversationList(true)
      // 如果删除的是当前选中的conversation，则重新选中列表的第一个
      if (id === this.conversationId) {
        if (this.conversationData.list.length > 0) {
          await this.updateConversationId(
            this.conversationData.list[0].conversation_id,
          )
        } else {
          await this.updateConversationId(undefined)
          this.currentSessionId = undefined
        }
      }
      if (id === this.defaultConversationId) {
        this.setDefaultConversationId(undefined)
      }
    } catch (e: any) {
      this.onError({
        type: 'API_ERROR',
        module: 'conversation',
        content: e,
      })
    }
  }

  async updateConversationTitle(id: string, title: string) {
    try {
      await this.apiClient.chatApi.Conversation.update({
        conversation_id: id,
        title,
      })
    } catch (e: any) {
      this.onError({
        type: 'API_ERROR',
        module: 'conversation',
        content: e,
      })
    }
  }

  async updateConversationModelConfig(
    id: string,
    modelConfig: {
      model: string
      channel: LLMChannels
    },
  ) {
    try {
      await this.apiClient.chatApi.Conversation.update({
        conversation_id: id,
        config: {
          model_config: modelConfig,
        },
      })
      this.loadConversationList(true)
    } catch (e: any) {
      this.onError({
        type: 'API_ERROR',
        module: 'conversation',
        content: e,
      })
    }
  }

  async onTitleGenerate(id: string, q: string, a: string) {
    try {
      const res = await this.apiClient.innerChatApi.Conversation.generateTitle({
        conversation_id: id,
        question: q,
        answer: a,
      })
      if (res?.data?.title) {
        const index = this.conversationData.list.findIndex(
          item => item.conversation_id === id,
        )
        if (index !== -1) {
          this.conversationData.list[index].title = res.data.title
          /**
           * 这里使用一个新的Object对象, 是因为React中，渲染Message的组件使用了memo，
           * 如果使用lastMessage[key] = value，会导致组件不更新
           */
          this.conversationData = { ...this.conversationData }
        }
      }
    } catch (e: any) {
      this.onError({
        type: 'API_ERROR',
        module: 'conversation',
        content: e,
      })
    }
  }

  async onRelatedQuestionToggle(enable: boolean) {
    try {
      await this.apiClient.innerChatApi.Conversation.toggleRelatedQuestionsGenerate(
        this.conversationId,
        this.currentSessionId,
        enable,
      )
    } catch (e: any) {
      this.onError({
        type: 'API_ERROR',
        module: 'conversation',
        content: e,
      })
    }
  }

  async loadVariable() {
    // 重置数据
    this.variables = {}
    this.ui.setVariableOpen(false)

    const variableConfig = this.agent.agentConfig?.rule?.agent_variables ?? []
    if (variableConfig.length) {
      // 加载当前保存的变量
      try {
        const res = await this.apiClient.chatApi.Conversation.getVariables(
          this.conversationId,
          this.currentSessionId,
        )
        const conversationVariable = res?.data ?? {}
        const variables: Record<string, any> = {
          ...conversationVariable,
          // 用户传入的自定义变量会优先使用
          ...this.defaultVariables,
        }
        // 根据配置，赋值保存的变量数据到上下文中
        variableConfig.forEach(configItem => {
          this.variables[configItem.variable_name] =
            variables[configItem.variable_name]
        })
        // 判断还有没有未赋值的必填变量，如果有，弹出变量编辑弹窗
        const hasEmptyRequiredVariable = variableConfig
          .filter(item => item.required)
          .some(item => {
            return !this.variables[item.variable_name]
          })
        if (hasEmptyRequiredVariable) {
          this.ui.setVariableOpen(true)
        }
      } catch (e: any) {
        this.onError({
          type: 'API_ERROR',
          module: 'conversation',
          content: e,
        })
      }
    }
    // 将用户设置的额外变量设置进变量中
    this.setDefaultVariableToVariable()
  }

  async setVariable(values: Record<string, any>) {
    try {
      this.variables = values
      this.setDefaultVariableToVariable()
      await this.apiClient.chatApi.Conversation.setVariables(
        this.conversationId,
        this.currentSessionId,
        values,
      )
    } catch (e: any) {
      this.onError({
        type: 'API_ERROR',
        module: 'conversation',
        content: e,
      })
      console.error('[BetterYeah Chat] setVariable failed, reason: ', e)
    }
  }

  setDefaultVariable(values: Record<string, any>) {
    this.defaultVariables = values
    this.setDefaultVariableToVariable()
  }

  // 将用户设置的额外变量设置进变量中
  setDefaultVariableToVariable() {
    Object.entries(this.defaultVariables).forEach(([key, value]) => {
      this.variables[key] = value
    })
  }

  async initConversationId() {
    try {
      this.conversationLoadLoading = true
      if (this.defaultConversationId) {
        /**
         * TODO 这里考虑是否需要校验conversationId的合法性
         * 以下情况可能会存在不合法的defaultConversationId
         * 1、通过UrlParams设置的id可能不是当前用户的
         * 2、通过SDK传入的id可能不是当前用户的
         * 如果conversationId不合法，则跳过默认赋值，进入下面的else分支
         * 1、如果List不为空，自动选中List第一项
         * 2、创建一个新的conversation
         */
        await this.updateConversationId(this.defaultConversationId)
      } else if (!this.conversationId) {
        if (this.conversationData.list.length > 0) {
          await this.updateConversationId(
            this.conversationData.list[0].conversation_id,
          )
        } else {
          const conversationId = await this.createConversationAndRefreshList()
          if (conversationId) {
            await this.updateConversationId(conversationId)
          }
        }
      }
    } finally {
      this.conversationLoadLoading = false
    }
  }

  private getRunType() {
    const runType = this.chatConfig.runType
    // 客户端查两种类型的会话
    if (runType === ChatbotRunType.CHAT_CLIENT) {
      return [
        ChatbotRunType.CHATBOT,
        ChatbotRunType.CLIENT_SMART_TOOLS,
        runType,
      ]
    }

    if (runType === ChatbotRunType.CHATBOT) {
      return [
        ChatbotRunType.CHAT_CLIENT,
        ChatbotRunType.CLIENT_SMART_TOOLS,
        ChatbotRunType.AI_RESEARCH,
        runType,
      ]
    }

    return runType
  }

  setCurrentTaskId(taskId: string) {
    this.currentTaskId = taskId
  }
}
