import type {
  Chat<PERSON><PERSON>ageI<PERSON>,
  ChatMessageQuote as OriginChatMessageQuote,
  FunctionCallChatStream,
  MessageFile,
  RelatedQuestionChatStream,
  SuggestedPluginChatStream,
  Task,
} from '@bty/global-types/message'
import { parseKnowledgeLogData } from '@bty/util'
import dayjs from 'dayjs'
import { ASSISTANT_ID_PREFIX, USER_ID_PREFIX } from '../constants/message'
import type {
  AssistantMessage,
  ChatMessageQuote,
  SuggestPlugin,
  UserMessage,
} from '../types/message'
import type {
  FormSkill,
  GroupProcessList,
  MessageKnowledge,
  MessageRelatedQuestion,
  MessageSkill,
  MessageThink,
  SyncFlowMsg,
} from '../types/skill'

export function transformUserMessage(message: ChatMessageItem): UserMessage {
  return {
    role: 'USER',
    content: message.user_content,
    id: `${USER_ID_PREFIX}-${message.id}`,
    sessionId: message.session_id,
    create_time: message.create_time,
    quote: processMessageQuote(message.quotes ?? []),
    file: processMessageFile(message),
  }
}

function processMessageFile(message: ChatMessageItem): MessageFile | undefined {
  if (message.files?.length) {
    const [file] = message.files

    if (file) {
      return {
        name: file.file_name,
        type: file.file_type,
        url: file.file_url,
        upload_file_id: file.upload_file_id,
        byte_size: file.byte_size,
      }
    }
  }

  if (message.file_name) {
    return {
      name: message.file_name,
      type: message.file_type ?? '',
      url: message.file_url ?? '',
      upload_file_id: message.upload_file_id ?? '',
    }
  }
}

function processMessageQuote(
  quotes: OriginChatMessageQuote[],
): ChatMessageQuote | undefined {
  const [quote] = quotes || []

  if (!quote) return undefined

  try {
    if (quote.quote_type === 'URL') {
      return {
        quote_type: 'URL',
        url: JSON.parse(quote.content).url ?? '',
        quote_id: quote.quote_id,
        content: JSON.parse(quote.content).title ?? quote.content ?? '',
        title: JSON.parse(quote.content).title ?? '',
        icon: JSON.parse(quote.content).favicon ?? '',
        tool_id: quote.chat_client_tool_id,
      }
    } else {
      return {
        quote_type: 'TEXT',
        quote_id: quote.quote_id,
        content: quote.content,
        tool_id: quote.chat_client_tool_id,
      }
    }
  } catch (error) {}
}

export function transformAssistantMessage(
  message: ChatMessageItem,
): AssistantMessage {
  let knowledge: MessageKnowledge | undefined
  let skills: MessageSkill[] = []
  let relatedQuestion: MessageRelatedQuestion | undefined
  let messageTask: Task | undefined
  let groupProcessList: GroupProcessList[] = []
  let tips: string[] = []
  let suggestPlugin = []
  let think: MessageThink

  message.response_ext?.tasks?.forEach(task => {
    switch (task.type) {
      case 'FUNCTION':
        if (task.tool_type === 'dataset') {
          knowledge = processKnowledge(task, knowledge)
          // 业务需求，Record中的PENDING请求视为失败
          if (knowledge.status === 'PENDING') {
            knowledge.status = 'FAILED'
          }
        } else if (
          task.tool_type === 'flow' ||
          task.tool_type === 'database' ||
          task.tool_type === 'system' ||
          task.tool_type === 'custom_function' ||
          task.tool_type === 'agent'
        ) {
          skills = processSkills(task, skills, message.id)
          skills.forEach(skill => {
            // 业务需求，Record中的PENDING请求视为失败
            if (skill.status === 'PENDING') {
              skill.status = 'FAILED'
            }
          })
        }
        break
      case 'TASK':
        messageTask = {
          id: task.content.task_id,
          status: task.status,
          color: task.metadata?.color || '',
          title: task.name,
          icon: task.metadata?.icon || '',
          timestamp: task.timestamp * 1000,
        }
        break
      case 'THINKING':
        think = {
          status: task.status === 'SUCCEEDED' ? 'SUCCEEDED' : 'FAILED',
          content: task.content,
        }
        break
      case 'SUGGESTED_PLUGIN':
        suggestPlugin = processSuggestPlugin(task)
        break
      case 'RELATED_QUESTIONS':
        relatedQuestion = processRelateQuestion(task, relatedQuestion)
        // 业务需求，Record中的PENDING请求视为失败
        if (relatedQuestion?.status === 'PENDING') {
          relatedQuestion.status = 'FAILED'
        }
        break
      default:
        break
    }
  })

  // 作用到Copilot Message，用来显示Copilot步骤的执行状态
  if (message.response_ext?.group_progress_list) {
    groupProcessList = message.response_ext.group_progress_list.map(v => ({
      title: v.name,
      description:
        v.status === 'SUCCEEDED'
          ? v?.sub_progress
              ?.map(v => v.name?.replace('增加插件 ', ''))
              ?.join('、') || ''
          : v.message,
      status: v.status,
    }))
  }

  if (message.tips?.length) {
    tips = message.tips
  }

  const success = message.record_status === 'SUCCEEDED'

  return {
    role: 'ASSISTANT',
    content: message.response,
    from: message.is_push ? 'SYSTEM_PUSH' : 'CHAT',
    id: `${ASSISTANT_ID_PREFIX}-${message.id}`,
    knowledge,
    skills,
    relatedQuestion,
    success,
    tips,
    think,
    groupProcessList,
    suggestPlugin,
    task: messageTask,
    sessionId: message.session_id,
    userMessageId: `${USER_ID_PREFIX}-${message.id}`,
    errorMessage: success ? '' : (message.response_ext.message ?? '未知错误'),
    isFetching: false,
    isStreaming: false,
    create_time: message.create_time,
    finishReason: message.finish_reason,
    feedbackId: message.feedback_id,
    recordId: message.id,
    durationTime: message.response_ext?.duration_time,
    llmModel: message.response_ext?.llm_model,
    irlTime: message.response_ext?.irl_time,
  }
}

export function generalUserMessage(payload: {
  content: string
  sessionId: string
  file?: MessageFile
  quote?: ChatMessageQuote
}): UserMessage {
  const { sessionId, content, file, quote } = payload
  return {
    role: 'USER',
    id: `${USER_ID_PREFIX}-${+new Date()}`,
    sessionId,
    content,
    file,
    quote,
    create_time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  }
}

export function generalAssistantMessage(sessionId: string): AssistantMessage {
  return {
    content: '',
    errorMessage: '',
    from: 'CHAT',
    id: `${ASSISTANT_ID_PREFIX}-${+new Date()}`,
    knowledge: undefined,
    relatedQuestion: undefined,
    role: 'ASSISTANT',
    sessionId,
    skills: [],
    success: true,
    userMessageId: undefined,
    isFetching: true,
    isStreaming: false,
    create_time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  }
}

export function processForm(
  task: FunctionCallChatStream,
  recordId?: string,
): FormSkill {
  let formSubmitted = task.content.form_submitted
  const formTaskId = task.content?.input?.task_id

  /**
   * 2025-04-16后端做了修改，底部快捷按钮提交的表单，inputsViewMode 从以前的 raw 改成了 form
   * 底部快捷按钮表单提交后，依然没有 form_submitted 字段，需要前端自己处理。
   * 底部快捷按钮表单提交后，表单数据已经提交过，此时有 task_id，只能用这个来判断表单数据已经提交过了。
   * https://mastergo.com/file/102587107134308?page_id=20440%3A179648&shareId=102587107134308
   * 技术文档：https://alidocs.dingtalk.com/i/nodes/2Amq4vjg89M5grDpfrRLnGOp83kdP0wQ
   */
  if (!formSubmitted && formTaskId) {
    formSubmitted = {
      status: 'SUBMITTED',
      inputs: task.content.input,
    }
  }

  const formData = formSubmitted?.inputs ?? task.content.input ?? {}

  const formInfo = task.metadata!.inputs_properties!

  // 根据配置格式化初始化数据
  formInfo?.formConfig?.forEach(item => {
    if (item.type === 'date' && formData[item.variableName]) {
      const value = formData[item.variableName]
      const formatMap = {
        year: 'YYYY',
        month: 'YYYY-MM',
        date: 'YYYY-MM-DD',
      }
      if (
        item.dateType &&
        value &&
        !dayjs(value, formatMap[item.dateType], true).isValid()
      ) {
        formData[item.variableName] = null
      }
    }
  })

  return {
    flowId: formInfo.flowId,
    recordId,
    formWelcome: formInfo.formWelcome,
    isSubmit: formSubmitted?.status === 'SUBMITTED',
    formConfig: formInfo.formConfig,
    formData,
  }
}

export function processKnowledge(
  task: FunctionCallChatStream,
  knowledge: MessageKnowledge,
): MessageKnowledge {
  let _knowledge: MessageKnowledge = { ...knowledge }
  switch (task.status) {
    case 'STARTED':
      _knowledge = {
        id: `knowledge-${new Date().getTime()}`,
        status: 'PENDING',
        content: [],
        function_code: task.function_code ?? '',
        metaData: {
          title: task.name,
          color: task.metadata.color,
          avatarType: 'image',
          avatar:
            'https://resource.bantouyan.com/betteryeah/agent/knowledge.svg',
        },
        raw: task,
      }
      break
    case 'SUCCEEDED': {
      _knowledge.status = 'SUCCEEDED'
      const parsedValue = parseKnowledgeLogData(
        typeof task.content.output === 'string'
          ? (task.content.output ?? '')
          : '',
      )
      _knowledge.content = parsedValue.map(item => ({
        id: Number(item.datastore_id),
        title: item.datastore_name,
        fileName: item.file_name,
        fileId: Number(item.file_id),
        mimeType: item.mimetype,
        paragraphId: Number(item.chunk_id),
        content: item.content,
        tool_type: 'dataset',
      }))
      _knowledge.raw = task
      break
    }
    case 'FAILED':
      _knowledge.status = 'FAILED'
      _knowledge.raw = task
      break
    default:
      break
  }
  return _knowledge
}

export function processSkills(
  task: FunctionCallChatStream,
  skills: MessageSkill[],
  recordId?: string,
): MessageSkill[] {
  const _skills = [...skills]

  const formatFlowMsg = (
    flowMsg: FunctionCallChatStream['content']['flow_msgs'],
  ): SyncFlowMsg[] => {
    return flowMsg.map(item => {
      return {
        message: item.message,
        type: item.message_type,
      }
    })
  }

  let currentSkill = _skills.find(item => item.id === task.tool_id)
  if (!currentSkill) {
    const icon = task.metadata?.icon ?? ''
    currentSkill = {
      status: 'PENDING',
      id: task.tool_id ?? '',
      metaData: {
        color: task.metadata?.color ?? '',
        avatar: icon,
        avatarType: icon.startsWith('http') ? 'image' : 'emoji',
        title: task.name,
      },
      function_code: task.function_code ?? '',
      output: task.content.output,
      tool_type: task.tool_type as
        | 'flow'
        | 'system'
        | 'custom_function'
        | 'agent',
      flowMsg: formatFlowMsg(task.content.flow_msgs ?? []),
      raw: task,
    }
  }

  // 为了兼容服务端某些bug，有些技能调用没有STARTED状态，需要在其他状态中塞入该技能
  const checkSkillAndPushSKill = () => {
    const currentSkillIndex = skills.findIndex(
      item => item.id === currentSkill.id,
    )
    if (currentSkillIndex === -1) {
      _skills.push(currentSkill)
    }
  }

  switch (task.status) {
    case 'STARTED': {
      _skills.push(currentSkill)
      break
    }
    case 'RUNNING':
      currentSkill.flowMsg = formatFlowMsg(task.content.flow_msgs ?? [])
      currentSkill.raw = task
      checkSkillAndPushSKill()
      break
    case 'SUCCEEDED':
      currentSkill.status = 'SUCCEEDED'
      currentSkill.output = task.content.output
      currentSkill.flowMsg = formatFlowMsg(task.content.flow_msgs ?? [])

      if (
        task.metadata?.inputs_properties &&
        task.metadata?.inputs_properties.inputsViewMode === 'form'
      ) {
        // 只有表单工作流需要处理 form 数据，普通工作流 inputsViewMode 是 raw
        // 2025-04-16后端做了修改，底部快捷按钮提交的表单，inputsViewMode 从以前的 raw 改成了 form
        currentSkill.form = processForm(task, recordId)
      }

      currentSkill.raw = task
      checkSkillAndPushSKill()
      break
    case 'FAILED':
      currentSkill.status = 'FAILED'
      currentSkill.raw = task
      checkSkillAndPushSKill()
      break
    default:
      break
  }
  return _skills.map(item => ({ ...item })) // 浅拷贝，否则memo更新不到
}

export function processRelateQuestion(
  task: RelatedQuestionChatStream,
  relatedQuestion?: MessageRelatedQuestion,
): MessageRelatedQuestion | undefined {
  let _relatedQuestion: MessageRelatedQuestion | undefined = relatedQuestion
    ? { ...relatedQuestion }
    : undefined
  switch (task.status) {
    case 'STARTED':
      _relatedQuestion = {
        status: 'PENDING',
        content: [],
      }
      break
    case 'SUCCEEDED':
      if (_relatedQuestion) {
        _relatedQuestion.status = 'SUCCEEDED'
        _relatedQuestion.content = task.content.related_questions ?? []
      }
      break
    case 'FAILED':
      if (_relatedQuestion) {
        _relatedQuestion.status = 'FAILED'
      }
      break
    default:
      break
  }
  return _relatedQuestion
}

export function processSuggestPlugin(
  task: SuggestedPluginChatStream,
): SuggestPlugin[] {
  return task.content.map(item => ({
    displayName: item.display_name,
    functionId: item.function_id,
    avatar: item.avatar,
  }))
}
