import type { CSSProperties, FC, MutableRefObject } from 'react'
import React, {
  useMemo,
  memo,
  useEffect,
  useRef,
  useState,
  useCallback,
} from 'react'
import type { MessageFile as IMessageFile } from '@bty/global-types/message'
import type { TextareaAutosizeProps } from 'react-textarea-autosize'
import TextAreaAutoSize from 'react-textarea-autosize'
import type { Shortcut } from '@bty/global-types/shortcuts'
import { cn, getRealRender } from '@bty/util'
import { useMemoizedFn } from 'ahooks'
import toast from 'react-hot-toast'
import { ShortcutsFormModal } from '@bty/components'
import type { LayoutMode } from '@bty/hooks'
import Tooltip from '../../base/components/Tooltip'
import { AppLogo } from '../../base/components/AppLogo'
import { ReactComponent as StopButton } from '../../assets/svg/stop.svg'
import { MessageQuote } from '../message/MessageQuote'
import type { ChatMessageQuote } from '../../../core'
import type { ChatEvents } from '../../provider/ChatEventProvider'

import { MobileConversationBtn } from '../MobileConversationBtn'
import ConversationClearBtn from './ConversationClearBtn'
import { ShortcutTips as DefaultShortcutTips } from './ShortcutTips'
import { ModelSelectWithChat as DefaultModelSelectWithChat } from './model-select/ModelSelect'
import { ShortcutsForm } from './shortcuts/ShortcutsForm'
import { useShortcutsForm } from './hooks/useShortcutsForm'
import { ShortcutsPanel } from './shortcuts/ShortcutsPanel'
import { SpeechInput } from './speech-input'
import { SendButton } from './SendButton'
import { InputFile } from './file/InputFile'
import { InputFileUploadWithChat } from './file/InputFileUploadWithChat'
import { getCaretCoordinates } from './util'

export interface MessageInputProps {
  /**
   * input props
   */
  className?: string
  style?: CSSProperties
  value?: string
  onChange?: (value: string) => void
  placeholder?: string
  autofocus?: boolean
  chatInputRef?: MutableRefObject<HTMLTextAreaElement>
  disabled?: boolean
  /**
   * 输入框事件
   */
  onSubmit?: ChatEvents['onMessageSubmit']
  onStopGenerate?: () => void
  onClearConversation?: () => void
  /**
   * 状态类
   */
  generating?: boolean
  /**
   * 功能开关类
   */
  voice?: boolean
  clearConversationEnable?: boolean
  conversationListEnable?: boolean
  /**
   * 文件相关
   */
  file?: IMessageFile
  onFileRemove?: () => void
  onFilePreview?: ChatEvents['onFilePreview']
  // 仅上传文件，拿到url
  onFileUpload?: ChatEvents['onFileUpload']
  // 上传文件，并且插入到MessageInput跟随消息一起发送
  onMessageFileUpload?: ChatEvents['onMessageFileUpload']
  /**
   * 自定义render
   */
  renders?: {
    fileUpload?: FC | boolean
    shortcutTips?: FC | boolean
    mobileConversationBtn?: FC | boolean
    modelSelect?: FC | boolean
    headerPrefix?: FC
    footer?: FC
  }
  /**
   * 语音输入
   */
  asrConfig?: {
    api: string
    params: Record<string, string>
  }
  /**
   * 客户端引用数据
   */
  quote?: ChatMessageQuote
  onQuoteRemove?: () => void
  /**
   * 输入框快捷入口
   */
  shortcuts?: Shortcut[]
  shortcutPreview?: boolean
  shortcutFormState?: ReturnType<typeof useShortcutsForm>
  /**
   * 样式布局
   */
  layoutMode?: LayoutMode
}

type MessageInputMode = 'input' | 'voice'

const NO_TIPS_SHORTCUTS_LENGTH = 4

const voiceAssets = {
  voiceButton:
    'https://resource-bty.oss-cn-hangzhou.aliyuncs.com/battleyeah-ai/chat-mobile/microphone%403x.png',
  keyboardButton:
    'https://resource-bty.oss-cn-hangzhou.aliyuncs.com/battleyeah-ai/chat-mobile/keyboard%403x.png',
}

const submitHotKey = 'Enter'

export const MessageInput = memo<MessageInputProps>(props => {
  const {
    placeholder = '问我任何问题...',
    value = '',
    generating,
    disabled,
    clearConversationEnable = false,
    conversationListEnable,
    shortcuts = [],
    shortcutPreview = false,
    autofocus = true,
    voice = false,
    file,
    onFileRemove,
    onFileUpload,
    onMessageFileUpload,
    quote,
    asrConfig,
    onChange,
    onSubmit,
    onStopGenerate,
    onClearConversation,
    onQuoteRemove,
    style,
    onFilePreview,
    chatInputRef,
    renders = {},
    layoutMode,
    shortcutFormState,
  } = props

  // 主动控制 textarea 输入框的 focus 状态
  const [focusTextArea, setFocusTextArea] = useState<boolean>(autofocus)
  const [inputMultiLine, setInputMultiLine] = useState<boolean>(false)

  const FileUpload = getRealRender(
    renders.fileUpload ?? true,
    InputFileUploadWithChat,
  )
  const ShortcutTips = getRealRender(
    renders.shortcutTips ?? true,
    DefaultShortcutTips,
  )

  const ModelSelect = getRealRender(
    renders.modelSelect ?? true,
    DefaultModelSelectWithChat,
  )

  const MobileConversationButton = getRealRender(
    renders.mobileConversationBtn ?? true,
    MobileConversationBtn,
  )

  const HeaderPrefix = renders.headerPrefix

  const Footer = renders.footer

  const [inputMode, setInputMode] = useState<MessageInputMode>('input')
  const textAreaRef = useRef<HTMLTextAreaElement>(null)
  const actionAreaRef = useRef<HTMLDivElement>(null)
  const selectionStartRef = useRef<number>()
  const canvasRef = useRef<HTMLCanvasElement>()
  const contextRef = useRef<CanvasRenderingContext2D>()

  const conversationListBtn = useMemo(() => {
    if (
      conversationListEnable &&
      MobileConversationButton &&
      layoutMode === 'mobile'
    ) {
      return <MobileConversationButton />
    }
    return null
  }, [conversationListEnable, MobileConversationButton, layoutMode])

  const handleChange = useMemoizedFn((content: string) => {
    onChange?.(content)
  })

  useEffect(() => {
    if (chatInputRef) {
      chatInputRef.current = textAreaRef.current
    }
  }, [])

  const handleSubmit = async () => {
    if (generating) {
      return
    }

    onSubmit?.({
      content: value,
      file,
      quote,
    })
    onChange?.('')
    onFileRemove?.()
  }

  const compositing = useRef(false)

  const adjustTextAreaScroll = (caretPosition: number) => {
    const textArea = textAreaRef.current
    if (!textArea) return

    // 获取当前行高
    const lineHeight = parseInt(getComputedStyle(textArea).lineHeight) || 24

    // 使用 requestAnimationFrame 在下一帧渲染前设置滚动位置
    requestAnimationFrame(() => {
      // 获取换行之后，准确的光标位置
      const coordinates = getCaretCoordinates(textArea, caretPosition)

      // 计算光标底部的位置
      const caretBottom = coordinates.top + coordinates.height

      // 计算文本区域可视范围的底部位置
      const visibleBottom = textArea.scrollTop + textArea.clientHeight

      /**
       * 只有当光标底部超出了可视范围时才调整滚动位置。
       * 当用户在输入内容的中间换行时，光标底部不会超出可视范围，不调整滚动位置
       * 有可能会出现等号的情况，所以必须 >=
       */
      if (caretBottom >= visibleBottom) {
        // 调整滚动位置，增加一行的高度
        textArea.scrollTop = caretBottom - textArea.clientHeight + lineHeight
      }
    })
  }

  const handleKeyDown: TextareaAutosizeProps['onKeyDown'] = event => {
    if (generating) {
      if (event.code === submitHotKey) {
        event.preventDefault()
      }
      return
    }
    if (event.code === submitHotKey) {
      event.preventDefault()
      // 防止用户自定义组合键换行冲突
      if (
        compositing.current ||
        event.altKey ||
        event.ctrlKey ||
        event.metaKey
      ) {
        return
      }
      // 追加回车
      if (event.shiftKey) {
        const { value, selectionStart, selectionEnd } =
          event.target as HTMLTextAreaElement
        const newValue = `${value.slice(0, selectionStart)}\n${value.slice(
          selectionEnd,
        )}`
        handleChange(newValue)
        selectionStartRef.current = selectionStart + 1

        // shift+enter之后，要调整滚动位置，避免光标位置超出可视范围
        adjustTextAreaScroll(selectionStart + 1)
        return
      }
      // 发送 prompt
      ;(value || file) && handleSubmit()
    }
  }

  const handlePaste: React.ClipboardEventHandler<
    HTMLTextAreaElement
  > = async event => {
    const items = event.clipboardData.items as any
    let fileFound = false
    for (const item of items) {
      if (item.kind === 'file') {
        const file = item.getAsFile()
        if (file) {
          fileFound = true
          await onMessageFileUpload?.({ file })
            .catch(e => {
              toast.error(e.message ?? '上传失败，请重试', {
                id: 'file-upload',
              })
            })
            .finally(() => {
              textAreaRef.current?.focus()
            })
          // 如果找到文件（图片或其他类型），处理上传
          break // 只处理第一个文件
        }
      }
    }
    if (fileFound) {
      // 如果找到文件，阻止默认粘贴行为
      event.preventDefault()
    }
  }

  useEffect(() => {
    // 处理换行后光标位置
    const cursor = selectionStartRef.current
    if (textAreaRef.current && value?.length && typeof cursor === 'number') {
      textAreaRef.current.selectionStart = cursor
      textAreaRef.current.selectionEnd = cursor
      selectionStartRef.current = undefined
    }
  }, [value])

  const handleComposition: React.CompositionEventHandler<
    HTMLTextAreaElement
  > = event => {
    // 输入中文时，敲下回车，不触发发送
    if (event.type === 'compositionstart') {
      compositing.current = true
      return
    }
    if (event.type === 'compositionend') {
      compositing.current = false
    }
  }

  const shortcutFormInterState = useShortcutsForm(shortcuts, shortcutPreview)

  const {
    shortcutsFormVisible,
    openShortcutsForm,
    hideShortcutsForm,
    currentShortcutsCardItem,
  } = shortcutFormState || shortcutFormInterState

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault()
    const items = e.dataTransfer.files as any
    for (const item of items) {
      if (item) {
        // 如果找到文件（图片或其他类型），处理上传
        await onMessageFileUpload?.({ file: item })
          .catch(e => {
            toast.error(e.message ?? '上传失败，请重试', {
              id: 'file-upload',
            })
          })
          .finally(() => {
            textAreaRef.current?.focus()
          })
        break // 只处理第一个文件
      }
    }
  }

  const showHotKeyTips = useMemo(() => {
    if (!ShortcutTips) {
      return false
    } else if (layoutMode === 'mobile' && (shortcuts.length || HeaderPrefix)) {
      return false
    } else {
      // 防止快捷方式超长，遮挡InputTips
      return shortcuts.length <= NO_TIPS_SHORTCUTS_LENGTH
    }
  }, [shortcuts, layoutMode, ShortcutTips])

  const onShortcutItemClick = useCallback((shortcut: Shortcut) => {
    // 点击快捷按钮后，不要 focus textarea 输入框
    setFocusTextArea(false)
    openShortcutsForm(shortcut)
  }, [])

  const onCloseForm = useCallback(() => {
    hideShortcutsForm()
    // 关闭 form时，自动聚焦到输入框
    setFocusTextArea(true)
  }, [hideShortcutsForm])

  // 添加一个计算宽度的函数
  const checkShouldBeMultiLine = useCallback((text: string) => {
    if (!textAreaRef.current) return false

    // 初始化 canvas 和 context（仅在第一次使用时创建）
    if (!canvasRef.current) {
      canvasRef.current = document.createElement('canvas')
      contextRef.current = canvasRef.current.getContext('2d')
    }

    const context = contextRef.current
    if (!context) return false

    // 获取输入框的字体样式
    const computedStyle = getComputedStyle(textAreaRef.current)
    context.font = `${computedStyle.fontSize} ${computedStyle.fontFamily}`

    // 测量文本宽度
    const textWidth = context.measureText(text).width

    // 获取容器总宽度
    const container = textAreaRef.current.closest('.chat-input-wrapper')
    const containerWidth = container?.clientWidth || 0

    const buttonAreaWidth = actionAreaRef.current?.clientWidth || 0

    // 可用的文本宽度
    const availableTextWidth = containerWidth - buttonAreaWidth - 32

    return textWidth > availableTextWidth
  }, [])

  return (
    <div
      onDrop={!shortcutsFormVisible ? handleDrop : undefined}
      className={cn('ui-relative group ui-w-full', props.className)}
      style={style}
    >
      {shortcutsFormVisible && currentShortcutsCardItem && (
        <ShortcutsFormModal
          icon={
            <AppLogo
              size={18}
              value={currentShortcutsCardItem?.icon}
              color={currentShortcutsCardItem?.color}
              fillSize={14}
              type='emoji'
              className='!ui-rounded-[4px]'
            />
          }
          title={currentShortcutsCardItem?.meta.short_name}
          onClose={onCloseForm}
        >
          <ShortcutsForm
            onFileUpload={onFileUpload}
            title={currentShortcutsCardItem.title}
            type={currentShortcutsCardItem?.type}
            item={currentShortcutsCardItem}
            autoFocus={!shortcutPreview}
            onSubmit={(value, shortcutFunctionId, toolArguments) => {
              onSubmit?.({
                content: value,
                shortcut: {
                  shortcutFunctionId,
                  toolArguments,
                },
              })
              onFileRemove?.()
              hideShortcutsForm()
            }}
            schema={currentShortcutsCardItem?.formConfig?.map(v => ({
              ...v,
              name: v.variableName,
            }))}
          />
        </ShortcutsFormModal>
      )}
      {!shortcutsFormVisible && (
        <>
          {(!!shortcuts.length || HeaderPrefix) && (
            <div
              className={cn(
                'ui-box-border ui-flex ui-items-center ui-mb-[8px] ui-pt-[8px] ui-gap-x-[8px]',
                {
                  'ui-ml-[68px]': clearConversationEnable,
                },
              )}
            >
              {!!shortcuts.length && conversationListBtn && (
                <div className='ui-ml-[6px] ui-h-32px ui-flex ui-items-center'>
                  {conversationListBtn}
                </div>
              )}
              {HeaderPrefix && <HeaderPrefix />}
              {!!shortcuts.length && (
                <ShortcutsPanel
                  className='ui-flex-1  ui-flex ui-flex-wrap ui-gap-[8px]'
                  shortcuts={shortcuts}
                  onItemClick={onShortcutItemClick}
                />
              )}
            </div>
          )}

          <div className='ui-flex ui-items-end  ui-gap-[12px] ui-relative'>
            {clearConversationEnable && (
              <ConversationClearBtn
                generating={generating}
                className='ui-pointer-events-none ui-rounded-[12px]  ui-box-border  ui-z-[1] ui-border-[#E1E1E5] ui-border ui-border-solid ui-border-opacity-60 ui-shadow-[0px_4px_20px_0px_rgba(0,0,0,0.06)]'
                onClick={() => !generating && onClearConversation?.()}
              />
            )}
            {showHotKeyTips && (
              <div className='ui-absolute ui-invisible ui-group-focus-within:ui-visible ui-right-0 ui-top-[-30px] ui-z-[10]'>
                <ShortcutTips />
              </div>
            )}

            {!shortcuts.length && !HeaderPrefix && conversationListBtn}

            <div
              className={cn(
                'ui-relative ui-p-[2px] chat-input-container ui-box-border ui-flex ui-justify-between ui-flex-1 ui-bg-white ui-rounded-[12px] ui-overflow-hidden',
                {
                  'ui-items-center': !file,
                  'ui-items-end': file,
                  'ui-border-[#E1E1E5] ui-border ui-border-solid ui-border-opacity-60 ui-shadow-[0px_4px_20px_0px_rgba(0,0,0,0.06)]':
                    inputMode === 'input',
                  'ui-border-transparent ui-border ui-border-solid':
                    inputMode === 'voice',
                },
              )}
            >
              <div
                className={cn({
                  'chat-input-background': inputMode === 'input',
                })}
              ></div>

              <div
                className={cn(
                  'chat-input-wrapper  ui-flex-col ui-relative  ui-z-[2] ui-min-h-[50px]  ui-w-full ui-flex ui-justify-between  ui-bg-white ui-rounded-[10px] ui-box-border',
                  {
                    'ui-px-[12px] ui-py-[9px] ui-pl-[16px]':
                      inputMode === 'input',
                  },
                )}
              >
                {file && (
                  <div className='ui-mb-[12px]'>
                    <InputFile
                      file={file}
                      onFileClick={onFilePreview}
                      onDelete={onFileRemove}
                    />
                  </div>
                )}
                {quote && (
                  <div className='ui-mb-[12px]'>
                    <MessageQuote
                      quote={quote}
                      className='flex-1'
                      showToolTips={false}
                      onClose={() => {
                        onQuoteRemove?.()
                        textAreaRef.current?.focus()
                      }}
                    />
                  </div>
                )}
                <div
                  className={cn('ui-flex-1 ui-flex', {
                    'ui-flex-col ui-gap-[8px]': inputMultiLine,
                  })}
                >
                  <div className='ui-flex-1 ui-flex ui-overflow-hidden ui-flex-col ui-justify-center'>
                    {inputMode === 'input' ? (
                      <>
                        <TextAreaAutoSize
                          autoFocus={focusTextArea}
                          onPaste={handlePaste}
                          wrap='soft'
                          ref={textAreaRef}
                          onKeyDown={handleKeyDown}
                          className='ui-block ui-min-h-[24px] ui-w-full ui-h-full ui-p-[0] placeholder:ui-align-middle ui-resize-none ui-border-0 ui-bg-white ui-text-[14px]/[24px] ui-outline-0 placeholder:ui-text-[#8d8d99] placeholder:ui-leading-[24px] ui-text-[#17171d] focus-visible:ui-outline-none disabled:placeholder:text-[#8d8d99]-40'
                          value={value}
                          placeholder={placeholder}
                          minRows={1}
                          maxRows={6}
                          onChange={event => {
                            const content = event.target.value
                            handleChange(content)
                            const shouldBeMultiLine =
                              content.includes('\n') ||
                              checkShouldBeMultiLine(content)

                            if (!inputMultiLine && shouldBeMultiLine) {
                              setInputMultiLine(true)
                            } else if (
                              inputMultiLine &&
                              !shouldBeMultiLine &&
                              !content.includes('\n')
                            ) {
                              setInputMultiLine(false)
                            }
                          }}
                          onHeightChange={(height, meta) => {
                            const rowHeight = meta.rowHeight
                            if (!inputMultiLine && height / rowHeight >= 2) {
                              setInputMultiLine(true)
                            }
                          }}
                          onCompositionStart={handleComposition}
                          onCompositionEnd={handleComposition}
                        />
                      </>
                    ) : (
                      <div className='ui-max-h-[85%] ui-w-full'>
                        <SpeechInput
                          config={asrConfig}
                          disabled={generating}
                          onOk={text => onSubmit?.({ content: text })}
                        />
                      </div>
                    )}
                    {Footer && <Footer />}
                  </div>

                  <div
                    className={cn('ui-flex ui-gap-[12px] ui-ml-[12px]', {
                      'ui-items-center': !file,
                      'ui-items-end': file,
                      'ui-ml-auto': inputMultiLine,
                    })}
                    ref={actionAreaRef}
                  >
                    {ModelSelect && <ModelSelect />}
                    {inputMode === 'input' && FileUpload && <FileUpload />}
                    <div className='ui-w-[32px] ui-h-[32px] ui-rounded-full  ui-flex-center ui-overflow-hidden'>
                      {!generating && (!!value || !voice) && (
                        <SendButton
                          disabled={(!value && !file) || generating || disabled}
                          onClick={handleSubmit}
                        />
                      )}
                      {!generating && voice && !value && (
                        <img
                          className='ui-w-24px ui-h-24px'
                          onClick={() =>
                            setInputMode(
                              inputMode === 'input' ? 'voice' : 'input',
                            )
                          }
                          src={
                            inputMode === 'input'
                              ? voiceAssets.voiceButton
                              : voiceAssets.keyboardButton
                          }
                        ></img>
                      )}

                      {generating && (
                        <Tooltip title='停止生成'>
                          <StopButton
                            className='ui-cursor-pointer'
                            onClick={() => onStopGenerate?.()}
                          />
                        </Tooltip>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  )
})
