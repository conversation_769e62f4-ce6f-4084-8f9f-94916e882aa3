import { memo, useMemo } from 'react'
import { ModelSelect } from '@bty/business-components'
import type { LLMChannels } from '@bty/global-types/model'
import { useUpdateEffect } from 'ahooks'
import { ChatbotRunType } from '@bty/global-types/conversation'
import { useChatCore, useChatStater } from '../../../../core-react'

export const ModelSelectWithChat = memo(() => {
  const chatCore = useChatCore()
  const agentLLMModel = useChatStater(chatCore.agent, 'agentLLMModel')
  const agentConfig = useChatStater(chatCore.agent, 'agentConfig')
  const conversationId = useChatStater(chatCore.conversation, 'conversationId')

  const conversationList = useChatStater(
    chatCore.conversation,
    'conversationData',
  )

  const currentConversationConfig = useMemo(() => {
    if (conversationList.list.length && conversationId) {
      return conversationList.list.find(
        item => item.conversation_id === conversationId,
      )
    }
    return null
  }, [conversationList, conversationId])

  useUpdateEffect(() => {
    if (
      currentConversationConfig &&
      agentConfig?.rule?.free_model &&
      !currentConversationConfig.config?.model_config
    ) {
      chatCore.conversation.updateConversationModelConfig(conversationId, {
        model: agentConfig.rule?.model,
        channel: agentConfig.rule?.channel,
      })
    }
  }, [currentConversationConfig, agentConfig])

  if (!agentConfig || !agentConfig.rule?.free_model || !conversationId) {
    return null
  }

  const { llmModelList = [], llmModelLimitedList = [] } = agentLLMModel || {}

  const onModelSelect = ({
    model,
    channel,
  }: {
    model: string
    channel: LLMChannels
  }) => {
    if (conversationId) {
      chatCore.conversation.updateConversationModelConfig(conversationId, {
        model,
        channel,
      })
    }
  }

  return (
    <div>
      <ModelSelect
        llmModelList={llmModelList}
        llmModelLimitedList={llmModelLimitedList}
        getLlmModelListLoading={false}
        getAsyncSupportModelListLoading={false}
        llmAsyncSupportModelLimitedList={[]}
        filterByMethodSupport={['tools', 'functions', 'normal']}
        placeholder='请选择'
        onChange={onModelSelect}
        value={currentConversationConfig?.config?.model_config}
        showPoints={
          currentConversationConfig?.run_type === ChatbotRunType.AGENT_TESTING
        }
      />
    </div>
  )
})
