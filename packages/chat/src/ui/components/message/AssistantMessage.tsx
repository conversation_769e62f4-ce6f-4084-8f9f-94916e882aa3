import { type FC, memo, useCallback, useMemo, useRef } from 'react'
import type { AvatarProps } from '@bty/global-types/avatar'
import { useMemoizedFn } from 'ahooks'
import { cn, getRealRender } from '@bty/util'
import { DislikePopover } from '@bty/business-components'
import type { MarkdownReference } from '@bty/components/markdown'
import type {
  AssistantMessage as IAssistantMessage,
  SuggestPlugin,
} from '../../../core'
import type { ToolBarProps } from '../../base/components/Tools'
import { ReactComponent as UnLikeIcon } from '../../assets/svg/unlike.svg'
import { CopilotToolsBar, ToolBar } from '../../base/components/Tools'
import { useChatEvent } from '../../hooks/useChatEvent'
import { Avatar } from '../avatar'
import { ReactComponent as RegenerateIcon } from '../../assets/svg/regenerate.svg'
import isSearch from '../../utils/isSearch'
import { MessageTime } from './MessageTime'
import { Watermark as DefaultWaterMark } from './Watermark'
import { MessageLoading as DefaultMessageLoading } from './MessageLoading'
import { TaskMessage } from './TaskMessage'
import { MarkdownMessage } from './MarkdownMessage'
import { ToolIcon } from './ToolIcon'
import { CopyButton } from './CopyButton'
import { RelatedQuestion } from './RelatedQuestion'
import { QuestionGuideList } from './QuestionGuideList'
import { MessageError } from './MessageError'
import type { ThinkingInfoProps } from './ThinkTask/ThinkingInfo'
import { ThinkTask } from './ThinkTask/ThinkingInfo'

export interface AssistantMessageProps {
  message: IAssistantMessage
  isLastMessage: boolean
  botAvatarInfo?: AvatarProps
  readonly?: boolean
  /**
   * @deprecated 过渡Props，Log重构后酌情删除
   */
  allowLogger?: boolean
  renders?: {
    watermark?: FC | boolean
    toolComponent?: FC<ToolBarProps> | boolean
    loading?: FC | boolean
    footer?: FC<{ message: IAssistantMessage }>
    suggestPlugin?: FC<{ plugins: SuggestPlugin[] }>
    messageTime?: FC<{ time: string; className?: string }> | boolean
    thinkTask?: FC<ThinkingInfoProps> | boolean
    modelComponent?: FC<{ model: string }>
  }
}

export const AssistantMessage = memo<AssistantMessageProps>(props => {
  const {
    message,
    botAvatarInfo,
    readonly,
    isLastMessage,
    allowLogger = false,
    renders = {},
  } = props
  const messageContainerRef = useRef<HTMLDivElement>(null)
  const {
    onHrefClick,
    onTaskClick,
    onMessageSubmit,
    onMessageUpdate,
    onRegenerate,
    onRelateQuestionToggle,
    onToolClick,
    onCopy,
  } = useChatEvent()

  console.log('====== message.content', message.content)

  const Watermark = getRealRender(renders?.watermark ?? true, DefaultWaterMark)
  const ToolComponent = getRealRender(renders?.toolComponent ?? true, ToolBar)
  const MessageLoading = getRealRender(
    renders?.loading ?? true,
    DefaultMessageLoading,
  )
  const Footer = getRealRender(renders.footer ?? true, MessageError)
  const MessageTimeComp = getRealRender(
    renders.messageTime ?? true,
    MessageTime,
  )
  const Think = getRealRender(renders.thinkTask ?? true, ThinkTask)
  const SuggestPlugin = renders.suggestPlugin ?? false
  const ModelComponent = renders.modelComponent ?? false

  const needRenderWatermark = useMemo(() => {
    return (
      message.from === 'CHAT' &&
      !message.isStreaming &&
      !message.isFetching &&
      message.success
    )
  }, [Watermark, message])

  const tools = [...message.skills, message.knowledge].filter(v => !!v)
  // const hasLoadingTool = tools.some(item => item.status === 'PENDING')

  const showRegenerate = useMemo(() => {
    return (
      isLastMessage &&
      !readonly &&
      message.role === 'ASSISTANT' &&
      message.from === 'CHAT'
    )
  }, [message, isLastMessage, readonly])

  const references = useMemo(() => {
    const searchTool = (message.skills ?? []).find(item => {
      return isSearch(item.function_code)
    })

    return searchTool?.output ?? []
  }, [message.skills])

  const getMarkdownElement = useMemoizedFn(() => {
    return messageContainerRef.current?.querySelector<HTMLDivElement>(
      '.chat-markdown-message',
    )
  })

  const handleRegenerate = useCallback(() => {
    onRegenerate(message)
  }, [message])

  const handleRelateQuestionToggle = useCallback(
    (enable: boolean) => {
      onRelateQuestionToggle(message, enable)
    },
    [message],
  )

  const handleDislikeFinish = useMemoizedFn((id: string) => {
    onMessageUpdate(message.id, {
      ...message,
      feedbackId: id,
    })
  })

  const onReferenceClick = useMemoizedFn((item: MarkdownReference) => {
    onHrefClick(item.url, true)
  })

  return (
    <div
      ref={messageContainerRef}
      className='chat-message-container ui-flex ui-items-start ui-relative ui-w-[calc(100%-8px)]'
    >
      <div className='ui-relative ui-w-full group'>
        {/* header */}
        <div className='chat-message-header ui-flex ui-items-center ui-w-full ui-mb-[6px] ui-relative ui-gap-[6px]'>
          {botAvatarInfo && (
            <div className='chat-message-avatar ui-flex-center ui-gap-[6px]'>
              <Avatar
                size={22}
                className='ui-shrink-0'
                {...botAvatarInfo}
                name={''}
              />
              <div className='ui-text-[#8D8D99] ui-text-[14px]/[28px] flex'>
                {botAvatarInfo?.name}
              </div>
              {ModelComponent && message.llmModel && (
                <ModelComponent model={message.llmModel} />
              )}
            </div>
          )}
          {MessageTimeComp && (
            <MessageTimeComp
              time={message.create_time}
              className='ui-text-right ui-right-0 ui-opacity-0 group-hover:ui-opacity-100'
            />
          )}
        </div>
        {/* body */}
        <div className='chat-message-content ui-relative ui-overflow-hidden ui-flex message-container'>
          <div className='ui-relative ui-max-w-full'>
            <div
              className={cn(
                '__message-item bot-message',
                'ui-relative ui-overflow-hidden ui-py-[5px] ui-text-[14px] ui-text-yeah ui-box-border !ui-px-0 !ui-py-0',
                {
                  'ui-pb-[6px]': showRegenerate || needRenderWatermark,
                  'result-streaming':
                    message.isStreaming &&
                    !message.content &&
                    message.think?.status !== 'PENDING',
                },
                message.isFetching
                  ? 'ui-flex-center ui-p-[7px]'
                  : 'ui-px-[12px]',
              )}
            >
              <div>
                {ToolComponent &&
                  tools.map((v, i) => {
                    return (
                      <div className='ui-mt-[6px] ui-mb-[4px]' key={i}>
                        <ToolComponent
                          tool={v}
                          readonly={readonly}
                          onHrefClick={onHrefClick}
                          allowLogger={allowLogger}
                          onToolClick={onToolClick}
                        />
                      </div>
                    )
                  })}
                {message.think && (
                  <div className='ui-mt-[6px] ui-mb-[4px]'>
                    <Think thinkMessage={message.think} />
                  </div>
                )}
                {Boolean(message.groupProcessList?.length) && (
                  <CopilotToolsBar
                    list={message.groupProcessList}
                  ></CopilotToolsBar>
                )}
              </div>
              {message.task && (
                <TaskMessage
                  task={message.task}
                  toDetail={() => onTaskClick?.(message.task)}
                />
              )}
              {message.isFetching && <MessageLoading />}
              <MarkdownMessage
                content={message.content}
                onHrefClick={onHrefClick}
                renderHtml={message.groupProcessList?.length > 0}
                references={references}
                onReferenceClick={onReferenceClick}
                codeLoading={
                  message.isStreaming && message.think?.status !== 'PENDING'
                }
              />
              {!!message.suggestPlugin?.length && SuggestPlugin && (
                <SuggestPlugin plugins={message.suggestPlugin} />
              )}
              {message.finishReason === 'abort' && (
                <div className='ui-text-[12px]/[12px] ui-text-[#8D8D99] ui-font-[400] ui-italic ui-mt-[8px]'>
                  （用户手动停止）
                </div>
              )}
              {/* 下面的逻辑服务端在2023年1月31日已经去掉了, 只有历史消息才会有 */}
              {/* 原本的逻辑是添加插件后，推送一条“我学会了xxx技能， 你可以问我xxxx */}
              {!readonly && message.tips && (
                <QuestionGuideList
                  questions={message.tips}
                  onQuestionClick={data => {
                    onMessageSubmit({ content: data })
                  }}
                />
              )}
              {Footer && (
                <div className='ui-mt-[7px] ui-text-[12px] ui-text-yeah-light/60 ui-min-w-[200px]  ui-max-w-[708px] ui-break-words'>
                  <Footer message={message} />
                </div>
              )}
              {!message.isFetching && !message.isStreaming && (
                <div
                  className={cn(
                    'ui-flex ui-w-full ui-mt-[8px] ui-items-center ui-gap-[10.5px]',
                    {
                      'ui-flex': showRegenerate || needRenderWatermark,
                      // 'ui-hidden!': message.isFetching || message.isStreaming,
                    },
                  )}
                >
                  <div
                    className={cn(
                      'ui-items-center',
                      isLastMessage
                        ? 'ui-flex'
                        : 'ui-hidden group-hover:ui-flex',
                    )}
                  >
                    {showRegenerate && (
                      <ToolIcon
                        IconSVG={RegenerateIcon}
                        tooltip='重新生成'
                        onClick={handleRegenerate}
                      />
                    )}
                    <CopyButton
                      onCopy={onCopy}
                      getTargetHTMLElement={getMarkdownElement}
                      content={message.content}
                    />
                    {!readonly && !!message.recordId && (
                      <DislikePopover
                        record_id={message.recordId}
                        feedback_id={message.feedbackId}
                        onFinish={handleDislikeFinish}
                      >
                        <ToolIcon
                          IconSVG={UnLikeIcon}
                          tooltip='不满意'
                          active={!!message.feedbackId}
                        />
                      </DislikePopover>
                    )}
                  </div>
                  {Watermark && !message.isFetching && !message.isStreaming && (
                    <div
                      className={cn(
                        'ui-text-[#8d8d99] ui-text-[9px]/[24px] ui-h-[24px] ui-ml-auto',
                        {
                          'ui-opacity-0': !needRenderWatermark,
                          'ui-opacity-100': needRenderWatermark,
                        },
                      )}
                    >
                      <Watermark />
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
        {/* footer */}
        {!readonly &&
          isLastMessage &&
          message.relatedQuestion &&
          message.relatedQuestion.status !== 'FAILED' && (
            <div className='ui-mt-[16px] ui-pr-[28px]'>
              <RelatedQuestion
                loading={message.relatedQuestion.status === 'PENDING'}
                questions={message.relatedQuestion.content}
                onSend={q => onMessageSubmit?.({ content: q })}
                onToggle={enable => handleRelateQuestionToggle(enable)}
              />
            </div>
          )}
      </div>
    </div>
  )
})

AssistantMessage.displayName = 'AssistantMessage'
