import { memo } from 'react'
import { Markdown } from '@bty/components'
import { ReactComponent as ArrowIcon } from '../../assets/svg/right-arrow.svg'

export interface QuestionGuideListProps {
  questions: string[]
  onQuestionClick: (data: string) => void
}

export const QuestionGuideList = memo<QuestionGuideListProps>(props => {
  const { questions, onQuestionClick } = props

  return (
    <div>
      {questions.map((question, i) => (
        <div
          key={i}
          className='ui-mt-[8px]  ui-max-w-[100%] ui-min-w-[302px] ui-w-fit ui-py-[4px] ui-px-[12px] ui-flex ui-items-center ui-justify-between ui-leading-[14px] ui-rounded-[8px] ui-border-[1px] ui-border-solid ui-border-[#e1e1e5] ui-border-opacity-80 ui-text-[12px] ui-text-[#17171d] hover:ui-bg-[#626999]/[0.08] ui-cursor-pointer'
          onClick={() => onQuestionClick(question)}
        >
          <Markdown content={question} imagePreviewable={false} />
          <ArrowIcon className='ui-ml-[28px] ui-shrink-0 ui-text-primary' />
        </div>
      ))}
    </div>
  )
})
