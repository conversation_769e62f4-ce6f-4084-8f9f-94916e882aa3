import { memo, useEffect, useRef } from 'react'
import { cn } from '@bty/util'
import Scrollbar from 'smooth-scrollbar'
import { useChatLayoutMode } from '@bty/hooks'
import { Markdown } from '@bty/components'
import { ReactComponent as ArrowIcon } from '../assets/svg/right-arrow.svg'

export interface WelcomeProps {
  avatar: string
  title: string
  welcomeMessage?: string
  quickQuestions: string[]
  onQuickItemClick?: (content: string) => void
}

const columnsCount = 2

export const Welcome = memo<WelcomeProps>(props => {
  const { avatar, title, welcomeMessage, quickQuestions, onQuickItemClick } =
    props

  const scrollRef = useRef<HTMLDivElement>(null)
  const { layoutMode } = useChatLayoutMode()

  useEffect(() => {
    Scrollbar.init(scrollRef.current, {
      damping: 0.5,
    })
  }, [])

  return (
    <div
      className='ui-w-full ui-pt-[90px] ui-h-full ui-of-scroll ui-text-[14px]'
      ref={scrollRef}
    >
      <div className='ui-max-w-[900px] ui-px-[46px] ui-mx-auto ui-text-center'>
        <img
          src={avatar}
          alt=''
          className='ui-w-[100px] ui-h-[100px] ui-rounded-full ui-inline-block'
        />
        <h3 className='ui-text-[18px] ui-font-600 ui-mt-[16px]'>{title}</h3>
        {welcomeMessage?.trim() && (
          <div className='ui-flex-center ui-mt-[16px]'>
            <Markdown
              overrides={{ root: 'ui-w-fit !ui-text-left' }}
              content={welcomeMessage}
              imagePreviewable={false}
            />
          </div>
        )}
        <div className='ui-flex ui-gap-[8px] ui-mt-[16px] ui-w-full ui-pb-[20px] ui-justify-center ui-flex-col'>
          {quickQuestions.length <= 5 || layoutMode === 'mobile' ? (
            quickQuestions.map((item, index) => {
              return (
                <QuestionItem
                  key={`agent-quick-question-${index}-${item}`}
                  onClick={() => onQuickItemClick?.(item)}
                  content={item}
                />
              )
            })
          ) : (
            // 分栏瀑布流
            <div className='ui-flex ui-gap-[8px] ui-w-full ui-justify-center'>
              {new Array(columnsCount).fill(null).map((_, columnIndex) => {
                const columns = quickQuestions.filter(
                  (_, questionIndex) =>
                    questionIndex % columnsCount === columnIndex,
                )
                return (
                  <div
                    className='ui-flex ui-flex-col ui-gap-[8px] flex-1'
                    key={`question-row-${columnIndex}`}
                  >
                    {columns.map((item, index) => (
                      <QuestionItem
                        key={`agent-quick-question-${columnIndex}-${index}-${item}`}
                        onClick={() => onQuickItemClick?.(item)}
                        content={item}
                      />
                    ))}
                  </div>
                )
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  )
})

function QuestionItem(props: {
  content: string
  className?: string
  onClick?: () => void
}) {
  return (
    <div
      onClick={props.onClick}
      className={cn(
        'ui-text-left of-hidden flex-wrap ui-flex ui-items-center ui-px-[12px] ui-py-[13.5px] ui-bg-[#fff]',
        '[&_img]:ui-block [&_img]:ui-h-[80px] [&_.rc-image-wrap]:ui-block [&_img]:ui-w-auto ',
        'ui-border-[#e7e7ea] ui-border-[1px] ui-rounded-[12px] ui-border-solid',
        'ui-cursor-pointer ui-box-border ui-leading-[21px] [&_*]:ui-leading-[21px] hover:ui-bg-[rgba(98,105,153,0.08)]',
        props.className,
      )}
    >
      <Markdown content={props.content} imagePreviewable={false} />
      <ArrowIcon className='ui-ml-[28px] ui-shrink-0 ui-text-yeah-primary' />
    </div>
  )
}
