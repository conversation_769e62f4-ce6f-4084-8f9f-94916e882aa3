import './index.css'

export type {
  MessageInputProps,
  InputFileUploadProps,
  ShortcutTipsProps,
} from './components/messageInput'

export {
  MessageInput,
  InputFileUpload,
  FileIconButton,
  ShortcutTips,
  FileUpload,
  shortcutsFormat,
  useShortcutsForm,
  SendButton,
  InputFile,
  InputFileUploadWithChat,
} from './components/messageInput'
export { isMacOrIOS } from './components/messageInput/util'
export {
  MessageList,
  ScrollContainer,
  ScrollbarContext,
  ToBottomBtn,
} from './components/messageList'
export type { MessageListProps } from './components/messageList'
export { ToolBar } from './base/components/Tools'
export type { ToolBarProps } from './base/components/Tools'
export { BotList, useBotList } from './bot-list'
export type { BotListProps } from './bot-list'
export type { AgentProfileProps } from './agent-profile/AgentProfile'
export { ChatLayoutProvider } from '@bty/hooks'
export { ChatEventProvider, eventMerge } from './provider/ChatEventProvider'
export {
  ChatTaskIcon,
  ChatDetailIcon,
  ChatMultiIcon,
  ChatHeader,
} from './header'
export type { ChatHeaderProps } from './header'
export { Chat } from './chat'
export type { ChatProps } from './chat'
export type {
  AssistantMessageProps,
  UserMessageProps,
} from './components/message'
export {
  AssistantMessage,
  getFormItemComponentMap,
  UserMessage,
  MessageError,
  ThinkTask,
} from './components/message'
export { AgentProfile } from './agent-profile'
export { useChatEvent } from './hooks/useChatEvent'
export { ConversationItem } from './conversation-list/ConversationItem'
export type { ChatEvents } from './provider/ChatEventProvider'
export { Toaster } from 'react-hot-toast'
export type { Env } from '@bty/chat-fetch'
export { PoweredBy } from './powered-by'
export { RelatedQuestion } from './components/message/RelatedQuestion'
export { FileThumb } from './base/components/File'

export type { CopyPayload } from '@bty/util'
export { copyImage, copyVideo } from '@bty/util'
export { MultiAgent } from './multi-agent/MultiAgent'
