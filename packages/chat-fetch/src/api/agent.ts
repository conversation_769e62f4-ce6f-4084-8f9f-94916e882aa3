import type {
  AgentDetailResponse,
  UploadFileSupportTypes,
} from '@bty/global-types/agent'
import type {
  ExtendedModelType,
  LLMModalItem,
  LLMModelLimitedItem,
} from '@bty/global-types/model'
import type { VersionInfo } from '@bty/global-types/version'
import type { ApiResponse } from '../base/response'
import type { Env } from '../types'
import type { InnerApiConfig, OpenApiConfig } from '../base/createFetch'
import { Api } from '../base/createFetch'
import { Inner, Property } from '../base/decorator'

interface IPluginItem {
  function_id: string
  name: string
  description: string
  display_name: string
  icon: string
  color: string
  package_name: string
  output: {
    comment: string
    ip_label: string
  }[]
  form_config: {
    description: string
    example: unknown
    type: string
    /**
     * 是否选择模型
     */
    llm_model?: boolean
    label: string
    required: boolean
    placeholder: string
    variableName: string
    value?: string
    variableType: string
    layout?: 'normal' | 'inline'
    tooltip?: string
  }[]
  action_type: 'INTEGRATION'
  type: 'PLUGIN'
  plugin_label: string
}

@Inner
export class AgentOpenApi extends Api<OpenApiConfig> {
  @Property
  env?: Env

  @Property
  workspaceId?: string

  @Property
  authorization = ''

  @Property
  apiPrefix = '/v1'

  async queryAgentById(id: string) {
    const res = await this.request.post<ApiResponse<AgentDetailResponse>>(
      'agent/detail',
      {
        flow_id: id,
      },
    )
    return res
  }

  async getPluginList() {
    const res = await this.request.get<ApiResponse<IPluginItem[]>>(
      '/flow/plugin_template',
    )
    return res
  }

  getFlowById(flowId: string) {
    return this.request.get<ApiResponse<any>>(
      `/flow/${flowId}`,
      {},
      {
        headers: { 'Application-Id': flowId },
      },
    )
  }

  getUploadFileSupportFileTypes(agentId: string) {
    return this.request.post<ApiResponse<UploadFileSupportTypes[]>>(
      '/chat/agent/file-support',
      {
        agent_id: agentId,
      },
    )
  }

  getLLMModelList(
    generate_type: 'textGenerate',
    extended_model_type?: ExtendedModelType,
  ) {
    return this.request.get<ApiResponse<LLMModalItem[]>>(
      '/llm/active_channels',
      {
        generate_type,
        extended_model_type,
      },
    )
  }

  getLLMModelListLimited(workspaceId: string) {
    return this.request.get<ApiResponse<LLMModelLimitedItem[]>>(
      `/workspace/model_limited?workspace_id=${workspaceId}`,
    )
  }

  getAgentCreatorVersionInfoByWorkspaceId(workspaceId: string) {
    return this.request.get<ApiResponse<VersionInfo>>(
      `/product_service/${workspaceId}`,
    )
  }
}

@Inner
export class AgentInnerApi extends Api<InnerApiConfig> {
  @Property
  env?: Env

  @Property
  workspaceId?: string

  @Property
  authorization?: string

  async queryAgentById(id: string) {
    const res = await this.request.post<ApiResponse<AgentDetailResponse>>(
      '/agent/detail',
      { flow_id: id },
    )
    return res
  }

  async getPluginList() {
    const res = await this.request.get<ApiResponse<IPluginItem[]>>(
      '/flow/plugin_template',
      {},
    )
    return res
  }

  getFlowById(flowId: string) {
    return this.request.get<ApiResponse<any>>(
      `/flow/${flowId}`,
      {},
      {
        headers: { 'Application-Id': flowId },
      },
    )
  }

  getUploadFileSupportFileTypes(agentId: string) {
    return this.request.post<ApiResponse<UploadFileSupportTypes[]>>(
      '/chat/agent/file-support',
      {
        agent_id: agentId,
      },
    )
  }

  getLLMModelList(
    generate_type: 'textGenerate',
    extended_model_type?: ExtendedModelType,
  ) {
    return this.request.get<ApiResponse<LLMModalItem[]>>(
      '/llm/active_channels',
      {
        generate_type,
        extended_model_type,
      },
    )
  }

  getLLMModelListLimited(workspaceId: string) {
    return this.request.get<ApiResponse<LLMModelLimitedItem[]>>(
      `/workspace/model_limited?workspace_id=${workspaceId}`,
    )
  }

  getAgentCreatorVersionInfoByWorkspaceId(workspaceId: string) {
    return this.request.get<ApiResponse<VersionInfo>>(
      `/product_service/${workspaceId}`,
    )
  }
}
