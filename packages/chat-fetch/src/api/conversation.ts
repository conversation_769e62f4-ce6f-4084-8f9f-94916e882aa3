import type {
  ConversationData,
  GenerateTitleParams,
  CreateConversationPayload,
} from '@bty/global-types/conversation'
import type { ApiResponse } from '../base/response'
import type { Agent, ChatUser, ClientType, Env } from '../types'
import type { InnerApiConfig, OpenApiConfig } from '../base/createFetch'
import { Api } from '../base/createFetch'
import { Inner, Open, Property } from '../base/decorator'
import { DEFAULT_CONVERSATION_PAGE_SIZE_LIMIT } from '../const'

export interface CreateConversationResponse {
  title: string
  robot_id: string
  session_id: string
  conversation_id: string
  update_time: string
}

@Open
export class ConversationOpenApi extends Api<
  OpenApiConfig<{ agent?: Agent; user?: ChatUser; clientType?: ClientType }>
> {
  @Property
  env?: Env

  @Property
  accessKey?: string

  @Property
  workspaceId?: string

  @Property
  clientType?: ClientType

  @Property
  agent?: Agent

  @Property
  user?: Chat<PERSON><PERSON>

  async create(payload: CreateConversationPayload) {
    const params = {
      robot_id: this.agent?.id,
      title: payload.title,
      user: this.user?.name || this.user?.id,
      biz_key: this.user?.id || this.user?.name,
      client_type: this.clientType,
      conversation_config: payload.conversation_config,
    }

    const res = await this.request.post<
      ApiResponse<CreateConversationResponse>
    >('/chat/conversation/create', params)

    return res
  }

  update(
    payload: Pick<ConversationData, 'conversation_id'> &
      Partial<Pick<ConversationData, 'title' | 'config'>>,
  ) {
    return this.request.put<ApiResponse<void>>('/chat/conversation', payload)
  }

  delete(id: string) {
    return this.request.delete<ApiResponse<void>>(
      `/chat/conversation?conversation_id=${id}`,
    )
  }

  async list(
    run_type?: string | string[],
    pageInfo?: { pageNo?: number; pageSize?: number },
  ) {
    const userKey = this.user?.id || this.user?.name
    const { pageNo = 1, pageSize = DEFAULT_CONVERSATION_PAGE_SIZE_LIMIT } =
      pageInfo || {}
    const res = await this.request.post<ApiResponse<ConversationData[]>>(
      '/chat/conversation/list',
      {
        robot_id: this.agent?.id,
        run_type: Array.isArray(run_type) ? run_type.join(',') : run_type,
        biz_key: userKey,
        limit: pageSize,
        pageNo,
        pageSize,
      },
    )

    const formattedData = {
      ...res,
      data: {
        data: res?.data,
        total: res?.data?.length,
        page_size: pageSize,
        page_no: pageNo,
      },
    }
    return formattedData
  }

  async currentSession(conversationId: string) {
    const res = await this.request.get<ApiResponse<string>>(
      '/chat/session/current',
      { conversation_id: conversationId },
    )
    return res
  }

  async getVariables(conversationId: string, session_id: string) {
    const res = await this.request.get<ApiResponse<Record<string, string>>>(
      `/agent_variables/${conversationId}/${session_id}`,
    )
    return res
  }

  setVariables(
    conversationId: string,
    session_id: string,
    variables: Record<string, string | string[]>,
  ) {
    return this.request.put<ApiResponse<Record<string, string>>>(
      '/agent_variables',
      {
        agent_id: this.agent?.id,
        session_id,
        conversation_id: conversationId,
        agent_variables: variables,
      },
    )
  }
}

@Inner
export class ConversationInnerApi extends Api<
  InnerApiConfig<{ robotId?: string; clientType?: ClientType }>
> {
  @Property
  env?: Env

  @Property
  authorization?: string

  @Property
  workspaceId?: string

  @Property
  robotId?: string

  @Property
  clientType?: ClientType

  async create(payload: CreateConversationPayload) {
    const params = {
      robot_id: this.robotId,
      title: payload.title,
      run_type: payload.runType,
      client_type: this.clientType,
      conversation_config: payload.conversation_config,
    }

    const res = await this.request.post<
      ApiResponse<CreateConversationResponse>
    >('/chat/conversation/create', params)

    return res
  }

  update(
    payload: Pick<ConversationData, 'conversation_id'> &
      Partial<Pick<ConversationData, 'title' | 'config'>>,
  ) {
    return this.request.put<ApiResponse<void>>('/chat/conversation', payload)
  }

  async list(
    run_type?: string | string[],
    pageInfo?: { pageNo?: number; pageSize?: number },
  ) {
    const { pageNo = 1, pageSize = DEFAULT_CONVERSATION_PAGE_SIZE_LIMIT } =
      pageInfo || {}

    const res = await this.request.get<
      ApiResponse<{
        data: ConversationData[]
        total: number
        page_size: number
        page_no: number
      }>
    >('/chat/conversation/list', {
      robot_id: this.robotId,
      run_type: Array.isArray(run_type) ? run_type.join(',') : run_type,
      pageNo,
      pageSize,
    })
    return res
  }

  async currentSession(conversationId: string) {
    const res = await this.request.get<ApiResponse<string>>(
      '/chat/session/current',
      { conversation_id: conversationId },
    )
    return res
  }

  delete(conversationId: string) {
    return this.request.delete<ApiResponse<void>>(
      `/chat/conversation?conversation_id=${conversationId}`,
    )
  }

  async generateTitle(data: GenerateTitleParams) {
    const res = await this.request.post<ApiResponse<{ title: string }>>(
      '/chat/get_title',
      data,
    )
    return res
  }

  async getVariables(conversationId: string, session_id: string) {
    const res = await this.request.get<ApiResponse<Record<string, string>>>(
      `/agent/agent_variables/${conversationId}/${session_id}`,
    )
    return res
  }

  setVariables(
    conversationId: string,
    session_id: string,
    variables: Record<string, string | string[]>,
  ) {
    return this.request.put<ApiResponse<Record<string, string>>>(
      '/agent/agent_variables',
      {
        agent_id: this.robotId,
        session_id,
        conversation_id: conversationId,
        agent_variables: variables,
      },
    )
  }

  toggleRelatedQuestionsGenerate(
    conversation_id: string,
    session_id: string,
    session_related_questions_enabled: boolean,
  ) {
    return this.request.post<ApiResponse<void>>(
      '/agent/session_related_questions',
      {
        conversation_id,
        session_id,
        session_related_questions_enabled,
      },
    )
  }
}
